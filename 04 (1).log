[2025-06-04T16:06:20+08:00][sql] CONNECT:[ UseTime:0.001065s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:20+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000969s ]
[2025-06-04T16:06:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000637s ]
[2025-06-04T16:06:20+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000556s ]
[2025-06-04T16:06:20+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.001265s ]
[2025-06-04T16:06:20+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000479s ]
[2025-06-04T16:06:20+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000763s ]
[2025-06-04T16:06:20+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000477s ]
[2025-06-04T16:06:20+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000411s ]
[2025-06-04T16:06:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000434s ]
[2025-06-04T16:06:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000472s ]
[2025-06-04T16:06:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000410s ]
[2025-06-04T16:06:31+08:00][sql] CONNECT:[ UseTime:0.001062s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:31+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000821s ]
[2025-06-04T16:06:31+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000587s ]
[2025-06-04T16:06:31+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000535s ]
[2025-06-04T16:06:31+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.004446s ]
[2025-06-04T16:06:31+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000394s ]
[2025-06-04T16:06:31+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000730s ]
[2025-06-04T16:06:31+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000349s ]
[2025-06-04T16:06:31+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000279s ]
[2025-06-04T16:06:31+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000271s ]
[2025-06-04T16:06:31+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000271s ]
[2025-06-04T16:06:31+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000324s ]
[2025-06-04T16:06:32+08:00][sql] CONNECT:[ UseTime:0.000968s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:32+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000781s ]
[2025-06-04T16:06:32+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000436s ]
[2025-06-04T16:06:32+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000397s ]
[2025-06-04T16:06:32+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000711s ]
[2025-06-04T16:06:32+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000409s ]
[2025-06-04T16:06:32+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000725s ]
[2025-06-04T16:06:32+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000354s ]
[2025-06-04T16:06:32+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000354s ]
[2025-06-04T16:06:32+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-06-04T16:06:32+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-06-04T16:06:32+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-06-04T16:06:35+08:00][sql] CONNECT:[ UseTime:0.002611s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_square` [ RunTime:0.000626s ]
[2025-06-04T16:06:35+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_square` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` INNER JOIN `cm_user` `U` ON `U`.`id`=`KRS`.`user_id` WHERE (  `KRS`.`is_show` = '1' ) AND `KRS`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000743s ]
[2025-06-04T16:06:35+08:00][sql] SELECT KRS.id,KRS.robot_id,KR.name,U.nickname as author,KR.image,KR.intro,KR.is_enable,KRS.create_time FROM `cm_kb_robot_square` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` INNER JOIN `cm_user` `U` ON `U`.`id`=`KRS`.`user_id` WHERE (  `KRS`.`is_show` = '1' ) AND `KRS`.`delete_time` IS NULL ORDER BY `KRS`.`sort` DESC,`KRS`.`id` DESC LIMIT 0,15 [ RunTime:0.000374s ]
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_config` [ RunTime:0.000554s ]
[2025-06-04T16:06:35+08:00][sql] SELECT `value` FROM `cm_config` WHERE  `type` = 'robot_award'  AND `name` = 'is_show_user' LIMIT 1 [ RunTime:0.000277s ]
[2025-06-04T16:06:35+08:00][sql] CONNECT:[ UseTime:0.012586s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_decorate_page` [ RunTime:0.010449s ]
[2025-06-04T16:06:35+08:00][sql] SELECT `type`,`name`,`data` FROM `cm_decorate_page` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.017217s ]
[2025-06-04T16:06:35+08:00][sql] CONNECT:[ UseTime:0.003971s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000791s ]
[2025-06-04T16:06:35+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000490s ]
[2025-06-04T16:06:35+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000422s ]
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000686s ]
[2025-06-04T16:06:35+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000998s ]
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000611s ]
[2025-06-04T16:06:35+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000257s ]
[2025-06-04T16:06:35+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000239s ]
[2025-06-04T16:06:35+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000325s ]
[2025-06-04T16:06:35+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000310s ]
[2025-06-04T16:06:35+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000271s ]
[2025-06-04T16:06:35+08:00][sql] CONNECT:[ UseTime:0.019827s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_category` [ RunTime:0.006201s ]
[2025-06-04T16:06:35+08:00][sql] SELECT id,name,image FROM `cm_kb_robot_category` WHERE (  `is_enable` = 1 ) AND `cm_kb_robot_category`.`delete_time` IS NULL ORDER BY `sort` DESC,`id` DESC [ RunTime:0.005396s ]
[2025-06-04T16:06:35+08:00][sql] CONNECT:[ UseTime:0.021104s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_decorate_page` [ RunTime:0.004360s ]
[2025-06-04T16:06:35+08:00][sql] SELECT `type`,`name`,`data` FROM `cm_decorate_page` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000398s ]
[2025-06-04T16:06:35+08:00][sql] CONNECT:[ UseTime:0.000748s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_square` [ RunTime:0.000660s ]
[2025-06-04T16:06:35+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_square` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` INNER JOIN `cm_user` `U` ON `U`.`id`=`KRS`.`user_id` WHERE (  `KRS`.`is_show` = '1' ) AND `KRS`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000484s ]
[2025-06-04T16:06:35+08:00][sql] SELECT KRS.id,KRS.robot_id,KR.name,U.nickname as author,KR.image,KR.intro,KR.is_enable,KRS.create_time FROM `cm_kb_robot_square` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` INNER JOIN `cm_user` `U` ON `U`.`id`=`KRS`.`user_id` WHERE (  `KRS`.`is_show` = '1' ) AND `KRS`.`delete_time` IS NULL ORDER BY `KRS`.`sort` DESC,`KRS`.`id` DESC LIMIT 0,15 [ RunTime:0.000393s ]
[2025-06-04T16:06:35+08:00][sql] SHOW FULL COLUMNS FROM `cm_config` [ RunTime:0.000545s ]
[2025-06-04T16:06:35+08:00][sql] SELECT `value` FROM `cm_config` WHERE  `type` = 'robot_award'  AND `name` = 'is_show_user' LIMIT 1 [ RunTime:0.000353s ]
[2025-06-04T16:06:36+08:00][sql] CONNECT:[ UseTime:0.000996s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:36+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_session` [ RunTime:0.000782s ]
[2025-06-04T16:06:36+08:00][sql] SELECT * FROM `cm_kb_robot_session` WHERE (  `user_id` = 2  AND `square_id` = 4 ) AND `cm_kb_robot_session`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000594s ]
[2025-06-04T16:06:36+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_square` [ RunTime:0.000589s ]
[2025-06-04T16:06:36+08:00][sql] SELECT * FROM `cm_kb_robot_square` WHERE (  `id` = 4 ) AND `cm_kb_robot_square`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000333s ]
[2025-06-04T16:06:36+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000617s ]
[2025-06-04T16:06:36+08:00][sql] SELECT id,name,is_public FROM `cm_kb_robot` WHERE (  `id` = 2 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000380s ]
[2025-06-04T16:06:40+08:00][sql] CONNECT:[ UseTime:0.002115s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_session` [ RunTime:0.000668s ]
[2025-06-04T16:06:40+08:00][sql] SELECT * FROM `cm_kb_robot_session` WHERE (  `user_id` = 2  AND `square_id` = 3 ) AND `cm_kb_robot_session`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000455s ]
[2025-06-04T16:06:40+08:00][sql] CONNECT:[ UseTime:0.000847s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000807s ]
[2025-06-04T16:06:40+08:00][sql] SELECT id,code,is_show_context,is_show_quote FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 1 [ RunTime:0.000381s ]
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_record` [ RunTime:0.000673s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_record` WHERE (  `robot_id` = 4  AND `user_id` = 2  AND `category_id` = 0  AND `square_id` = 6  AND `is_show` = 1 ) AND `cm_kb_robot_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000419s ]
[2025-06-04T16:06:40+08:00][sql] CONNECT:[ UseTime:0.009633s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_session` [ RunTime:0.001782s ]
[2025-06-04T16:06:40+08:00][sql] SELECT KRS.id,KRS.robot_id,KRS.square_id,KR.image,KR.name,KR.is_public,KR.delete_time,KR.intro,KRS.create_time FROM `cm_kb_robot_session` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` WHERE (  `KRS`.`user_id` = '2'  AND `KRS`.`square_id` > '0' ) AND `KRS`.`delete_time` IS NULL ORDER BY `KRS`.`id` DESC [ RunTime:0.001347s ]
[2025-06-04T16:06:40+08:00][sql] CONNECT:[ UseTime:0.001966s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.001429s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `id`,`user_id`,`cate_id`,`code`,`kb_ids`,`icons`,`image`,`name`,`intro`,`model`,`model_id`,`model_sub_id`,`roles_prompt`,`temperature`,`search_similarity`,`search_limits`,`search_empty_type`,`search_empty_text`,`welcome_introducer`,`related_issues_num`,`copyright`,`share_bg`,`digital_bg`,`flow_status`,`flow_config`,`context_num`,`digital_id`,`is_digital`,`is_show_feedback`,`is_show_context`,`is_show_quote`,`is_public`,`is_enable`,`support_file`,`create_time`,`update_time` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.010825s ]
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_instruct` [ RunTime:0.000647s ]
[2025-06-04T16:06:40+08:00][sql] SELECT keyword,content,images FROM `cm_kb_robot_instruct` WHERE  `robot_id` = 4 ORDER BY `id` ASC [ RunTime:0.000292s ]
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_visitor` [ RunTime:0.000468s ]
[2025-06-04T16:06:40+08:00][sql] SELECT * FROM `cm_kb_robot_visitor` WHERE  `ip` = '*************'  AND `robot_id` = 4  AND `terminal` = 1  AND `create_time` BETWEEN 1748966400 AND 1749052799 LIMIT 1 [ RunTime:0.000367s ]
[2025-06-04T16:06:40+08:00][sql] UPDATE `cm_kb_robot_visitor`  SET `visit` = 9 , `update_time` = 1749024400  WHERE  `id` = 14 [ RunTime:0.000243s ]
[2025-06-04T16:06:40+08:00][sql] CONNECT:[ UseTime:0.003528s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_decorate_page` [ RunTime:0.000909s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `type`,`name`,`data` FROM `cm_decorate_page` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000385s ]
[2025-06-04T16:06:40+08:00][sql] CONNECT:[ UseTime:0.024973s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.004200s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.003504s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000546s ]
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000962s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000361s ]
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000790s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000368s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000324s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.001293s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000359s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.002548s ]
[2025-06-04T16:06:40+08:00][sql] CONNECT:[ UseTime:0.001009s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000887s ]
[2025-06-04T16:06:40+08:00][sql] SELECT id,code,is_show_context,is_show_quote FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 1 [ RunTime:0.000408s ]
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_record` [ RunTime:0.000689s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_record` WHERE (  `robot_id` = 4  AND `user_id` = 2  AND `category_id` = 0  AND `square_id` = 6  AND `is_show` = 1 ) AND `cm_kb_robot_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000428s ]
[2025-06-04T16:06:40+08:00][sql] CONNECT:[ UseTime:0.001053s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000776s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000574s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000556s ]
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000960s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000398s ]
[2025-06-04T16:06:40+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000757s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000435s ]
[2025-06-04T16:06:40+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000313s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000546s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000396s ]
[2025-06-04T16:06:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000439s ]
[2025-06-04T16:06:43+08:00][sql] CONNECT:[ UseTime:0.001024s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:43+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000869s ]
[2025-06-04T16:06:43+08:00][sql] SELECT id,sn,sex,account,nickname,real_name,avatar,mobile,email,is_blacklist,is_new_user,create_time,password,balance,video_num,robot_num,total_chat,first_leader,`total_space` FROM `cm_user` WHERE (  `id` = 2 ) AND `cm_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000706s ]
[2025-06-04T16:06:43+08:00][sql] SHOW FULL COLUMNS FROM `cm_user_auth` [ RunTime:0.000510s ]
[2025-06-04T16:06:43+08:00][sql] SELECT * FROM `cm_user_auth` WHERE  `user_id` = 2  AND `terminal` = 4 LIMIT 1 [ RunTime:0.000309s ]
[2025-06-04T16:06:43+08:00][sql] SHOW FULL COLUMNS FROM `cm_member_package` [ RunTime:0.000466s ]
[2025-06-04T16:06:43+08:00][sql] SELECT `id` FROM `cm_member_package` WHERE `cm_member_package`.`delete_time` IS NULL ORDER BY `sort` DESC,`id` DESC [ RunTime:0.000331s ]
[2025-06-04T16:06:43+08:00][sql] SHOW FULL COLUMNS FROM `cm_user_member` [ RunTime:0.000516s ]
[2025-06-04T16:06:43+08:00][sql] SELECT `id`,`package_id`,`member_end_time`,`is_perpetual` FROM `cm_user_member` WHERE (  ( `user_id` = 2 AND `is_perpetual` = 0 AND `member_end_time` > 1749024403 AND `package_id` IN (3,2,1) )  OR ( `is_perpetual` = 1 AND `user_id` = 2 AND `package_id` IN (3,2,1) ) ) AND `cm_user_member`.`delete_time` IS NULL ORDER BY FIELD(package_id,3,2,1) LIMIT 1 [ RunTime:0.000477s ]
[2025-06-04T16:06:43+08:00][sql] SELECT `id`,`package_id`,`member_end_time`,`is_perpetual` FROM `cm_user_member` WHERE (  `user_id` = 2  AND `package_id` IN (3,2,1)  AND `is_clear` = 0 ) AND `cm_user_member`.`delete_time` IS NULL ORDER BY FIELD(package_id,3,2,1) LIMIT 1 [ RunTime:0.000529s ]
[2025-06-04T16:06:43+08:00][sql] SHOW FULL COLUMNS FROM `cm_music_record` [ RunTime:0.000722s ]
[2025-06-04T16:06:43+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_music_record` WHERE (  `user_id` = 2  AND `status` = 2 ) AND `cm_music_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000448s ]
[2025-06-04T16:06:43+08:00][sql] SHOW FULL COLUMNS FROM `cm_video_record` [ RunTime:0.000645s ]
[2025-06-04T16:06:43+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_video_record` WHERE (  `user_id` = 2  AND `status` = 2 ) AND `cm_video_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000402s ]
[2025-06-04T16:06:43+08:00][sql] SHOW FULL COLUMNS FROM `cm_draw_records` [ RunTime:0.000608s ]
[2025-06-04T16:06:43+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_draw_records` WHERE (  `user_id` = 2  AND `status` = 3 ) AND `cm_draw_records`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000316s ]
[2025-06-04T16:06:43+08:00][sql] CONNECT:[ UseTime:0.010905s ] pgsql:dbname=postgres;host=chatmoney-postgres;port=5432
[2025-06-04T16:06:43+08:00][sql] SELECT COUNT(*) AS think_count FROM cm_kb_embedding WHERE  user_id = 2  AND is_delete = 0 LIMIT 1 [ RunTime:0.001133s ]
[2025-06-04T16:06:43+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.001002s ]
[2025-06-04T16:06:43+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000588s ]
[2025-06-04T16:06:44+08:00][sql] CONNECT:[ UseTime:0.031125s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:44+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.010110s ]
[2025-06-04T16:06:44+08:00][sql] SELECT id,code,is_show_context,is_show_quote FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 1 [ RunTime:0.000462s ]
[2025-06-04T16:06:44+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_record` [ RunTime:0.008907s ]
[2025-06-04T16:06:44+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_record` WHERE (  `robot_id` = 4  AND `user_id` = 2  AND `category_id` = 0  AND `square_id` = 6  AND `is_show` = 1 ) AND `cm_kb_robot_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.004272s ]
[2025-06-04T16:06:45+08:00][sql] CONNECT:[ UseTime:0.015211s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_decorate_page` [ RunTime:0.001382s ]
[2025-06-04T16:06:45+08:00][sql] SELECT `type`,`name`,`data` FROM `cm_decorate_page` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.000304s ]
[2025-06-04T16:06:45+08:00][sql] CONNECT:[ UseTime:0.001796s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_category` [ RunTime:0.001031s ]
[2025-06-04T16:06:45+08:00][sql] SELECT id,name,image FROM `cm_kb_robot_category` WHERE (  `is_enable` = 1 ) AND `cm_kb_robot_category`.`delete_time` IS NULL ORDER BY `sort` DESC,`id` DESC [ RunTime:0.000442s ]
[2025-06-04T16:06:45+08:00][sql] CONNECT:[ UseTime:0.012661s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_square` [ RunTime:0.015679s ]
[2025-06-04T16:06:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_square` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` INNER JOIN `cm_user` `U` ON `U`.`id`=`KRS`.`user_id` WHERE (  `KRS`.`is_show` = '1' ) AND `KRS`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.001694s ]
[2025-06-04T16:06:45+08:00][sql] SELECT KRS.id,KRS.robot_id,KR.name,U.nickname as author,KR.image,KR.intro,KR.is_enable,KRS.create_time FROM `cm_kb_robot_square` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` INNER JOIN `cm_user` `U` ON `U`.`id`=`KRS`.`user_id` WHERE (  `KRS`.`is_show` = '1' ) AND `KRS`.`delete_time` IS NULL ORDER BY `KRS`.`sort` DESC,`KRS`.`id` DESC LIMIT 0,15 [ RunTime:0.000534s ]
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_config` [ RunTime:0.001814s ]
[2025-06-04T16:06:45+08:00][sql] SELECT `value` FROM `cm_config` WHERE  `type` = 'robot_award'  AND `name` = 'is_show_user' LIMIT 1 [ RunTime:0.001351s ]
[2025-06-04T16:06:45+08:00][sql] CONNECT:[ UseTime:0.002015s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.001313s ]
[2025-06-04T16:06:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.001984s ]
[2025-06-04T16:06:45+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000396s ]
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000642s ]
[2025-06-04T16:06:45+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000447s ]
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000641s ]
[2025-06-04T16:06:45+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000258s ]
[2025-06-04T16:06:45+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000246s ]
[2025-06-04T16:06:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000275s ]
[2025-06-04T16:06:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000241s ]
[2025-06-04T16:06:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000217s ]
[2025-06-04T16:06:45+08:00][sql] CONNECT:[ UseTime:0.001352s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_decorate_page` [ RunTime:0.012913s ]
[2025-06-04T16:06:45+08:00][sql] SELECT `type`,`name`,`data` FROM `cm_decorate_page` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000457s ]
[2025-06-04T16:06:45+08:00][sql] CONNECT:[ UseTime:0.001129s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_square` [ RunTime:0.000769s ]
[2025-06-04T16:06:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_square` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` INNER JOIN `cm_user` `U` ON `U`.`id`=`KRS`.`user_id` WHERE (  `KRS`.`is_show` = '1' ) AND `KRS`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000588s ]
[2025-06-04T16:06:45+08:00][sql] SELECT KRS.id,KRS.robot_id,KR.name,U.nickname as author,KR.image,KR.intro,KR.is_enable,KRS.create_time FROM `cm_kb_robot_square` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` INNER JOIN `cm_user` `U` ON `U`.`id`=`KRS`.`user_id` WHERE (  `KRS`.`is_show` = '1' ) AND `KRS`.`delete_time` IS NULL ORDER BY `KRS`.`sort` DESC,`KRS`.`id` DESC LIMIT 0,15 [ RunTime:0.000562s ]
[2025-06-04T16:06:45+08:00][sql] SHOW FULL COLUMNS FROM `cm_config` [ RunTime:0.000600s ]
[2025-06-04T16:06:45+08:00][sql] SELECT `value` FROM `cm_config` WHERE  `type` = 'robot_award'  AND `name` = 'is_show_user' LIMIT 1 [ RunTime:0.000324s ]
[2025-06-04T16:06:46+08:00][sql] CONNECT:[ UseTime:0.009388s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_session` [ RunTime:0.000816s ]
[2025-06-04T16:06:46+08:00][sql] SELECT * FROM `cm_kb_robot_session` WHERE (  `user_id` = 2  AND `square_id` = 1 ) AND `cm_kb_robot_session`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000510s ]
[2025-06-04T16:06:46+08:00][sql] CONNECT:[ UseTime:0.019883s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_session` [ RunTime:0.003876s ]
[2025-06-04T16:06:46+08:00][sql] SELECT KRS.id,KRS.robot_id,KRS.square_id,KR.image,KR.name,KR.is_public,KR.delete_time,KR.intro,KRS.create_time FROM `cm_kb_robot_session` `KRS` INNER JOIN `cm_kb_robot` `KR` ON `KR`.`id`=`KRS`.`robot_id` WHERE (  `KRS`.`user_id` = '2'  AND `KRS`.`square_id` > '0' ) AND `KRS`.`delete_time` IS NULL ORDER BY `KRS`.`id` DESC [ RunTime:0.002311s ]
[2025-06-04T16:06:46+08:00][sql] CONNECT:[ UseTime:0.021728s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.002016s ]
[2025-06-04T16:06:46+08:00][sql] SELECT id,code,is_show_context,is_show_quote FROM `cm_kb_robot` WHERE (  `id` = 3 ) AND `cm_kb_robot`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 1 [ RunTime:0.003167s ]
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_record` [ RunTime:0.004691s ]
[2025-06-04T16:06:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_record` WHERE (  `robot_id` = 3  AND `user_id` = 2  AND `category_id` = 0  AND `square_id` = 3  AND `is_show` = 1 ) AND `cm_kb_robot_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000558s ]
[2025-06-04T16:06:46+08:00][sql] CONNECT:[ UseTime:0.003553s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.001452s ]
[2025-06-04T16:06:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000472s ]
[2025-06-04T16:06:46+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000327s ]
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.002709s ]
[2025-06-04T16:06:46+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000375s ]
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000733s ]
[2025-06-04T16:06:46+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000405s ]
[2025-06-04T16:06:46+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000384s ]
[2025-06-04T16:06:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000599s ]
[2025-06-04T16:06:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000356s ]
[2025-06-04T16:06:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000326s ]
[2025-06-04T16:06:46+08:00][sql] CONNECT:[ UseTime:0.006096s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.003001s ]
[2025-06-04T16:06:46+08:00][sql] SELECT `id`,`user_id`,`cate_id`,`code`,`kb_ids`,`icons`,`image`,`name`,`intro`,`model`,`model_id`,`model_sub_id`,`roles_prompt`,`temperature`,`search_similarity`,`search_limits`,`search_empty_type`,`search_empty_text`,`welcome_introducer`,`related_issues_num`,`copyright`,`share_bg`,`digital_bg`,`flow_status`,`flow_config`,`context_num`,`digital_id`,`is_digital`,`is_show_feedback`,`is_show_context`,`is_show_quote`,`is_public`,`is_enable`,`support_file`,`create_time`,`update_time` FROM `cm_kb_robot` WHERE (  `id` = 3 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.002237s ]
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_instruct` [ RunTime:0.019808s ]
[2025-06-04T16:06:46+08:00][sql] SELECT keyword,content,images FROM `cm_kb_robot_instruct` WHERE  `robot_id` = 3 ORDER BY `id` ASC [ RunTime:0.000767s ]
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_visitor` [ RunTime:0.001863s ]
[2025-06-04T16:06:46+08:00][sql] SELECT * FROM `cm_kb_robot_visitor` WHERE  `ip` = '*************'  AND `robot_id` = 3  AND `terminal` = 1  AND `create_time` BETWEEN 1748966400 AND 1749052799 LIMIT 1 [ RunTime:0.002884s ]
[2025-06-04T16:06:46+08:00][sql] UPDATE `cm_kb_robot_visitor`  SET `visit` = 5 , `update_time` = 1749024406  WHERE  `id` = 15 [ RunTime:0.003884s ]
[2025-06-04T16:06:46+08:00][sql] CONNECT:[ UseTime:0.000978s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_decorate_page` [ RunTime:0.000742s ]
[2025-06-04T16:06:46+08:00][sql] SELECT `type`,`name`,`data` FROM `cm_decorate_page` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000398s ]
[2025-06-04T16:06:46+08:00][sql] CONNECT:[ UseTime:0.001985s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000829s ]
[2025-06-04T16:06:46+08:00][sql] SELECT id,code,is_show_context,is_show_quote FROM `cm_kb_robot` WHERE (  `id` = 3 ) AND `cm_kb_robot`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 1 [ RunTime:0.000395s ]
[2025-06-04T16:06:46+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_record` [ RunTime:0.000889s ]
[2025-06-04T16:06:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_record` WHERE (  `robot_id` = 3  AND `user_id` = 2  AND `category_id` = 0  AND `square_id` = 3  AND `is_show` = 1 ) AND `cm_kb_robot_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000492s ]
[2025-06-04T16:06:50+08:00][sql] CONNECT:[ UseTime:0.000931s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:50+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000811s ]
[2025-06-04T16:06:50+08:00][sql] SELECT id,sn,sex,account,nickname,real_name,avatar,mobile,email,is_blacklist,is_new_user,create_time,password,balance,video_num,robot_num,total_chat,first_leader,`total_space` FROM `cm_user` WHERE (  `id` = 2 ) AND `cm_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000499s ]
[2025-06-04T16:06:50+08:00][sql] SHOW FULL COLUMNS FROM `cm_user_auth` [ RunTime:0.000489s ]
[2025-06-04T16:06:50+08:00][sql] SELECT * FROM `cm_user_auth` WHERE  `user_id` = 2  AND `terminal` = 4 LIMIT 1 [ RunTime:0.000291s ]
[2025-06-04T16:06:50+08:00][sql] SHOW FULL COLUMNS FROM `cm_member_package` [ RunTime:0.002422s ]
[2025-06-04T16:06:50+08:00][sql] SELECT `id` FROM `cm_member_package` WHERE `cm_member_package`.`delete_time` IS NULL ORDER BY `sort` DESC,`id` DESC [ RunTime:0.001241s ]
[2025-06-04T16:06:50+08:00][sql] SHOW FULL COLUMNS FROM `cm_user_member` [ RunTime:0.003116s ]
[2025-06-04T16:06:50+08:00][sql] SELECT `id`,`package_id`,`member_end_time`,`is_perpetual` FROM `cm_user_member` WHERE (  ( `user_id` = 2 AND `is_perpetual` = 0 AND `member_end_time` > 1749024410 AND `package_id` IN (3,2,1) )  OR ( `is_perpetual` = 1 AND `user_id` = 2 AND `package_id` IN (3,2,1) ) ) AND `cm_user_member`.`delete_time` IS NULL ORDER BY FIELD(package_id,3,2,1) LIMIT 1 [ RunTime:0.005037s ]
[2025-06-04T16:06:50+08:00][sql] SELECT `id`,`package_id`,`member_end_time`,`is_perpetual` FROM `cm_user_member` WHERE (  `user_id` = 2  AND `package_id` IN (3,2,1)  AND `is_clear` = 0 ) AND `cm_user_member`.`delete_time` IS NULL ORDER BY FIELD(package_id,3,2,1) LIMIT 1 [ RunTime:0.000492s ]
[2025-06-04T16:06:50+08:00][sql] SHOW FULL COLUMNS FROM `cm_music_record` [ RunTime:0.000766s ]
[2025-06-04T16:06:50+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_music_record` WHERE (  `user_id` = 2  AND `status` = 2 ) AND `cm_music_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000333s ]
[2025-06-04T16:06:50+08:00][sql] SHOW FULL COLUMNS FROM `cm_video_record` [ RunTime:0.000558s ]
[2025-06-04T16:06:50+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_video_record` WHERE (  `user_id` = 2  AND `status` = 2 ) AND `cm_video_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000320s ]
[2025-06-04T16:06:50+08:00][sql] SHOW FULL COLUMNS FROM `cm_draw_records` [ RunTime:0.000521s ]
[2025-06-04T16:06:50+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_draw_records` WHERE (  `user_id` = 2  AND `status` = 3 ) AND `cm_draw_records`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000279s ]
[2025-06-04T16:06:50+08:00][sql] CONNECT:[ UseTime:0.012483s ] pgsql:dbname=postgres;host=chatmoney-postgres;port=5432
[2025-06-04T16:06:50+08:00][sql] SELECT COUNT(*) AS think_count FROM cm_kb_embedding WHERE  user_id = 2  AND is_delete = 0 LIMIT 1 [ RunTime:0.001003s ]
[2025-06-04T16:06:50+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000787s ]
[2025-06-04T16:06:50+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000433s ]
[2025-06-04T16:06:51+08:00][sql] CONNECT:[ UseTime:0.000947s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:51+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000610s ]
[2025-06-04T16:06:51+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000396s ]
[2025-06-04T16:06:51+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000387s ]
[2025-06-04T16:06:51+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000660s ]
[2025-06-04T16:06:51+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000299s ]
[2025-06-04T16:06:51+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000584s ]
[2025-06-04T16:06:51+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000370s ]
[2025-06-04T16:06:51+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000217s ]
[2025-06-04T16:06:51+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000380s ]
[2025-06-04T16:06:51+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000231s ]
[2025-06-04T16:06:51+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000229s ]
[2025-06-04T16:06:52+08:00][sql] CONNECT:[ UseTime:0.000869s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:52+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000689s ]
[2025-06-04T16:06:52+08:00][sql] SELECT id,code,is_show_context,is_show_quote FROM `cm_kb_robot` WHERE (  `id` = 3 ) AND `cm_kb_robot`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 1 [ RunTime:0.000346s ]
[2025-06-04T16:06:52+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot_record` [ RunTime:0.000628s ]
[2025-06-04T16:06:52+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_kb_robot_record` WHERE (  `robot_id` = 3  AND `user_id` = 2  AND `category_id` = 0  AND `square_id` = 3  AND `is_show` = 1 ) AND `cm_kb_robot_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000415s ]
[2025-06-04T16:06:55+08:00][sql] CONNECT:[ UseTime:0.000927s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:06:55+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000752s ]
[2025-06-04T16:06:55+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000387s ]
[2025-06-04T16:06:55+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000332s ]
[2025-06-04T16:06:55+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000744s ]
[2025-06-04T16:06:55+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000396s ]
[2025-06-04T16:06:55+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000729s ]
[2025-06-04T16:06:55+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000304s ]
[2025-06-04T16:06:55+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000247s ]
[2025-06-04T16:06:55+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000393s ]
[2025-06-04T16:06:55+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000334s ]
[2025-06-04T16:06:55+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000374s ]
[2025-06-04T16:07:01+08:00][sql] CONNECT:[ UseTime:0.001079s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:07:01+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000825s ]
[2025-06-04T16:07:01+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000406s ]
[2025-06-04T16:07:01+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000373s ]
[2025-06-04T16:07:01+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000668s ]
[2025-06-04T16:07:01+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000421s ]
[2025-06-04T16:07:01+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000704s ]
[2025-06-04T16:07:01+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000345s ]
[2025-06-04T16:07:01+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000291s ]
[2025-06-04T16:07:01+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000402s ]
[2025-06-04T16:07:01+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000407s ]
[2025-06-04T16:07:01+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000296s ]
[2025-06-04T16:07:05+08:00][sql] CONNECT:[ UseTime:0.000989s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:07:05+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000613s ]
[2025-06-04T16:07:05+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000372s ]
[2025-06-04T16:07:05+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000315s ]
[2025-06-04T16:07:05+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000627s ]
[2025-06-04T16:07:05+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000244s ]
[2025-06-04T16:07:05+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000527s ]
[2025-06-04T16:07:05+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000236s ]
[2025-06-04T16:07:05+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000271s ]
[2025-06-04T16:07:05+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000326s ]
[2025-06-04T16:07:05+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000316s ]
[2025-06-04T16:07:05+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000489s ]
[2025-06-04T16:07:06+08:00][sql] CONNECT:[ UseTime:0.000860s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:07:06+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000773s ]
[2025-06-04T16:07:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000540s ]
[2025-06-04T16:07:06+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000417s ]
[2025-06-04T16:07:06+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000587s ]
[2025-06-04T16:07:06+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000355s ]
[2025-06-04T16:07:06+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000721s ]
[2025-06-04T16:07:06+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000342s ]
[2025-06-04T16:07:06+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000325s ]
[2025-06-04T16:07:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000303s ]
[2025-06-04T16:07:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000231s ]
[2025-06-04T16:07:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000309s ]
[2025-06-04T16:07:12+08:00][sql] CONNECT:[ UseTime:0.001084s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:07:12+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000860s ]
[2025-06-04T16:07:12+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000470s ]
[2025-06-04T16:07:12+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000376s ]
[2025-06-04T16:07:12+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000764s ]
[2025-06-04T16:07:12+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000319s ]
[2025-06-04T16:07:12+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000652s ]
[2025-06-04T16:07:12+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000355s ]
[2025-06-04T16:07:12+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-06-04T16:07:12+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000318s ]
[2025-06-04T16:07:12+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000292s ]
[2025-06-04T16:07:12+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000263s ]
[2025-06-04T16:07:16+08:00][sql] CONNECT:[ UseTime:0.000979s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:07:16+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000792s ]
[2025-06-04T16:07:16+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000644s ]
[2025-06-04T16:07:16+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000491s ]
[2025-06-04T16:07:16+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000889s ]
[2025-06-04T16:07:16+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000428s ]
[2025-06-04T16:07:16+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000796s ]
[2025-06-04T16:07:16+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000318s ]
[2025-06-04T16:07:16+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000318s ]
[2025-06-04T16:07:16+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000327s ]
[2025-06-04T16:07:16+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000314s ]
[2025-06-04T16:07:16+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000307s ]
[2025-06-04T16:07:22+08:00][sql] CONNECT:[ UseTime:0.001108s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:07:22+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000763s ]
[2025-06-04T16:07:22+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000503s ]
[2025-06-04T16:07:22+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000472s ]
[2025-06-04T16:07:22+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000802s ]
[2025-06-04T16:07:22+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000407s ]
[2025-06-04T16:07:22+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000791s ]
[2025-06-04T16:07:22+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000399s ]
[2025-06-04T16:07:22+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000258s ]
[2025-06-04T16:07:22+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000358s ]
[2025-06-04T16:07:22+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000415s ]
[2025-06-04T16:07:22+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000303s ]
[2025-06-04T16:07:26+08:00][sql] CONNECT:[ UseTime:0.001079s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:07:26+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000775s ]
[2025-06-04T16:07:26+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000434s ]
[2025-06-04T16:07:26+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000348s ]
[2025-06-04T16:07:26+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000648s ]
[2025-06-04T16:07:26+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000357s ]
[2025-06-04T16:07:26+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000636s ]
[2025-06-04T16:07:26+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000322s ]
[2025-06-04T16:07:26+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000264s ]
[2025-06-04T16:07:26+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000382s ]
[2025-06-04T16:07:26+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.001258s ]
[2025-06-04T16:07:26+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000374s ]
[2025-06-04T16:07:27+08:00][sql] CONNECT:[ UseTime:0.001027s ] mysql:host=chatmoney-mysql;port=3306;dbname=chatmoney;charset=utf8mb4
[2025-06-04T16:07:27+08:00][sql] SHOW FULL COLUMNS FROM `cm_notice_record` [ RunTime:0.000796s ]
[2025-06-04T16:07:27+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000486s ]
[2025-06-04T16:07:27+08:00][sql] SELECT `id`,`user_id`,`robot_id`,`title`,`content`,`read`,`notice_type`,`send_uid`,`extra`,`create_time` FROM `cm_notice_record` WHERE (  `user_id` = 2  AND `scene_id` IN (300,500)  AND `recipient` = 1  AND `send_type` = 1  AND `notice_type` IN (3,4,5,6,7) ) AND `cm_notice_record`.`delete_time` IS NULL ORDER BY `read` ASC,`id` DESC LIMIT 0,5 [ RunTime:0.000382s ]
[2025-06-04T16:07:27+08:00][sql] SHOW FULL COLUMNS FROM `cm_kb_robot` [ RunTime:0.000641s ]
[2025-06-04T16:07:27+08:00][sql] SELECT `name`,`id` FROM `cm_kb_robot` WHERE (  `id` = 0 ) AND `cm_kb_robot`.`delete_time` IS NULL [ RunTime:0.000272s ]
[2025-06-04T16:07:27+08:00][sql] SHOW FULL COLUMNS FROM `cm_user` [ RunTime:0.000676s ]
[2025-06-04T16:07:27+08:00][sql] SELECT `avatar`,`nickname`,`id` FROM `cm_user` WHERE (  `id` = 0 ) AND `cm_user`.`delete_time` IS NULL [ RunTime:0.000323s ]
[2025-06-04T16:07:27+08:00][sql] SELECT `name` FROM `cm_kb_robot` WHERE (  `id` = 4 ) AND `cm_kb_robot`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000385s ]
[2025-06-04T16:07:27+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` IN (300,500)  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000477s ]
[2025-06-04T16:07:27+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 300  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000410s ]
[2025-06-04T16:07:27+08:00][sql] SELECT COUNT(*) AS think_count FROM `cm_notice_record` WHERE (  `read` = 0  AND `scene_id` = 500  AND `user_id` = 2  AND `recipient` = 1  AND `send_type` = 1 ) AND `cm_notice_record`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000414s ]
