/**
 * 首页Banner图片专用缓存工具
 * 仅用于首页banner组件，不影响其他功能
 */

interface CacheImageInfo {
    url: string           // 原始图片URL
    cachedUrl: string     // 缓存后的URL
    timestamp: number     // 缓存时间戳
    expire: number        // 过期时间戳
}

interface BannerImageCacheData {
    [key: string]: CacheImageInfo
}

class BannerImageCache {
    private readonly CACHE_KEY = 'app_banner_image_cache'
    private readonly MAX_CACHE_SIZE = 20 // 最多缓存20张banner图片
    private readonly CACHE_EXPIRE = 7 * 24 * 60 * 60 * 1000 // 7天过期

    /**
     * 获取缓存数据
     */
    private getCacheData(): BannerImageCacheData {
        try {
            const data = uni.getStorageSync(this.CACHE_KEY)
            return data ? JSON.parse(data) : {}
        } catch (error) {
            console.warn('Banner图片缓存数据读取失败:', error)
            return {}
        }
    }

    /**
     * 保存缓存数据
     */
    private setCacheData(data: BannerImageCacheData): void {
        try {
            uni.setStorageSync(this.CACHE_KEY, JSON.stringify(data))
        } catch (error) {
            console.warn('Banner图片缓存数据保存失败:', error)
        }
    }

    /**
     * 生成缓存key
     */
    private generateCacheKey(url: string): string {
        return `banner_img_${this.hashCode(url)}`
    }

    /**
     * 简单的字符串hash函数
     */
    private hashCode(str: string): string {
        let hash = 0
        if (str.length === 0) return hash.toString()
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i)
            hash = ((hash << 5) - hash) + char
            hash = hash & hash // 转为32位整数
        }
        return Math.abs(hash).toString()
    }

    /**
     * 清理过期缓存
     */
    private cleanExpiredCache(): void {
        const cacheData = this.getCacheData()
        const now = Date.now()
        let hasChanges = false

        Object.keys(cacheData).forEach(key => {
            const item = cacheData[key]
            if (item.expire < now) {
                delete cacheData[key]
                hasChanges = true
            }
        })

        if (hasChanges) {
            this.setCacheData(cacheData)
        }
    }

    /**
     * 清理最旧的缓存（当缓存数量超限时）
     */
    private cleanOldestCache(): void {
        const cacheData = this.getCacheData()
        const items = Object.entries(cacheData)
        
        if (items.length <= this.MAX_CACHE_SIZE) return

        // 按时间戳排序，删除最旧的
        items.sort((a, b) => a[1].timestamp - b[1].timestamp)
        
        const toDelete = items.slice(0, items.length - this.MAX_CACHE_SIZE)
        toDelete.forEach(([key]) => {
            delete cacheData[key]
        })

        this.setCacheData(cacheData)
    }

    /**
     * 预加载图片
     */
    private preloadImage(url: string): Promise<string> {
        return new Promise((resolve, reject) => {
            uni.downloadFile({
                url: url,
                success: (res) => {
                    if (res.statusCode === 200) {
                        resolve(res.tempFilePath)
                    } else {
                        reject(new Error(`下载失败，状态码: ${res.statusCode}`))
                    }
                },
                fail: (error) => {
                    reject(new Error(`下载失败: ${error.errMsg}`))
                }
            })
        })
    }

    /**
     * 获取缓存的图片URL
     */
    async getCachedImageUrl(url: string): Promise<string> {
        if (!url) return url

        // 清理过期缓存
        this.cleanExpiredCache()

        const cacheKey = this.generateCacheKey(url)
        const cacheData = this.getCacheData()
        const cachedItem = cacheData[cacheKey]

        // 检查缓存是否存在且未过期
        if (cachedItem && cachedItem.expire > Date.now()) {
            return cachedItem.cachedUrl
        }

        // 缓存不存在或已过期，尝试预加载
        try {
            const cachedPath = await this.preloadImage(url)
            const now = Date.now()
            const expire = now + this.CACHE_EXPIRE

            // 更新缓存记录
            cacheData[cacheKey] = {
                url,
                cachedUrl: cachedPath,
                timestamp: now,
                expire
            }

            this.cleanOldestCache()
            this.setCacheData(cacheData)
            
            return cachedPath
        } catch (error) {
            console.warn('Banner图片预加载失败:', error)
            return url // 预加载失败时返回原URL
        }
    }

    /**
     * 批量预加载banner图片
     */
    async preloadBannerImages(urls: string[]): Promise<string[]> {
        const promises = urls.map(url => {
            return this.getCachedImageUrl(url).catch(error => {
                console.warn(`预加载banner图片失败: ${url}`, error)
                return url
            })
        })

        try {
            const results = await Promise.all(promises)
            console.log('Banner图片预加载完成')
            return results
        } catch (error) {
            console.warn('Banner图片预加载部分失败:', error)
            return urls
        }
    }

    /**
     * 清理所有banner图片缓存
     */
    clearAllCache(): void {
        uni.removeStorageSync(this.CACHE_KEY)
        console.log('Banner图片缓存已清理')
    }

    /**
     * 获取缓存信息
     */
    getCacheInfo(): { count: number } {
        const cacheData = this.getCacheData()
        return {
            count: Object.keys(cacheData).length
        }
    }
}

// 导出单例实例
export const bannerImageCache = new BannerImageCache()

export default bannerImageCache 