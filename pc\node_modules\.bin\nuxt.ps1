#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="F:\erkai999\pc\node_modules\.pnpm\nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq\node_modules\nuxt\bin\node_modules;F:\erkai999\pc\node_modules\.pnpm\nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq\node_modules\nuxt\node_modules;F:\erkai999\pc\node_modules\.pnpm\nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq\node_modules;F:\erkai999\pc\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/f/erkai999/pc/node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../nuxt/bin/nuxt.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../nuxt/bin/nuxt.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../nuxt/bin/nuxt.mjs" $args
  } else {
    & "node$exe"  "$basedir/../nuxt/bin/nuxt.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
