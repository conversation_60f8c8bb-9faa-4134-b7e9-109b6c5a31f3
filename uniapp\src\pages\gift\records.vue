<template>
  <page-meta :page-style="$theme.pageStyle"></page-meta>
  <view class="gift-records">
    <!-- 导航栏 -->
    <nav-bar title="赠送记录" :show-back="true">
      <template #right>
        <button class="filter-btn" @click="showFilterModal = true">
          <text class="text-lg">⚙️</text>
        </button>
      </template>
    </nav-bar>
    
    <!-- 统计卡片 -->
    <view class="stats-section p-4">
      <view class="stats-grid grid grid-cols-2 gap-3">
        <view class="stat-card bg-gradient-to-r from-red-500 to-red-600 text-white p-4 rounded-xl text-center">
          <view class="text-sm opacity-90 mb-1">本月赠送</view>
          <view class="text-xl font-bold">{{ statistics?.monthSend || 0 }}</view>
          <view class="text-xs opacity-75">{{ appStore.getChatConfig.price_unit }}</view>
        </view>
        <view class="stat-card bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl text-center">
          <view class="text-sm opacity-90 mb-1">本月接收</view>
          <view class="text-xl font-bold">{{ statistics?.monthReceive || 0 }}</view>
          <view class="text-xs opacity-75">{{ appStore.getChatConfig.price_unit }}</view>
        </view>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tabs-section">
      <u-tabs
        :list="tabs"
        :current="tabs.findIndex(tab => tab.value === activeTab)"
        :active-color="$theme.primaryColor"
        @change="onTabChange"
      />

    </view>

    <!-- 记录列表 -->
    <view class="records-section p-4">
      <view v-if="loading" class="loading-state text-center py-20">
        <view class="loading-spinner mb-4"></view>
        <text class="text-gray-500">加载中...</text>
      </view>
      
      <view v-else-if="records.length === 0" class="empty-state text-center py-20">
        <text class="text-6xl mb-4 block">📋</text>
        <text class="text-gray-500">暂无记录</text>
      </view>
      
      <view v-else class="records-list">
        <view 
          v-for="record in records" 
          :key="record.id"
          class="record-card bg-white rounded-xl p-4 mb-3"
          @click="showRecordDetail(record)"
        >
          <!-- 记录头部 -->
          <view class="record-header flex justify-between items-center mb-3">
            <view class="record-sn text-sm text-gray-500">{{ record.gift_sn }}</view>
            <view class="record-status">
              <text 
                class="status-tag px-2 py-1 rounded-full text-xs"
                :class="getStatusClass(record.status)"
              >
                {{ getStatusText(record.status) }}
              </text>
            </view>
          </view>
          
          <!-- 用户信息 -->
          <view class="user-info flex items-center mb-3">
            <image 
              :src="record.type === 'send' ? record.to_user_avatar : record.from_user_avatar" 
              class="user-avatar w-12 h-12 rounded-full mr-3"
            />
            <view class="user-details flex-1">
              <view class="user-name font-medium text-lg">
                {{ record.type === 'send' ? record.to_user_nickname : record.from_user_nickname }}
              </view>
              <view class="user-id text-sm text-gray-500">
                ID: {{ record.type === 'send' ? (record.to_user_sn || record.to_user_id) : (record.from_user_sn || record.from_user_id) }}
              </view>
            </view>
            <view class="amount-display text-right">
              <view 
                class="amount text-xl font-bold"
                :class="record.type === 'send' ? 'text-red-500' : 'text-green-500'"
              >
                {{ record.type === 'send' ? '-' : '+' }}{{ Math.floor(parseFloat(record.gift_amount)) }}
              </view>
              <view class="unit text-xs text-gray-500">{{ appStore.getChatConfig.price_unit }}</view>
            </view>
          </view>
          

          
          <!-- 时间信息 -->
          <view class="record-time text-xs text-gray-400 text-right">
            {{ formatTime(record.create_time) }}
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore && !loading" class="load-more p-4">
      <u-button
        type="info"
        shape="circle"
        plain
        @click="loadMore"
      >
        加载更多
      </u-button>
    </view>

    <!-- 筛选模态框 -->
    <view v-if="showFilterModal" class="modal-overlay" @click="showFilterModal = false">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <u-icon name="close" size="20" color="#9ca3af" @click="showFilterModal = false" class="modal-close"></u-icon>
        </view>
        
        <view class="filter-content p-4">
          <!-- 时间筛选 -->
          <view class="filter-item mb-4">
            <view class="filter-label mb-2">时间范围</view>
            <view class="date-picker flex items-center justify-between">
              <picker 
                mode="date" 
                :value="filterForm.start_date"
                @change="onStartDateChange"
              >
                <view class="date-input bg-gray-50 p-2 rounded flex-1 mr-2 text-center">
                  {{ filterForm.start_date || '开始日期' }}
                </view>
              </picker>
              <text class="mx-2">至</text>
              <picker 
                mode="date" 
                :value="filterForm.end_date"
                @change="onEndDateChange"
              >
                <view class="date-input bg-gray-50 p-2 rounded flex-1 ml-2 text-center">
                  {{ filterForm.end_date || '结束日期' }}
                </view>
              </picker>
            </view>
          </view>
          
          <!-- 状态筛选 -->
          <view class="filter-item mb-4">
            <view class="filter-label mb-2">状态</view>
            <view class="status-options flex flex-wrap">
              <u-tag 
                v-for="status in statusOptions" 
                :key="status.value"
                :text="status.label"
                :type="filterForm.status === status.value ? 'primary' : 'info'"
                :mode="filterForm.status === status.value ? 'dark' : 'light'"
                size="mini"
                class="mr-2 mb-2"
                @click="selectStatus(status.value)"
              />
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="filter-actions flex gap-3">
            <u-button
              type="info"
              shape="circle"
              plain
              class="flex-1"
              @click="resetFilter"
            >
              重置
            </u-button>
            <u-button
              type="primary"
              shape="circle"
              class="flex-1"
              @click="applyFilter"
            >
              确定
            </u-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 记录详情模态框 -->
    <view v-if="showDetailModal" class="modal-overlay" @click="showDetailModal = false">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">记录详情</text>
          <u-icon name="close" size="20" color="#9ca3af" @click="showDetailModal = false" class="modal-close"></u-icon>
        </view>
        
        <view v-if="selectedRecord" class="detail-content p-4">
          <view class="detail-item flex justify-between mb-3">
            <text class="label">流水号</text>
            <text class="value">{{ selectedRecord.gift_sn }}</text>
          </view>
          <view class="detail-item flex justify-between mb-3">
            <text class="label">类型</text>
            <text 
              class="value"
              :class="selectedRecord.type === 'send' ? 'text-red-500' : 'text-green-500'"
            >
              {{ selectedRecord.type === 'send' ? '赠送' : '接收' }}
            </text>
          </view>
          <view class="detail-item flex justify-between mb-3">
            <text class="label">对方用户</text>
            <text class="value">
              {{ selectedRecord.type === 'send' ? selectedRecord.to_user_nickname : selectedRecord.from_user_nickname }}
            </text>
          </view>
          <view class="detail-item flex justify-between mb-3">
            <text class="label">灵感值数量</text>
            <text 
              class="value font-bold"
              :class="selectedRecord.type === 'send' ? 'text-red-500' : 'text-green-500'"
            >
              {{ selectedRecord.type === 'send' ? '-' : '+' }}{{ Math.floor(parseFloat(selectedRecord.gift_amount)) }} {{ appStore.getChatConfig.price_unit }}
            </text>
          </view>
          <view class="detail-item flex justify-between mb-3">
            <text class="label">状态</text>
            <text 
              class="value"
              :class="getStatusClass(selectedRecord.status)"
            >
              {{ getStatusText(selectedRecord.status) }}
            </text>
          </view>
          <view class="detail-item flex justify-between mb-3">
            <text class="label">时间</text>
            <text class="value">{{ selectedRecord.create_time }}</text>
          </view>

        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { getGiftRecords, getUserGiftStatistics } from '@/api/gift'
import type { GiftRecord, GiftStatistics } from '@/api/gift'

const appStore = useAppStore()

// 响应式数据
const activeTab = ref('all')
const records = ref<GiftRecord[]>([])
const statistics = ref<GiftStatistics>({
  monthSend: 0,
  monthReceive: 0,
  totalSend: 0,
  totalReceive: 0,
  dailyGiftAmount: 0,
  dailyReceiveAmount: 0,
  dailyGiftTimes: 0,
  dailyReceiveTimes: 0
})
const loading = ref(false)
const hasMore = ref(true)
const page = ref(1)
const showFilterModal = ref(false)
const showDetailModal = ref(false)
const selectedRecord = ref<GiftRecord | null>(null)

const filterForm = ref({
  start_date: '',
  end_date: '',
  status: ''
})

// 配置数据 - uView的u-tabs组件需要name字段
const tabs = [
  { name: '全部', value: 'all' },
  { name: '我的赠送', value: 'send' },
  { name: '我的接收', value: 'receive' }
]

const statusOptions = [
  { label: '全部', value: '' },
  { label: '成功', value: 1 },
  { label: '失败', value: 2 },
  { label: '已撤回', value: 3 }
]

// 方法
const switchTab = (tabValue: string) => {
  console.log('切换标签页:', tabValue)
  activeTab.value = tabValue
  resetList()
  loadRecords()
}

const onTabChange = (index: number) => {
  const tabValue = tabs[index].value
  console.log('标签页索引变化:', index, '对应值:', tabValue)
  switchTab(tabValue)
}

const loadRecords = async (loadMore = false) => {
  if (loading.value) return
  
  try {
    loading.value = true
    
    const currentPage = loadMore ? page.value + 1 : 1
    
    const params = {
      page_no: currentPage,
      page_size: 20,
      type: activeTab.value,
      ...filterForm.value
    }
    
    const data = await getGiftRecords(params)
    console.log('获取到的记录数据:', data)
    
    // 处理数据结构，兼容不同的响应格式
    let lists = []
    console.log('原始数据结构:', data)
    
    if (data && data.lists) {
      // 直接使用data.lists，这是后端返回的格式
      lists = Array.isArray(data.lists) ? data.lists : []
    } else if (data && Array.isArray(data)) {
      // 如果data本身就是数组
      lists = data
    } else {
      console.warn('未知的数据结构:', data)
      lists = []
    }
    
    if (loadMore) {
      records.value = [...records.value, ...lists]
    } else {
      records.value = lists
    }
    
    page.value = currentPage
    hasMore.value = lists.length >= 20
    
    console.log(`加载完成: 页码${currentPage}, 获取${lists.length}条记录, 还有更多: ${hasMore.value}`)
    
  } catch (error) {
    console.error('加载记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  loadRecords(true)
}

const resetList = () => {
  records.value = []
  page.value = 1
  hasMore.value = true
}

const loadStatistics = async () => {
  try {
    const data = await getUserGiftStatistics()
    if (data.data) {
      statistics.value = { ...statistics.value, ...data.data }
    }
  } catch (error) {
    console.error('加载统计失败:', error)
    // 保持默认统计数据，不要设置为undefined
  }
}

const showRecordDetail = (record: GiftRecord) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const onStartDateChange = (e: any) => {
  filterForm.value.start_date = e.detail.value
}

const onEndDateChange = (e: any) => {
  filterForm.value.end_date = e.detail.value
}

const selectStatus = (status: number | string) => {
  filterForm.value.status = status
}

const resetFilter = () => {
  filterForm.value = {
    start_date: '',
    end_date: '',
    status: ''
  }
}

const applyFilter = () => {
  showFilterModal.value = false
  resetList()
  loadRecords()
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '成功',
    2: '失败',
    3: '已撤回'
  }
  return statusMap[status] || '未知'
}

const getStatusClass = (status: number) => {
  const classMap: Record<number, string> = {
    1: 'bg-green-100 text-green-800',
    2: 'bg-red-100 text-red-800',
    3: 'bg-gray-100 text-gray-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const formatTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return Math.floor(diff / 60000) + '分钟前'
  } else if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前'
  } else {
    return date.toLocaleDateString()
  }
}

onMounted(() => {
  loadRecords()
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.gift-records {
  min-height: 100vh;
  background: $u-bg-color;
}

.filter-btn {
  background: none;
  border: none;
  padding: 8rpx;
}

.tabs-section {
  padding: 0 32rpx 32rpx;
}

.records-section {
  .record-card {
    transition: all 0.2s;
    
    &:active {
      transform: scale(0.98);
      background: $u-bg-color;
    }
    
    .user-avatar {
      width: 96rpx;
      height: 96rpx;
    }
    
    .amount {
      font-size: 40rpx;
    }
    
    .unit {
      font-size: 24rpx;
    }
  }
}

.loading-state {
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid $u-border-color;
    border-top: 4rpx solid $u-type-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid $u-border-color;
  
  .modal-title {
    font-size: 36rpx;
    font-weight: 600;
    flex: 1;
    text-align: center;
  }
  
  .modal-close {
    position: absolute;
    right: 32rpx;
    top: 32rpx;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.filter-content {
  .filter-label {
    font-weight: 500;
    color: $u-main-color;
  }
  
  .date-input {
    color: $u-content-color;
    
    &:active {
      background: $u-bg-color;
    }
  }
}

.detail-content {
  .detail-item {
    .label {
      color: $u-tips-color;
      font-size: 28rpx;
    }
    
    .value {
      color: $u-main-color;
      font-size: 28rpx;
    }
  }
  
  .message-box {
    .value {
      word-break: break-all;
      line-height: 1.5;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 