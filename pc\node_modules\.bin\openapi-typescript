#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/openapi-typescript@7.4.4_typescript@4.9.3/node_modules/openapi-typescript/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/openapi-typescript@7.4.4_typescript@4.9.3/node_modules/openapi-typescript/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/openapi-typescript@7.4.4_typescript@4.9.3/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/openapi-typescript@7.4.4_typescript@4.9.3/node_modules/openapi-typescript/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/openapi-typescript@7.4.4_typescript@4.9.3/node_modules/openapi-typescript/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/openapi-typescript@7.4.4_typescript@4.9.3/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../openapi-typescript/bin/cli.js" "$@"
else
  exec node  "$basedir/../openapi-typescript/bin/cli.js" "$@"
fi
