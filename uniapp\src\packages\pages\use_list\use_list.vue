<template>
    <page-meta :page-style="$theme.pageStyle">
        <!-- #ifndef H5 -->
        <navigation-bar
            :front-color="$theme.navColor"
            :background-color="$theme.navBgColor"
        />
        <!-- #endif -->
    </page-meta>
    <view class="earnings-detail">
        <view class="earnings-detail__header bg-white">
            <u-tabs
                :list="tabs"
                :is-scroll="false"
                v-model="state.current"
                :active-color="$theme.primaryColor"
                @change="handleChange"
            ></u-tabs>
        </view>

        <!-- 智能体分成收益提醒 -->
        <view class="mx-[20rpx] mb-[20rpx] p-[24rpx] bg-blue-50 rounded-[14rpx] border-1 border-blue-200" v-if="state.current === 0">
            <view class="flex items-start">
                <view class="text-blue-500 mr-[10rpx] mt-[4rpx]">
                    <text class="iconfont icon-info text-[24rpx]">ℹ️</text>
                </view>
                <view class="text-[24rpx] text-blue-700 flex-1">
                    <view class="font-medium mb-[8rpx]">智能体分成收益说明</view>
                    <view class="leading-[1.4]">
                        • 通过使用智能体广场的智能体可获得分成收益奖励<br/>
                        • 分成收益将在每天凌晨自动结算并发放到您的账户<br/>
                        • 具体分成比例和规则请查看智能体详情页面
                    </view>
                </view>
            </view>
        </view>

        <view class="earnings-detail__main">
            <z-paging
                ref="paging"
                v-model="state.lists"
                @query="getList"
                :fixed="false"
                height="100%"
                empty-view-text="暂无明细记录～"
                :empty-view-img="EmptyWithdrawImage"
                :empty-view-style="{ 'margin-top': '100px' }"
                :empty-view-center="false"
                :auto-clean-list-when-reload="false"
                :auto-scroll-to-top-when-reload="false"
                :empty-view-img-style="{ width: '360rpx', height: '360rpx' }"
            >
                <view class="list mx-[20rpx] bg-white rounded-[14rpx]">
                    <view
                        class="list__item p-[20rpx] flex justify-between"
                        v-for="item in state.lists"
                        :key="item.id"
                        @click="toDetail(item.id)"
                    >
                        <view class="flex-1">
                            <view class="flex items-center mb-[8rpx]">
                                <text class="text-lg text-black mr-[8rpx]">
                                    {{ item.change_type }}
                                </text>
                                <!-- 智能体分成收益标识 -->
                                <view 
                                    v-if="isRobotRevenue(item.change_type)"
                                    class="px-[12rpx] py-[4rpx] text-[20rpx] bg-green-100 text-green-800 rounded-full"
                                >
                                    分成收益
                                </view>
                                <!-- 赠送相关标识 -->
                                <view 
                                    v-if="isGiftRecord(item.change_type)"
                                    class="px-[12rpx] py-[4rpx] text-[20rpx] rounded-full"
                                    :class="item.action == 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                >
                                    {{ item.action == 1 ? '💝 收到赠送' : '💝 已赠送' }}
                                </view>
                            </view>
                            <view class="text-muted text-xs">
                                {{ item.create_time }}
                            </view>
                            <view 
                                v-if="item.robot_name" 
                                class="text-xs text-gray-600 mt-[4rpx]"
                            >
                                智能体：{{ item.robot_name }}
                            </view>
                        </view>
                        <view class="flex items-center">
                            <view
                                v-if="item.action == 1"
                                class="text-2xl"
                                :class="isRobotRevenue(item.change_type) ? 'text-green-600' : 'text-[#FF2C3C]'"
                            >
                                +{{ item.change_amount }}
                            </view>
                            <view v-if="item.action == 2" class="text-2xl">
                                -{{ item.change_amount }}
                            </view>
                        </view>
                    </view>
                </view>
            </z-paging>
        </view>
        <!-- #ifdef H5 -->
        <!--    悬浮菜单    -->
        <floating-menu></floating-menu>
        <!-- #endif -->
    </view>
</template>
<script lang="ts" setup>
import { computed, reactive, shallowRef } from 'vue'
import { accountLog } from '@/api/user'
import EmptyWithdrawImage from '@/packages/static/empty/withdraw.png'
import FloatingMenu from '@/components/floating-menu/floating-menu.vue'
import { onLoad } from '@dcloudio/uni-app'
import { useRoute, useRouter } from 'uniapp-router-next'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()
const route = useRoute()
const router = useRouter()
const paging = shallowRef()
const tabs = computed(() => {
    return [
        {
            name: `${appStore.getTokenUnit}明细`,
            type: 1
        },
        {
            name: '智能体明细',
            type: 2
        }
    ]
})
const state = reactive({
    current: 0,
    lists: [] as any[]
})

// 判断是否是智能体分成收益
const isRobotRevenue = (changeType: string) => {
    return changeType && changeType.includes('智能体分成收益')
}

// 判断是否是赠送记录
const isGiftRecord = (changeType: string) => {
    return changeType && (changeType.includes('赠送') || changeType.includes('接收赠送'))
}

const handleChange = (index: number) => {
    state.current = index
    paging.value.reload()
}

const getList = async (pageNo: number, pageSize: number) => {
    try {
        const { lists } = await accountLog({
            page_no: pageNo,
            page_size: pageSize,
            type: tabs.value[state.current].type
        })
        paging.value.complete(lists)
    } catch (error) {
        paging.value.complete(false)
        console.log('请求佣金列表失败', error)
    }
}

const toDetail = (id: number) => {
    router.navigateTo(
        `/packages/pages/use_detail/use_detail?id=${id}&type=${
            tabs.value[state.current].type
        }`
    )
}

onLoad(() => {
    const opt = route.query
    if (opt.type) {
        state.current = opt.type == 1 ? 0 : 1
    }
})
</script>

<style lang="scss" scoped>
.earnings-detail {
    &__header {
        overflow: hidden;
        margin: 20rpx;
        border-radius: 14rpx;
    }

    &__main {
        height: calc(100vh - 40px - env(safe-area-inset-bottom));

        .list {
            &__item {
                border-bottom: 1px solid #e5e5e5;
            }
            &__item:last-child {
                border-bottom: none;
            }
        }
    }
}
</style>
