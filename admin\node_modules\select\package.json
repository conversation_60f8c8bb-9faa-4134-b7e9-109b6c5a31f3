{"name": "select", "description": "Programmatically select the text of a HTML element", "version": "1.1.2", "license": "MIT", "main": "src/select.js", "repository": "zenorocha/select", "keywords": ["range", "select", "selecting", "selection"], "devDependencies": {"browserify": "^14.0.0", "chai": "^3.3.0", "karma": "^1.4.1", "karma-browserify": "^5.1.1", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^3.2.0", "phantomjs": "^2.1.7"}, "scripts": {"build": "browserify src/select.js -s select -o dist/select.js", "test": "karma start --single-run"}}