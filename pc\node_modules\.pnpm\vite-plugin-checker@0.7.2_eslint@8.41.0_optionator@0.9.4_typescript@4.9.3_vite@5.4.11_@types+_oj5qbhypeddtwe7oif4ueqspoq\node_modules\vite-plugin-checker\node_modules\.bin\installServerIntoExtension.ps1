#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="F:\erkai999\pc\node_modules\.pnpm\vscode-languageserver@7.0.0\node_modules\vscode-languageserver\bin\node_modules;F:\erkai999\pc\node_modules\.pnpm\vscode-languageserver@7.0.0\node_modules\vscode-languageserver\node_modules;F:\erkai999\pc\node_modules\.pnpm\vscode-languageserver@7.0.0\node_modules;F:\erkai999\pc\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/f/erkai999/pc/node_modules/.pnpm/vscode-languageserver@7.0.0/node_modules/vscode-languageserver/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vscode-languageserver@7.0.0/node_modules/vscode-languageserver/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vscode-languageserver@7.0.0/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

# Support pipeline input
if ($MyInvocation.ExpectingInput) {
  $input | & "$basedir/../../../vscode-languageserver/bin/installServerIntoExtension"   $args
} else {
  & "$basedir/../../../vscode-languageserver/bin/installServerIntoExtension"   $args
}
$env:NODE_PATH=$env_node_path
exit $LASTEXITCODE
