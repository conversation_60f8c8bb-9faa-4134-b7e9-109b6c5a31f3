{"extVersion": "1.28.27", "name": "OANEW-早", "icon": "riGlobalLine", "table": [], "version": "1.28.27", "drawflow": {"nodes": [{"type": "BlockBasic", "data": {"disableBlock": false, "description": "", "type": "manual", "interval": 60, "delay": 5, "date": "", "time": "00:00", "url": "", "shortcut": "", "activeInInput": false, "isUrlRegex": false, "days": [], "contextMenuName": "", "contextTypes": [], "parameters": [], "preferParamsInTab": false, "observeElement": {"selector": "", "baseSelector": "", "matchPattern": "", "targetOptions": {"subtree": false, "childList": true, "attributes": false, "attributeFilter": [], "characterData": false}, "baseElOptions": {"subtree": false, "childList": true, "attributes": false, "attributeFilter": [], "characterData": false}}, "triggers": [{"data": {"disableBlock": false, "description": "", "type": "manual", "interval": 60, "delay": 5, "date": "", "time": "00:00", "url": "", "shortcut": "", "activeInInput": false, "isUrlRegex": false, "days": [], "contextMenuName": "", "contextTypes": [], "parameters": [], "preferParamsInTab": false, "observeElement": {"selector": "", "baseSelector": "", "matchPattern": "", "targetOptions": {"subtree": false, "childList": true, "attributes": false, "attributeFilter": [], "characterData": false}, "baseElOptions": {"subtree": false, "childList": true, "attributes": false, "attributeFilter": [], "characterData": false}}, "triggers": [{"data": {"disableBlock": false, "description": "", "type": "manual", "interval": 60, "delay": 5, "date": "", "time": "00:00", "url": "", "shortcut": "", "activeInInput": false, "isUrlRegex": false, "days": [], "contextMenuName": "", "contextTypes": [], "parameters": [], "preferParamsInTab": false, "observeElement": {"selector": "", "baseSelector": "", "matchPattern": "", "targetOptions": {"subtree": false, "childList": true, "attributes": false, "attributeFilter": [], "characterData": false}, "baseElOptions": {"subtree": false, "childList": true, "attributes": false, "attributeFilter": [], "characterData": false}}}, "type": "manual", "id": "iihnT"}, {"id": "iaGFg", "type": "specific-day", "data": {"days": [{"id": 1, "times": ["08:21:14"]}, {"id": 3, "times": ["08:21:14"]}, {"id": 4, "times": ["08:21:14"]}], "time": "00:00"}}, {"id": "aDkf5", "type": "specific-day", "data": {"days": [{"id": 2, "times": ["08:16:37"]}, {"id": 5, "times": ["08:16:37"]}], "time": "00:00"}}]}, "type": "manual", "id": "v-HtY"}, {"id": "ytYOM", "type": "specific-day", "data": {"days": [{"id": 1, "times": ["16:49:06"]}, {"id": 4, "times": ["16:49:06"]}, {"id": 5, "times": ["16:49:06"]}], "time": "00:00"}}, {"id": "cQzUX", "type": "specific-day", "data": {"days": [{"id": 2, "times": ["16:36:01"]}, {"id": 3, "times": ["16:36:01"]}], "time": "00:00"}}]}, "events": {}, "position": {"x": 50, "y": 300}, "id": "5v_3lhAHhyAZkrZTUsWmc", "label": "trigger"}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "", "url": "http://************:8080/wui/index.html#/", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": false, "updatePrevTab": false, "customUserAgent": false}, "events": {}, "id": "BSjzcDK-8LL4fAe_07d68", "label": "new-tab", "position": {"y": 300, "x": 320}}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "新标签页", "url": "http://************:8080/wui/index.html#/", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": false, "updatePrevTab": false, "customUserAgent": false}, "events": {}, "id": "WjCFiKPhUwRcWGkB64_ei", "label": "new-tab", "position": {"y": 300, "x": 600}}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "登录密码", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 5000, "selector": ".e9login-form-item:nth-child(2) > .e9login-form-label", "markEl": false, "multiple": false}, "events": {}, "id": "I3AkPYdQZCEN8G3LCAMtJ", "label": "event-click", "position": {"y": 300, "x": 880}}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "Text field (userpassword)", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 5000, "selector": "input#userpassword", "markEl": false, "multiple": false, "selected": true, "clearValue": true, "getValue": false, "saveData": false, "dataColumn": "", "selectOptionBy": "value", "optionPosition": "1", "assignVariable": false, "variableName": "", "type": "text-field", "value": "Ghc13579!", "delay": 100, "events": []}, "events": {}, "id": "3d8EjQGRDaiCV36GprBw6", "label": "forms", "position": {"y": 300, "x": 1160}}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 5000, "selector": "input#userpassword", "markEl": false, "multiple": false, "eventName": "change", "eventType": "event", "eventParams": {"bubbles": true, "cancelable": false}}, "events": {}, "id": "KbjUfmGTkCO19i2nZPL5I", "label": "trigger-event", "position": {"y": 300, "x": 1440}}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "登 录", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 5000, "selector": "button#submit", "markEl": false, "multiple": false}, "events": {}, "id": "xJyiy28nm8MRe0CV8Ighe", "label": "event-click", "position": {"y": 450, "x": 50}}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "等待10秒", "time": 10000}, "events": {}, "id": "wait_10s", "label": "delay", "position": {"y": 450, "x": 200}}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "条件判断确定按钮", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 3000, "selector": "a.e<PERSON>obile-modal-ok", "markEl": false, "multiple": false}, "events": {}, "label": "event-click", "id": "qy6ncfx", "position": {"x": 216.4776041568722, "y": 692.8057098758089}}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "考勤", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 5000, "selector": ".singBtn > span", "markEl": false, "multiple": false}, "events": {}, "id": "g0I7ZbloTaGlSM4vNY7Er", "label": "event-click", "position": {"y": 450, "x": 330}}, {"type": "BlockBasic", "data": {"disableBlock": false, "description": "打 卡", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 5000, "selector": ".ant-btn > span", "markEl": false, "multiple": false}, "events": {}, "id": "fsnjXN0UE1f-f34eCH_3_", "label": "event-click", "position": {"y": 450, "x": 610}}], "edges": [{"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "5v_3lhAHhyAZkrZTUsWmc-output-1", "targetHandle": "BSjzcDK-8LL4fAe_07d68-input-1", "source": "5v_3lhAHhyAZkrZTUsWmc", "target": "BSjzcDK-8LL4fAe_07d68", "data": {}, "events": {}, "id": "F0B_paJDFSKHf4pezeuyb", "class": "source-5v_3lhAHhyAZkrZTUsWmc-output-1 targte-BSjzcDK-8LL4fAe_07d68-input-1", "sourceX": 262, "sourceY": 336, "targetX": 300, "targetY": 336}, {"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "BSjzcDK-8LL4fAe_07d68-output-1", "targetHandle": "WjCFiKPhUwRcWGkB64_ei-input-1", "source": "BSjzcDK-8LL4fAe_07d68", "target": "WjCFiKPhUwRcWGkB64_ei", "data": {}, "events": {}, "id": "QLDfHi42bC_Kj2qGHWqC1", "class": "source-BSjzcDK-8LL4fAe_07d68-output-1 targte-WjCFiKPhUwRcWGkB64_ei-input-1", "sourceX": 532, "sourceY": 336, "targetX": 580, "targetY": 336}, {"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "WjCFiKPhUwRcWGkB64_ei-output-1", "targetHandle": "I3AkPYdQZCEN8G3LCAMtJ-input-1", "source": "WjCFiKPhUwRcWGkB64_ei", "target": "I3AkPYdQZCEN8G3LCAMtJ", "data": {}, "events": {}, "id": "NR6mRBgrrLnAv0-0x0D0p", "class": "source-WjCFiKPhUwRcWGkB64_ei-output-1 targte-I3AkPYdQZCEN8G3LCAMtJ-input-1", "sourceX": 812, "sourceY": 336, "targetX": 860, "targetY": 336}, {"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "I3AkPYdQZCEN8G3LCAMtJ-output-1", "targetHandle": "3d8EjQGRDaiCV36GprBw6-input-1", "source": "I3AkPYdQZCEN8G3LCAMtJ", "target": "3d8EjQGRDaiCV36GprBw6", "data": {}, "events": {}, "id": "_iczC-dhfQGf9OwFke6FI", "class": "source-I3AkPYdQZCEN8G3LCAMtJ-output-1 targte-3d8EjQGRDaiCV36GprBw6-input-1", "sourceX": 1092, "sourceY": 336, "targetX": 1140, "targetY": 336}, {"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "3d8EjQGRDaiCV36GprBw6-output-1", "targetHandle": "KbjUfmGTkCO19i2nZPL5I-input-1", "source": "3d8EjQGRDaiCV36GprBw6", "target": "KbjUfmGTkCO19i2nZPL5I", "data": {}, "events": {}, "id": "LSUrNoXEfRaKCYkNxTv5L", "class": "source-3d8EjQGRDaiCV36GprBw6-output-1 targte-KbjUfmGTkCO19i2nZPL5I-input-1", "sourceX": 1372, "sourceY": 336, "targetX": 1420, "targetY": 336}, {"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "KbjUfmGTkCO19i2nZPL5I-output-1", "targetHandle": "xJyiy28nm8MRe0CV8Ighe-input-1", "source": "KbjUfmGTkCO19i2nZPL5I", "target": "xJyiy28nm8MRe0CV8Ighe", "data": {}, "events": {}, "id": "XXCweXW0yKowurDhLou1G", "class": "source-KbjUfmGTkCO19i2nZPL5I-output-1 targte-xJyiy28nm8MRe0CV8Ighe-input-1", "sourceX": 1652, "sourceY": 336, "targetX": 30, "targetY": 486}, {"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "xJyiy28nm8MRe0CV8Ighe-output-1", "targetHandle": "wait_10s-input-1", "source": "xJyiy28nm8MRe0CV8Ighe", "target": "wait_10s", "data": {}, "events": {}, "id": "login-to-wait", "class": "source-xJyiy28nm8MRe0CV8Ighe-output-1 target-wait_10s-input-1", "sourceX": 262, "sourceY": 486, "targetX": 180, "targetY": 486}, {"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "wait_10s-output-1", "targetHandle": "qy6ncfx-input-1", "source": "wait_10s", "target": "qy6ncfx", "data": {}, "events": {}, "id": "wait-to-confirm", "class": "source-wait_10s-output-1 target-qy6ncfx-input-1", "sourceX": 220, "sourceY": 486, "targetX": 196.48182141444263, "targetY": 692.8057098758089}, {"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "qy6ncfx-output-1", "targetHandle": "g0I7ZbloTaGlSM4vNY7Er-input-1", "source": "qy6ncfx", "target": "g0I7ZbloTaGlSM4vNY7Er", "data": {}, "events": {}, "id": "confirm-to-attendance", "class": "source-qy6ncfx-output-1 target-g0I7ZbloTaGlSM4vNY7Er-input-1", "sourceX": 428.48820021515814, "sourceY": 728.8139749973762, "targetX": 310, "targetY": 486}, {"type": "custom", "updatable": true, "selectable": true, "markerEnd": "arrowclosed", "sourceHandle": "g0I7ZbloTaGlSM4vNY7Er-output-1", "targetHandle": "fsnjXN0UE1f-f34eCH_3_-input-1", "source": "g0I7ZbloTaGlSM4vNY7Er", "target": "fsnjXN0UE1f-f34eCH_3_", "data": {}, "events": {}, "id": "KXR40mWpZjox77L9Q185t", "class": "source-g0I7ZbloTaGlSM4vNY7Er-output-1 targte-fsnjXN0UE1f-f34eCH_3_-input-1", "sourceX": 542, "sourceY": 486, "targetX": 590, "targetY": 486}], "position": [16.053327203769697, 72.29425310861495], "zoom": 0.5516607286518791, "viewport": {"x": 16.053327203769697, "y": 72.29425310861495, "zoom": 0.5516607286518791}}, "settings": {"publicId": "", "blockDelay": 0, "saveLog": true, "debugMode": false, "restartTimes": 3, "notification": true, "execContext": "popup", "reuseLastState": false, "inputAutocomplete": true, "onError": "continue-workflow", "executedBlockOnWeb": false, "insertDefaultColumn": false, "defaultColumnName": "column"}, "globalData": "{\n\t\"key\": \"value\"\n}", "description": "", "includedWorkflows": {}}