{"name": "resolve-url", "version": "0.2.1", "description": "Like Node.js’ `path.resolve`/`url.resolve` for the browser.", "author": "<PERSON>", "license": "MIT", "main": "resolve-url.js", "repository": "lydell/resolve-url", "keywords": ["resolve", "url"], "scripts": {"test": "jshint resolve-url.js test/ && testling -u"}, "devDependencies": {"testling": "~1.6.0", "jshint": "~2.4.3", "tape": "~2.5.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "chrome/latest", "firefox/latest", "opera/12", "opera/latest", "safari/5", "iphone/6", "android-browser/4"]}}