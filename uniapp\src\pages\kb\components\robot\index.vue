<template>
    <view class="content">
        <!--  :enable-back-to-top="currentIndex===tabIndex" 在微信小程序上可以多加这一句，因为默认是允许点击返回顶部的，但是这个页面有多个scroll-view，会全部返回顶部，所以需要控制是当前index才允许点击返回顶部 -->
        <!-- 如果当前页已经加载过数据或者当前切换到的tab是当前页，才展示当前页数据（懒加载） -->
        <z-paging
            v-if="firstLoaded || isCurrentPage"
            ref="pagingRef"
            v-model="dataList"
            :auto-clean-list-when-reload="false"
            @query="queryList"
            :fixed="false"
        >
            <!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
            <template #top>
                <!-- 醒目的新增操作区域 -->
                <view class="px-[30rpx] py-[20rpx] bg-white">
                    <view class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-[20rpx] p-[30rpx] mb-[20rpx]">
                        <view class="text-center">
                            <view class="text-[32rpx] font-bold text-[#333] mb-[16rpx]">创建您的专属智能体</view>
                            <view class="text-[26rpx] text-[#666] mb-[30rpx]">让AI助手为您提供个性化服务</view>
                            
                            <view class="flex justify-center space-x-[80rpx]">
                                <!-- 快速创建 -->
                                <view class="flex flex-col items-center" @click="handelShowAdd">
                                    <view class="w-[120rpx] h-[120rpx] robot-create-btn rounded-[20rpx] flex items-center justify-center mb-[20rpx] shadow-lg relative">
                                        <u-icon name="plus-circle" color="#fff" size="40" />
                                        <view class="absolute -top-[6rpx] -right-[6rpx] w-[24rpx] h-[24rpx] bg-[#ff4757] rounded-full flex items-center justify-center">
                                            <text class="text-white text-[20rpx] font-bold">+</text>
                                        </view>
                                    </view>
                                    <view class="text-[28rpx] font-bold text-[#333]">快速创建</view>
                                    <view class="text-[24rpx] text-[#666] mt-[8rpx]">立即开始</view>
                                </view>
                                
                                <!-- 智能体广场 -->
                                <view class="flex flex-col items-center" @click="goToSquare">
                                    <view class="w-[120rpx] h-[120rpx] robot-square-btn rounded-[20rpx] flex items-center justify-center mb-[20rpx] shadow-lg relative">
                                        <u-icon name="star" color="#fff" size="40" />
                                        <view class="absolute -top-[6rpx] -right-[6rpx] w-[24rpx] h-[24rpx] bg-[#ffa502] rounded-full flex items-center justify-center">
                                            <text class="text-white text-[20rpx] font-bold">★</text>
                                        </view>
                                    </view>
                                    <view class="text-[28rpx] font-bold text-[#333]">智能体广场</view>
                                    <view class="text-[24rpx] text-[#666] mt-[8rpx]">发现更多</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                
                <view class="py-[14rpx] px-[30rpx] bg-white">
                    <u-search
                        v-model="keyword"
                        placeholder="请输入关键词搜索"
                        height="72"
                        bg-color="#F7F8F9"
                        @search="pagingRef.reload()"
                        @custom="pagingRef.reload()"
                        @clear="pagingRef.reload()"
                    />
                </view>
            </template>
            <view class="px-[20rpx] pt-[30rpx]">
                <view class="flex flex-wrap mx-[-15rpx]">
                    <view
                        class="item w-[50%] px-[15rpx] mb-[30rpx]"
                        v-for="item in dataList"
                        :key="item.id"
                    >
                        <view
                            class="h-full robot-item"
                            @click="toInfo(item.id)"
                        >
                            <view class="flex justify-end" @click.stop>
                                <view class="flex-1">
                                    <!-- 根据分享状态显示不同标签 -->
                                    <template v-if="item.share_status === 2">
                                        <u-tag type="success" text="已上架" size="mini"></u-tag>
                                    </template>
                                    <template v-else-if="item.share_status === 1">
                                        <u-tag type="warning" text="审核中" size="mini"></u-tag>
                                    </template>
                                    <template v-else-if="item.share_status === 3">
                                        <u-tag type="error" text="已拒绝" size="mini" @click="showRejectReason(item)"></u-tag>
                                    </template>
                                    <template v-else>
                                        <u-tag
                                            type="warning"
                                            v-if="item.is_public"
                                            text="公开"
                                            size="mini"
                                        ></u-tag>
                                        <u-tag
                                            type="primary"
                                            v-else
                                            text="私有"
                                            size="mini"
                                        ></u-tag>
                                    </template>
                                </view>
                                <view @click="showAction(item)">
                                    <u-icon name="more-dot-fill" />
                                </view>
                            </view>
                            <view class="flex flex-col items-center">
                                <u-image
                                    :src="item.image"
                                    width="100"
                                    height="100"
                                    shape="circle"
                                ></u-image>
                                <view
                                    class="text-2xl mt-[22rpx] mb-[16px] line-clamp-3"
                                    >{{ item.name }}</view
                                >
                                <view
                                    class="text-muted leading-[40rpx] h-[120rpx] line-clamp-3"
                                >
                                    {{
                                        item.intro || '这个智能体还没介绍呢'
                                    }}</view
                                >
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </z-paging>
        <AddBtn @click="handelShowAdd" />
        <AddPopup v-model="showAdd" @update="pagingRef.reload()" />
        <u-action-sheet
            :list="menuOptions"
            v-model="actionState.show"
            @click="menuAction"
        ></u-action-sheet>
        <share-popup
            v-if="showShare"
            ref="shareRef"
            @close="showShare = false"
            @success="shareSuccess"
        ></share-popup>
    </view>
</template>

<script setup lang="ts">
import { watch, shallowRef, ref, nextTick, reactive } from 'vue'
import { getRobotLists, delRobot, cancelShare } from '@/api/robot'
import AddBtn from '../add-btn.vue'
import AddPopup from './add-popup.vue'
import SharePopup from './share-popup.vue'
import { useRouter } from 'uniapp-router-next'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'

const props = defineProps<{
    tabIndex: number
    currentIndex: number
}>()

const userStore = useUserStore()
const appStore = useAppStore()
const dataList = ref<any[]>([])
const pagingRef = shallowRef()
const firstLoaded = ref(false)
const isCurrentPage = ref(false)
const showAdd = ref(false)
const keyword = ref('')
const actionState = reactive({
    show: false,
    currentId: -1,
    isShare: -1 as number | undefined,
    isPublic:  -1 as number | undefined,
    shareStatus: 0 as number | undefined,
    verifyResult: '' as string | undefined
})
const menuOptions = shallowRef([
    {
        value: 'begin',
        text: '开始对话'
    },
    {
        value: 'release',
        text: '发布智能体'
    },
    {
        value: 'dialogue',
        text: '对话数据'
    },
    {
        value: 'share',
        text: '分享至广场',
        show: appStore.getSquareConfig.robot_award?.is_open
    },
    {
        value: 'delete',
        text: '删除'
    }
])

const showShare = ref<boolean>(false)
const shareRef = shallowRef<any>(null)
const sharedIds = ref<number[]>([])

const showAction = (item: any) => {
    actionState.show = true
    actionState.currentId = item.id
    actionState.isPublic = item.is_public
    actionState.isShare = item.is_share
    actionState.shareStatus = item.share_status || 0
    actionState.verifyResult = item.verify_result || ''
    // 根据分享状态设置菜单文本
    if (item.share_status === 2) {
        // 已通过审核，显示取消发布
        menuOptions.value[3].text = '取消发布广场'
    } else if (item.share_status === 1) {
        // 审核中，显示审核状态
        menuOptions.value[3].text = '审核中...'
    } else if (item.share_status === 3) {
        // 审核拒绝，显示重新提交
        menuOptions.value[3].text = '重新提交'
    } else {
        // 未分享，显示分享选项
        menuOptions.value[3].text = '分享至广场'
    }
}

const showRejectReason = (item: any) => {
    if (item.verify_result) {
        uni.showModal({
            title: '审核拒绝原因',
            content: item.verify_result,
            showCancel: false
        })
    } else {
        uni.$u.toast('暂无拒绝原因说明')
    }
}

const removeRobot = async (id: number) => {
    const { cancel } = await uni.showModal({
        title: '温馨提示',
        content: '确定删除？'
    })
    if (cancel) return
    await delRobot({ id })
    const index = dataList.value.findIndex((item) => item.id == id)
    dataList.value.splice(index, 1)
}

const shareAgent = async (id: number, shareStatus?: number, verifyResult?: string) => {
    // 根据分享状态决定是否可以分享
    if (shareStatus === 1) {
        uni.$u.toast('智能体正在审核中，请等待审核完成')
        return
    }
    if (shareStatus === 2) {
        uni.$u.toast('智能体已在广场中，请先取消发布再重新分享')
        return
    }
    if (shareStatus === 3) {
        // 审核拒绝的情况下，可以重新分享
        uni.$u.toast('智能体之前被拒绝，正在重新提交审核')
    }
    
    showShare.value = true
    await nextTick()
    setTimeout(() => shareRef.value.open(id, verifyResult), 50)
}

const shareSuccess = (value: number) => {
    sharedIds.value.push(value)
    reload()
}

const menuAction = async (index: number) => {
    const action = menuOptions.value[index].value
    const id = actionState.currentId
    switch (action) {
        case 'delete': {
            removeRobot(id)
            // reload()
            break
        }
        case 'dialogue':
        case 'release': {
            toInfo(id, action)
            break
        }
        case 'share': {
            if (actionState.shareStatus === 2) {
                // 已上架，执行取消发布
                await cancelShare({ id })
            } else if (actionState.shareStatus === 1) {
                // 审核中，不允许操作
                uni.$u.toast('智能体正在审核中，请等待审核完成')
                return
            } else {
                // 未分享或审核拒绝，执行分享
                shareAgent(id, actionState.shareStatus, actionState.verifyResult)
            }
            reload()
            break
        }
        case 'begin': {
            router.navigateTo({
                path: '/packages/pages/robot_chat/robot_chat',
                query: {
                    id
                }
            })
        }
    }
}
const router = useRouter()
const toInfo = (id: number, type?: string) => {
    router.navigateTo({
        path: '/packages/pages/robot_info/robot_info',
        query: {
            id,
            type: type || null
        }
    })
}
const handelShowAdd = async () => {
    if (!userStore.isLogin) return router.navigateTo('/pages/login/login')
    if (userStore.userInfo.robot_num <= 0) {
        if (!appStore.getIsShowRecharge) {
            uni.$u.toast('智能体数量已用完。请联系客服增加')
        } else {
            const { cancel } = await uni.showModal({
                title: '温馨提示',
                content: '智能体数量已用完，请前往充值'
            })
            if (cancel) return
            router.navigateTo({
                path: '/packages/pages/recharge/recharge'
            })
        }
        return Promise.reject()
    }
    showAdd.value = true
}

const goToSquare = () => {
    // 跳转到智能体广场
    router.navigateTo({
        path: '/packages/pages/robot_square/robot_square'
    })
}

const queryList = async (pageNo: number, pageSize: number) => {
    try {
        const { lists = [] } = await getRobotLists({
            page_size: pageSize,
            page_no: pageNo,
            keyword: keyword.value
        })

        pagingRef.value?.complete(lists)
    } catch (error) {
        pagingRef.value?.complete(false)
    }
}

const reload = () => {
    nextTick(() => {
        // 刷新列表数据(如果不希望列表pageNo被重置可以用refresh代替reload方法)
        pagingRef.value && pagingRef.value.refresh()
    })
}

watch(
    () => props.currentIndex,
    (newVal) => {
        if (newVal === props.tabIndex) {
            // 懒加载，当滑动到当前的item时，才去加载
            if (!firstLoaded.value) {
                // 这里需要延迟渲染z-paging的原因是为了避免在一些平台上立即渲染可能引发的底层报错问题
                nextTick(() => {
                    setTimeout(() => {
                        isCurrentPage.value = true
                    }, 100)
                })
            }
        }
    },
    {
        immediate: true
    }
)

defineExpose({
    reload
})
</script>

<style>
/* 注意:父节点需要固定高度，z-paging的height:100%才会生效 */
.content {
    height: 100%;
}

.robot-item {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0 0 16rpx #3c5efd0f;
    padding: 20rpx 20rpx 30rpx;
}

.add-float {
}

/* 渐变背景样式 */
.bg-gradient-to-r {
    background: linear-gradient(90deg, #f0f9ff 0%, #faf5ff 100%);
}

.bg-gradient-to-br {
    background: linear-gradient(135deg, var(--from-color), var(--to-color));
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.space-x-\[80rpx\] > view:not(:last-child) {
    margin-right: 80rpx;
}

/* 自定义渐变色 */
.robot-create-btn {
    background: linear-gradient(135deg, #10b981, #059669);
}
.robot-square-btn {
    background: linear-gradient(135deg, #f97316, #ea580c);
}
</style>
