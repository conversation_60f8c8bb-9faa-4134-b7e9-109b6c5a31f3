page {
  background-color: $u-bg-color;
  font-size: 28rpx;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: theme('fontFamily.sans')
}

uni-modal {
  z-index: 999999 !important;
}

button::after {
  border: initial;
}

button[type='primary'] {
  background-color: var(--color-primary) !important;
}


.uni-scroll-view,scroll-view {
  -webkit-overflow-scrolling: initial;
}
view {
  word-break: break-all;
}