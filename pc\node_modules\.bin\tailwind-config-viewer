#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/tailwind-config-viewer@1.7.3_tailwindcss@3.3.7/node_modules/tailwind-config-viewer/cli/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/tailwind-config-viewer@1.7.3_tailwindcss@3.3.7/node_modules/tailwind-config-viewer/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/tailwind-config-viewer@1.7.3_tailwindcss@3.3.7/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/tailwind-config-viewer@1.7.3_tailwindcss@3.3.7/node_modules/tailwind-config-viewer/cli/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/tailwind-config-viewer@1.7.3_tailwindcss@3.3.7/node_modules/tailwind-config-viewer/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/tailwind-config-viewer@1.7.3_tailwindcss@3.3.7/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../tailwind-config-viewer/cli/index.js" "$@"
else
  exec node  "$basedir/../tailwind-config-viewer/cli/index.js" "$@"
fi
