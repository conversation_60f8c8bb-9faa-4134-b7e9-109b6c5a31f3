{"name": "posthtml", "version": "0.9.2", "description": "HTML/XML processor", "keywords": ["html", "xml", "postproccessor", "parser", "transform", "transformations", "manipulation", "preprocessor", "processor"], "main": "lib", "engines": {"node": ">=0.10.0"}, "dependencies": {"posthtml-parser": "^0.2.0", "posthtml-render": "^1.0.5"}, "devDependencies": {"chai": "^3.0.0", "chai-as-promised": "^6.0.0", "chai-subset": "^1.1.0", "conventional-changelog-cli": "^1.0.0", "es6-promise": "^4.0.5", "istanbul": "^0.4.2", "jsdoc-to-markdown": "^2.0.0", "mocha": "^3.1.2", "mversion": "^1.10.0", "object.assign": "^4.0.3", "standard": "^8.3.0"}, "scripts": {"lint": "standard", "test": "npm run lint && mocha -R dot && npm run cover", "clean": "rm -rf coverage jsdoc-api", "cover": "istanbul cover --report text --report html --report lcov node_modules/mocha/bin/_mocha -- -R tap", "docs:api": "jsdoc2md lib/api.js > docs/api.md", "docs:core": "jsdoc2md lib/index.js > docs/core.md", "release:patch": "mversion patch", "release:minor": "mversion minor", "release:major": "mversion major"}, "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/posthtml/posthtml", "repository": {"type": "git", "url": "git://github.com/posthtml/posthtml.git"}, "bugs": {"url": "https://github.com/posthtml/posthtml/issues"}, "license": "MIT"}