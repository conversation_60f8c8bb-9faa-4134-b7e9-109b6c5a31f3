-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- 主机： 172.20.0.2
-- 生成日期： 2025-06-04 14:06:03
-- 服务器版本： 5.7.29
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `chatmoney`
--

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_revenue_config`
--

CREATE TABLE `cm_kb_robot_revenue_config` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `is_enable` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否启用分成功能：0-关闭 1-开启',
  `share_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '分享者分成比例(百分比): 0.00-100.00',
  `platform_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT '100.00' COMMENT '平台保留比例(百分比): 0.00-100.00',
  `min_revenue` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.01' COMMENT '最小分成金额(电力值)',
  `settle_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '结算方式：1-实时结算 2-每日结算',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能体分成收益配置表';

--
-- 转存表中的数据 `cm_kb_robot_revenue_config`
--

INSERT INTO `cm_kb_robot_revenue_config` (`id`, `is_enable`, `share_ratio`, `platform_ratio`, `min_revenue`, `settle_type`, `create_time`, `update_time`) VALUES
(1, 1, '30.00', '70.00', '0.01', 1, 1748843505, 1749014916);

--
-- 转储表的索引
--

--
-- 表的索引 `cm_kb_robot_revenue_config`
--
ALTER TABLE `cm_kb_robot_revenue_config`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_revenue_config`
--
ALTER TABLE `cm_kb_robot_revenue_config`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID', AUTO_INCREMENT=2;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
