!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).lime={})}(this,(function(t){"use strict";function e(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e=function(){return t};var t={},r=Object.prototype,n=r.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function s(t,e,r,n){var o=Object.create((e&&e.prototype instanceof h?e:h).prototype),a=new M(n||[]);return o._invoke=function(t,e,r){var n="suspendedStart";return function(o,a){if("executing"===n)throw Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return S()}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var c=x(i,r);if(c){if(c===l)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=f(t,e,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===l)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(t,r,a),o}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var l={};function h(){}function p(){}function y(){}var d={};u(d,a,(function(){return this}));var v=Object.getPrototypeOf,m=v&&v(v(E([])));m&&m!==r&&n.call(m,a)&&(d=m);var g=y.prototype=h.prototype=Object.create(d);function w(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function r(o,a,i,c){var u=f(t[o],t,a);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==typeof l&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(l).then((function(t){s.value=t,i(s)}),(function(t){return r("throw",t,i,c)}))}c(u.arg)}var o;this._invoke=function(t,n){function a(){return new e((function(e,o){r(t,n,e,o)}))}return o=o?o.then(a,a):a()}}function x(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,x(t,e),"throw"===e.method))return l;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var n=f(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,l;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,l):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,l)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function E(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:S}}function S(){return{value:void 0,done:!0}}return p.prototype=y,u(g,"constructor",y),u(y,"constructor",p),p.displayName=u(y,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,c,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},w(b.prototype),u(b.prototype,i,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new b(s(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},w(g),u(g,c,"Generator"),u(g,a,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=E,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,n){return i.type="throw",i.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return r("end");if(this.prev>=a.tryLoc){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(a.catchLoc>this.prev)return r(a.catchLoc,!0);if(a.finallyLoc>this.prev)return r(a.finallyLoc)}else if(c){if(a.catchLoc>this.prev)return r(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(a.finallyLoc>this.prev)return r(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(this.prev>=o.tryLoc&&n.call(o,"finallyLoc")&&o.finallyLoc>this.prev){var a=o;break}}a&&("break"===t||"continue"===t)&&e>=a.tryLoc&&a.finallyLoc>=e&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,l):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),L(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;L(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:E(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function r(t,e,r,n,o,a,i){try{var c=t[a](i),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var r=0;e.length>r;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,c=!1;try{for(r=r.call(t);!(i=(n=r.next()).done)&&(a.push(n.value),!e||a.length!==e);i=!0);}catch(t){c=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(c)throw o}}return a}(t,e)||c(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){if(t){if("string"==typeof t)return u(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);e>r;r++)n[r]=t[r];return n}var s={color:"rgba(0,0,0,.15)",fontSize:16,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"},f=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n(this,t),this.canvas=null,this.ctx=null,this.options={pixelRatio:1},this.props={rotate:-22,baseSize:2,fontGap:3,gap:[30,30]},this.gap={gapX:0,gapY:0,gapXCenter:0,gapYCenter:0,offsetLeft:0,offsetTop:0},this.font=s,this.appendWatermark=function(){},this.createImage=function(){return new Image},e?this.canvas=e:"undefined"!=typeof window&&(this.canvas=document.createElement("canvas"));var o=r.appendWatermark,a=r.createImage;o&&(this.appendWatermark=o),a&&(this.createImage=a),Object.assign(this.options,r)}var c,u,f,l,h;return c=t,u=[{key:"rotateWatermark",value:function(t,e,r,n){t.translate(e,r),t.rotate(Math.PI/180*Number(n)),t.translate(-e,-r)}},{key:"calcFont",value:function(){Object.assign(this.font,s,this.props.font||{})}},{key:"calcGap",value:function(){var t,e,r=this.props,n=r.gap,o=r.offset,i=a(void 0===n?[20,20]:n,2),c=i[0],u=i[1],s=c/2,f=u/2,l=null!==(t=null==o?void 0:o[0])&&void 0!==t?t:s,h=null!==(e=null==o?void 0:o[1])&&void 0!==e?e:f;this.gap={gapX:c,gapY:u,gapXCenter:s,gapYCenter:f,offsetLeft:l,offsetTop:h}}},{key:"getPixelRatio",value:function(){var t=this.options.pixelRatio;return t||("undefined"!=typeof window?window.devicePixelRatio:1)}},{key:"calcRotateMarkSize",value:function(t,e){var r=this.props.rotate;if(!r)return[t,e];var n=r*Math.PI/180,o=-t/2*Math.cos(n)+e/2*Math.sin(n),a=-t/2*Math.sin(n)-e/2*Math.cos(n),i=t/2*Math.cos(n)+e/2*Math.sin(n),c=t/2*Math.sin(n)-e/2*Math.cos(n),u=t/2*Math.cos(n)-e/2*Math.sin(n),s=t/2*Math.sin(n)+e/2*Math.cos(n),f=-t/2*Math.cos(n)-e/2*Math.sin(n),l=-t/2*Math.sin(n)+e/2*Math.cos(n);return[Math.round(Math.max(o,i,u,f)-Math.min(o,i,u,f)),Math.round(Math.max(a,c,s,l)-Math.min(a,c,s,l))]}},{key:"getMarkSize",value:function(t){var e=this.props,r=e.width,n=e.height,o=e.content,c=e.fontGap,u=this.font,s=u.fontSize,f=u.fontFamily,l=120,h=64;if(!e.image&&t.measureText){t.font="".concat(Number(s),"px ").concat(f);var p=Array.isArray(o)?o:[o],y=p.map((function(e){return t.measureText(e).width}));l=Math.ceil(Math.max.apply(Math,i(y)));var d=a(this.calcRotateMarkSize(l,h=Number(s)*p.length+(p.length-1)*c),2);l=d[0],h=d[1]}return[r||l,n||h]}},{key:"fillTexts",value:function(t,e,r,n,o){var a=this.getPixelRatio(),i=this.font,c=i.fontFamily,u=i.fontWeight,s=i.fontStyle,f=i.color,l=this.props,h=l.content,p=l.fontGap,y=Number(i.fontSize)*a;t.font="".concat(s," normal ").concat(u," ").concat(y,"px/").concat(o,"px ").concat(c),t.fillStyle=f,t.textAlign="center",t.textBaseline="top",t.translate(n/2,0);var d=Array.isArray(h)?h:[h];null==d||d.forEach((function(n,o){t.fillText(null!=n?n:"",e,r+o*(y+p*a))}))}},{key:"toDataURL",value:function(t){try{var e=t.toDataURL();return"string"==typeof e?Promise.resolve(e):e}catch(t){return console.warn(t),Promise.reject(t)}}},{key:"drawText",value:function(t,e,r,n,o,a,i,c,u,s,f){var l=this,h=this.props.rotate;this.fillTexts(e,r,n,o,a),e.restore(),this.rotateWatermark(e,i,c,h),this.fillTexts(e,u,s,o,a),this.draw(e),this.toDataURL(t).then((function(t){return l.appendWatermark(t,f,l.gap)}))}},{key:"draw",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};t.draw?t.draw(!1,e):e()}},{key:"render",value:(l=e().mark((function t(r){var n,o,i,c,u,s,f,l,h,p,y,d,v,m,g,w,b,x,k,L,M,E,S,j,T,O,P,I=this;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(Object.assign(this.props,r||{}),this.calcFont(),this.calcGap(),n=this.canvas){t.next=6;break}return t.abrupt("return");case 6:if(o=n.getContext("2d"),this.ctx=o,!o){t.next=33;break}if(i=this.getPixelRatio(),c=this.getMarkSize(o),u=a(c,2),d=(y=this.props).rotate,v=y.image,w=((p=(l=this.gap).gapY)+(f=u[1]))*i,n.width=(g=((h=l.gapX)+(s=u[0]))*i)*(m=y.baseSize),n.height=w*m,!("draw"in o)){t.next=20;break}return t.next=20,new Promise((function(t){return setTimeout(t,100)}));case 20:S=(b=h*i/2)+g,j=(x=p*i/2)+w,T=(M=((k=s*i)+h*i)/2)+g,O=(E=((L=f*i)+p*i)/2)+w,o.save(),this.rotateWatermark(o,M,E,d),v?((P=this.createImage(this.canvas)).onload=function(){var t="path"in P?P.path:P;o.drawImage(t,b,x,k,L),o.restore(),I.rotateWatermark(o,T,O,d),o.drawImage(t,S,j,k,L),I.draw(o,(function(){return I.toDataURL(n).then((function(t){return I.appendWatermark(t,s,I.gap)}))}))},P.onerror=function(){return I.drawText(n,o,b,x,k,L,T,O,S,j,s)},P.crossOrigin="anonymous",P.referrerPolicy="no-referrer",P.src=v):this.drawText(n,o,b,x,k,L,T,O,S,j,s);case 33:case"end":return t.stop()}}),t,this)})),h=function(){var t=this,e=arguments;return new Promise((function(n,o){var a=l.apply(t,e);function i(t){r(a,n,o,i,c,"next",t)}function c(t){r(a,n,o,i,c,"throw",t)}i(void 0)}))},function(t){return h.apply(this,arguments)})}],u&&o(c.prototype,u),f&&o(c,f),Object.defineProperty(c,"prototype",{writable:!1}),t}();t.Watermark=f,Object.defineProperty(t,"__esModule",{value:!0})}));
