-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- 主机： 172.20.0.2
-- 生成日期： 2025-05-22 18:46:41
-- 服务器版本： 5.7.29
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `chatmoney`
--

-- --------------------------------------------------------

--
-- 表的结构 `cm_example_category`
--

CREATE TABLE `cm_example_category` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '类别名称',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例库类别表';

--
-- 转存表中的数据 `cm_example_category`
--

INSERT INTO `cm_example_category` (`id`, `name`, `sort`, `status`, `create_time`, `update_time`, `delete_time`) VALUES
(1, '111', 0, 1, 1747830879, 1747830879, NULL),
(2, '111', 0, 1, 1747886942, 1747886942, NULL),
(3, '222', 0, 1, 1747887305, 1747887305, NULL),
(4, '123123123', 0, 1, 1747901961, 1747901961, NULL),
(5, '44', 0, 1, 1747902005, 1747902005, NULL),
(6, '5555', 0, 1, 1747903085, 1747903085, NULL);

--
-- 转储表的索引
--

--
-- 表的索引 `cm_example_category`
--
ALTER TABLE `cm_example_category`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `cm_example_category`
--
ALTER TABLE `cm_example_category`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
