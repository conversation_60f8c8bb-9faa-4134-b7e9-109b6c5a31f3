<template>
    <view class="chat-container">
        <!-- VIP限制提示 -->
        <view v-if="showVipLimitTip" class="fixed top-[20rpx] left-1/2 transform -translate-x-1/2 z-50">
            <view class="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-300 rounded-[12rpx] p-[20rpx] shadow-lg text-blue-700 max-w-[600rpx]">
                <view class="font-semibold mb-[12rpx] flex items-center text-[24rpx]">
                    <text class="text-blue-500 mr-[8rpx] text-[28rpx]">💡</text>
                    会员免费额度提醒
                </view>
                <view class="text-[22rpx] leading-[32rpx]">{{ vipLimitTip }}</view>
            </view>
        </view>
        <!-- VIP超额付费弹窗 -->
        <view v-if="showVipOverLimitDialog && isPC" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
            <view class="bg-white rounded-[16rpx] p-[40rpx] shadow-xl max-w-[600rpx] w-[80vw] text-center">
                <view class="font-bold text-[28rpx] mb-[24rpx] text-red-600 flex items-center justify-center">
                    <text class="mr-[8rpx] text-[32rpx]">⚠️</text>
                    超额付费使用提醒
                </view>
                <view class="text-[24rpx] text-gray-700 mb-[32rpx]">
                    {{ vipOverLimitTip }}
                </view>
                <button class="bg-blue-500 text-white rounded-[8rpx] px-[32rpx] py-[12rpx] text-[24rpx]" @click="closeVipOverLimitDialog">我知道了</button>
            </view>
        </view>
        <!-- ... existing code ... -->
    </view>
</template>

<script setup lang="ts">
// ... existing code ...
const showVipLimitTip = ref(false)
const vipLimitTip = ref('')
const showVipOverLimitDialog = ref(false)
const vipOverLimitTip = ref('该模型短时间内使用频次过高，已明显超过正常使用需求，平台将对该模型免费使用进行限制，继续使用将正常扣费，建议您使用其他会员免费模型。此限制将于今日24:00解除，届时您可继续免费使用该模型。')
const isPC = ref(false)

const closeVipOverLimitDialog = () => {
    showVipOverLimitDialog.value = false
    // 关闭弹窗后刷新页面
    window.location.reload()
}

// 监听消息发送前的VIP限制检查
const checkVipLimit = (modelInfo: any) => {
    if (modelInfo?.vip_limit_info?.is_exceeded) {
        // 顶部提示
        showVipLimitTip.value = true
        vipLimitTip.value = '该模型短时间内使用频次过高，已明显超过正常使用需求，平台将对该模型免费使用进行限制，继续使用将正常扣费，建议您使用其他会员免费模型。此限制将于今日24:00解除，届时您可继续免费使用该模型。'
        setTimeout(() => {
            showVipLimitTip.value = false
        }, 3000)
        // 弹窗提示
        showVipOverLimitDialog.value = true
        vipOverLimitTip.value = '该模型短时间内使用频次过高，已明显超过正常使用需求，平台将对该模型免费使用进行限制，继续使用将正常扣费，建议您使用其他会员免费模型。此限制将于今日24:00解除，届时您可继续免费使用该模型。'
    }
}

// 在发送消息前检查VIP限制
const handleSend = async () => {
    if (!currentModel.value) return
    checkVipLimit(currentModel.value)
    // ... 原有的发送消息逻辑 ...
}

onMounted(() => {
    // 检测是否为PC端
    isPC.value = window.innerWidth > 768
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        isPC.value = window.innerWidth > 768
    })
})

onUnmounted(() => {
    // 移除监听器
    window.removeEventListener('resize', () => {
        isPC.value = window.innerWidth > 768
    })
})
// ... existing code ...
</script>

<style lang="scss">
// ... existing code ...
</style> 