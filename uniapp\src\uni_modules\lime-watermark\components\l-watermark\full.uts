import View from 'android.view.View';
import Runnable from 'java.lang.Runnable';
import FrameLayout from "android.widget.FrameLayout";
import Color from "android.graphics.Color";
import ViewGroup from "android.view.ViewGroup";
import Paint from "android.graphics.Paint";
import Canvas from "android.graphics.Canvas";
import Context from 'android.content.Context';

import { WatermarkProps, WatermarkFont, WatermarkGAP } from './type'
const defaultFont : WatermarkFont = {
	color: 'rgba(0,0,0,0.15)',
	fontSize: 16,
	fontWeight: 'normal',
	fontStyle: 'normal',
	fontFamily: 'sans-serif',
}

function shallowMerge(obj1 : WatermarkFont | null, obj2 : WatermarkFont = defaultFont) : WatermarkFont {
	if (obj1 != null) {
		const result = obj1;
		for (const key in obj2) {
			if (obj1[key] == null) {
				result[key] = obj2[key];
			}
		}
		return result
	} else {
		return obj2
	}
}

function shallowMerge(obj1 : WatermarkProps | null, obj2 : WatermarkProps) : WatermarkProps {
	if (obj1 != null) {
		const result = obj1;
		for (const key in obj2) {
			if (obj1[key] == null) {
				result[key] = obj2[key];
			}
		}
		return result
	} else {
		return obj2
	}
}


function parseCssColor(cssColor: string): number {
    if (cssColor.startsWith("rgba(") && cssColor.endsWith(")")) {
        const matchs = cssColor.match(/(\d+\.)?\d+/g) //?? []
		if (Array.isArray(matchs) && matchs!.length >= 3) {
            try {
                const red = parseInt(matchs[0]!);
                const green = parseInt(matchs[1]!);
                const blue = parseInt(matchs[2]!);
                const alpha = matchs.length >= 4 ? parseFloat(matchs[3]!) : 1;
                return Color.argb((alpha * 255).toInt(), red.toInt(), green.toInt(), blue.toInt());
            } catch (e) {
				console.log('parseCssColor')
                // 无法解析颜色格式
				return Color.TRANSPARENT;
            }
        }
    } else {
		return Color.parseColor(cssColor);
	}
    return Color.TRANSPARENT; // 返回透明颜色
}



class WatermarkView extends View {
	paint : Paint
	props : WatermarkProps = {
		rotate: -22,
		baseSize: 2,
		fontGap: 3,
		gap: [30, 30]
	} as WatermarkProps
	_gap : WatermarkGAP = {
		gapX: 0,
		gapY: 0,
		gapXCenter: 0,
		gapYCenter: 0,
		offsetLeft: 0,
		offsetTop: 0,
	} as WatermarkGAP
	font : WatermarkFont = defaultFont
	dpr: number = 1
	public constructor(context ?: Context) {
		super(context);
		this.paint = new Paint();
		this.dpr = uni.getSystemInfoSync().pixelRatio
	}
	override onDraw(canvas : Canvas) {
		if(this.props.content == null) {
			return
		};
		this.calcGap()
		const contents = this.props.content as string[]
		const [markWidth, markHeight] = this.getMarkSize()
		const {fontSize = 16, color} = this.font
		const { gapX, gapY } = this._gap
		const width = this.getWidth();
		const height = this.getHeight();
		const [canvasWidth, canvasHeight] = this.rotateRectangle(width, height)
		const offsetX = (canvasWidth - width)/2 * -1
		const offsetY = (canvasHeight - height)/2* -1
		const cols = Math.ceil(canvasWidth / (markWidth + gapX))
		const rows = Math.ceil(canvasHeight / (markHeight + gapY))
		
		const _color = parseCssColor(color!)
		this.paint.setColor(_color.toInt());
		this.paint.setTextSize((fontSize * this.dpr).toFloat());
		this.paint.setTextAlign(Paint.Align.CENTER);
		canvas.save();
		canvas.rotate(this.props.rotate!.toFloat(), (width/2).toFloat(), (height/2).toFloat())
		for (let i = 0; i < rows; i++) {
			for (let j = 0; j < cols; j++) {
				const x = j * (markWidth + gapX)  + (markWidth + gapX) / 2 + offsetX
				const y = i * (markHeight + gapY) + (markHeight + gapY) / 2 + offsetY
				contents.forEach((text, index) =>{
					const _y = y + index * (fontSize + this.props.fontGap)
					canvas.drawText(text, (x*this.dpr).toFloat(), (_y*this.dpr).toFloat(), this.paint);
				})
			}
		}
		canvas.restore();
	}
	rotateRectangle(width: number, height:number):number[]{
		const rotate = this.props.rotate as number
		const angleRadians = rotate * Math.PI / 180;
		const sin = Math.sin(angleRadians);
		const cos = Math.cos(angleRadians);
		const rotatedWidth = Math.abs(width * cos) + Math.abs(height * sin);
		const rotatedHeight = Math.abs(width * sin) + Math.abs(height * cos);
		return [rotatedWidth, rotatedHeight]
	}
	getMarkSize():number[]{
		const { content, fontGap } = this.props;
		const { fontSize } = this.font
		const contents = content as string[]
		const paint = new Paint()
		paint.setTextSize((fontSize!).toFloat())
		
		let defaultWidth = 0
		contents.forEach(text =>{
			const width = paint.measureText(text)
			if(width > defaultWidth){
				defaultWidth = width
			}
		})
		let defaultHeight = fontSize * contents.length + (contents.length - 1) * fontGap;
		return [defaultWidth, defaultHeight]
	}
	calcGap() {
		const { gap = [20, 20], offset } = this.props
		const [gapX, gapY] = gap;
		const gapXCenter = gapX / 2;
		const gapYCenter = gapY / 2;
		const offsetLeft = offset != null ? offset[0] : gapXCenter;
		const offsetTop = offset != null && offset.length > 1? offset[1]: gapYCenter;
		this._gap = {
			gapX,
			gapY,
			gapXCenter,
			gapYCenter,
			offsetLeft,
			offsetTop
		} as WatermarkGAP
	}
	setContent(content: string){
		this.props.content = [content]
	}
	setContent(content: string[]){
		this.props.content = content
	}
	setOptions(options: WatermarkProps){
		this.props = shallowMerge(options, this.props)
		this.font = shallowMerge(options.font, this.font)
	}
	set color(color: string){
		this.font.color = color
	}
	get color():string{
		return this.font.color??''
	}
	
	set fontSize(fontSize: number){
		this.font.fontSize = fontSize
	}
	get fontSize():number{
		return this.font.fontSize ?? 0
	}
	
	set rotate(rotate: number){
		this.props.rotate = rotate
	}
	get rotate():number{
		return this.props.rotate ?? 0
	}
	set gap(gap: number[]){
		this.props.gap = gap
	}
	get gap():number[]{
		return this.props.gap ?? [20,20]
	}
}


/**
 * 实现一个添加view的 Runnable类
 * 用法说明：https://uniapp.dcloud.net.cn/plugin/uts-plugin.html#%E5%8C%BF%E5%90%8D%E5%86%85%E9%83%A8%E7%B1%BB
 */
class AddUIRunnable implements Runnable {
	view: WatermarkView
	constructor(){
		super()
		const view = new WatermarkView(UTSAndroid.getUniActivity()!)
		this.view = view
	}
	override run() : void {
		try{
			this.view.setTag("limeWatermarkTag");
			let decorView = UTSAndroid.getUniActivity()!.window.decorView;
			let frameContent = decorView.findViewById<FrameLayout>(android.R.id.content)
			let layoutParam = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
			frameContent.addView(this.view, layoutParam)
		}catch(e){
			console.warn('lime watermar', e)
			//TODO handle the exception
		}
		
	}
};

/**
 * 实现一个移除view的 Runnable类
 * 用法说明：https://uniapp.dcloud.net.cn/plugin/uts-plugin.html#%E5%8C%BF%E5%90%8D%E5%86%85%E9%83%A8%E7%B1%BB
 */
class RemoveUIRunnable extends Runnable {
	
	override run() : void {
		try{
			let decorView = UTSAndroid.getUniActivity()!.getWindow().getDecorView();
			let frameContent = decorView.findViewById<FrameLayout>(android.R.id.content)
			let targetTV = frameContent.findViewWithTag<WatermarkView>("limeWatermarkTag")
			if(targetTV == null) return
			frameContent.removeView(targetTV)
		}catch(e){
			console.warn('lime watermark', e)
			//TODO handle the exception
		}
		
	}
};

/**
 * 实现添加view实例至decorview
 * 
 */

export function createWatermark():WatermarkView {
	removeWatermark()
	let uiRunable = new AddUIRunnable();
	UTSAndroid.getUniActivity()!.runOnUiThread(uiRunable)
	return uiRunable.view;
}
/**
 * 实现从decorview上移除指定view
 */
export function removeWatermark() {
	var uiRunable = new RemoveUIRunnable();
	UTSAndroid.getUniActivity()!.runOnUiThread(uiRunable)
}