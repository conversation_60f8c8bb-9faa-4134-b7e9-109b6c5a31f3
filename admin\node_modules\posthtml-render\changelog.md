## 1.4.0 (2020-11-18)

* test: quoteStyle option ([18d31c0](https://github.com/posthtml/posthtml-render/commit/18d31c0))
* test: remove write test ([3c9b49e](https://github.com/posthtml/posthtml-render/commit/3c9b49e))
* feat: add type definition for quoteStyle ([3ee8d58](https://github.com/posthtml/posthtml-render/commit/3ee8d58))
* feat: implement quoteStyle option ([903300f](https://github.com/posthtml/posthtml-render/commit/903300f))
* docs: add quoteStyle option ([38b2b04](https://github.com/posthtml/posthtml-render/commit/38b2b04))
* Delete test.html ([0dea643](https://github.com/posthtml/posthtml-render/commit/0dea643))
* Update funding.yml ([86f2c9d](https://github.com/posthtml/posthtml-render/commit/86f2c9d))



## 1.3.0 (2020-11-12)

* 1.3.0 ([334705d](https://github.com/posthtml/posthtml-render/commit/334705d))
* ci: package lock for ci ([4c85131](https://github.com/posthtml/posthtml-render/commit/4c85131))
* ci: remove windows ([34aa3c2](https://github.com/posthtml/posthtml-render/commit/34aa3c2))
* docs: add description for replaceQuote ([44ab8b0](https://github.com/posthtml/posthtml-render/commit/44ab8b0))
* style: after lint ([c944f82](https://github.com/posthtml/posthtml-render/commit/c944f82))
* reaftor: build sustem ([b63ec4e](https://github.com/posthtml/posthtml-render/commit/b63ec4e))
* feat: replaceQuote, close #43 ([00927c3](https://github.com/posthtml/posthtml-render/commit/00927c3)), closes [#43](https://github.com/posthtml/posthtml-render/issues/43)
* test: not replace quote in arribute, issue #43 ([ccb7d23](https://github.com/posthtml/posthtml-render/commit/ccb7d23)), closes [#43](https://github.com/posthtml/posthtml-render/issues/43)



## <small>1.2.3 (2020-07-28)</small>

* chore: adds typescript type definition ([8e69f1d](https://github.com/posthtml/posthtml-render/commit/8e69f1d))
* chore(release): 1.2.3 ([397e00b](https://github.com/posthtml/posthtml-render/commit/397e00b))
* build(deps-dev): bump standard-version from 7.1.0 to 8.0.1 ([fe1be76](https://github.com/posthtml/posthtml-render/commit/fe1be76))
* build(package): update dep dev ([98281b2](https://github.com/posthtml/posthtml-render/commit/98281b2))



## <small>1.2.2 (2020-04-16)</small>

* chore(release): 1.2.2 ([2f88db9](https://github.com/posthtml/posthtml-render/commit/2f88db9))
* revert: because yarn by default respects the engine ([1ff7911](https://github.com/posthtml/posthtml-render/commit/1ff7911))



## <small>1.2.1 (2020-04-14)</small>

* chore(release): 1.2.1 ([dd09a17](https://github.com/posthtml/posthtml-render/commit/dd09a17))
* fix: illegal addition of custom tags ([f936272](https://github.com/posthtml/posthtml-render/commit/f936272))
* docs: add keeping tags unquoted ([35a3c9d](https://github.com/posthtml/posthtml-render/commit/35a3c9d))
* docs: fix node url ([0049dca](https://github.com/posthtml/posthtml-render/commit/0049dca))
* build: frop support old node ([714f42f](https://github.com/posthtml/posthtml-render/commit/714f42f))
* ci: drop support old node ([5f6bef8](https://github.com/posthtml/posthtml-render/commit/5f6bef8))



## 1.2.0 (2020-02-25)

* chore: 100% coveralls ([3ede092](https://github.com/posthtml/posthtml-render/commit/3ede092))
* chore(release): 1.2.0 ([5d53868](https://github.com/posthtml/posthtml-render/commit/5d53868))
* style: after lint ([bb22948](https://github.com/posthtml/posthtml-render/commit/bb22948))
* build: update dep dev ([53a7af4](https://github.com/posthtml/posthtml-render/commit/53a7af4))
* Add support for keeping tags unquoted. ([3492483](https://github.com/posthtml/posthtml-render/commit/3492483))
* Fix rendering of unquoted empty attributes. ([3d1d99c](https://github.com/posthtml/posthtml-render/commit/3d1d99c))
* perf: extra performance ([f118d98](https://github.com/posthtml/posthtml-render/commit/f118d98))
* docs: fix node support badges ([53d7464](https://github.com/posthtml/posthtml-render/commit/53d7464))



## <small>1.1.5 (2019-05-06)</small>

* chore(release): 1.1.5 ([c9817b4](https://github.com/posthtml/posthtml-render/commit/c9817b4))
* ci: change script to run coveralls ([860f58b](https://github.com/posthtml/posthtml-render/commit/860f58b))
* ci: try fix coveralls ([2f69283](https://github.com/posthtml/posthtml-render/commit/2f69283))
* ci: try fix coveralls ([e615e69](https://github.com/posthtml/posthtml-render/commit/e615e69))
* build: add lint pretest ([bece65d](https://github.com/posthtml/posthtml-render/commit/bece65d))
* build: update depDev ([f6a6b85](https://github.com/posthtml/posthtml-render/commit/f6a6b85))
* style: according standard ([85f914a](https://github.com/posthtml/posthtml-render/commit/85f914a))
* perf: drop support old nodejs ([4663c4d](https://github.com/posthtml/posthtml-render/commit/4663c4d))
* fix: cut out content, close #25 ([03acfa8](https://github.com/posthtml/posthtml-render/commit/03acfa8)), closes [#25](https://github.com/posthtml/posthtml-render/issues/25)
* test: fail with options closingSingleTag slash, #25 ([8be0ded](https://github.com/posthtml/posthtml-render/commit/8be0ded)), closes [#25](https://github.com/posthtml/posthtml-render/issues/25)
* Fix readme section about 'closingSingleTag' ([ce4144f](https://github.com/posthtml/posthtml-render/commit/ce4144f))



## <small>1.1.4 (2018-05-11)</small>

* chore(release): 1.1.4 ([7999cc8](https://github.com/posthtml/posthtml-render/commit/7999cc8))
* Add test on double quotes in attribute values ([cd277a5](https://github.com/posthtml/posthtml-render/commit/cd277a5))
* Fix rendering double quotes in html attributes ([3a6eb19](https://github.com/posthtml/posthtml-render/commit/3a6eb19))



## <small>1.1.3 (2018-04-04)</small>

* chore(CODEOWNERS): fix username (`@GitScrum` => `@Scrum`) ([f9f9c0a](https://github.com/posthtml/posthtml-render/commit/f9f9c0a))
* chore(release): 1.1.3 ([bbc4e73](https://github.com/posthtml/posthtml-render/commit/bbc4e73))
* fix(lib/index): don't handle `<component>` as self-closing tag ([c48a2e2](https://github.com/posthtml/posthtml-render/commit/c48a2e2))



## <small>1.1.2 (2018-03-20)</small>

* chore(package): remove clean script - not use ([df85eb0](https://github.com/posthtml/posthtml-render/commit/df85eb0))
* chore(package): remove run script build in release script ([7e8c096](https://github.com/posthtml/posthtml-render/commit/7e8c096))
* chore(release): 1.1.2 ([4cd2b4a](https://github.com/posthtml/posthtml-render/commit/4cd2b4a))
* delete browser.min.js ([c1d766b](https://github.com/posthtml/posthtml-render/commit/c1d766b))
* remove `browser` ([658ef38](https://github.com/posthtml/posthtml-render/commit/658ef38))



## <small>1.1.1 (2018-03-02)</small>

* chore(.editorconfig): use 2 spaces as `indent_size` ([7359ae4](https://github.com/posthtml/posthtml-render/commit/7359ae4))
* chore(.github): add `CODEOWNERS` ([0270bb6](https://github.com/posthtml/posthtml-render/commit/0270bb6))
* chore(.github): add `ISSUE_TEMPLATE` ([25fcd58](https://github.com/posthtml/posthtml-render/commit/25fcd58))
* chore(.github): add `PULL_REQUEST_TEMPLATE` ([94416b8](https://github.com/posthtml/posthtml-render/commit/94416b8))
* chore(.gitignore): add `nyc_output` ([ed05dda](https://github.com/posthtml/posthtml-render/commit/ed05dda))
* chore(.npmignore): add `.nyc_output` && coverage ([0ca896d](https://github.com/posthtml/posthtml-render/commit/0ca896d))
* chore(.npmrc): don't generate a lockfile ([4674906](https://github.com/posthtml/posthtml-render/commit/4674906))
* chore(package): update dependencies ([ad8f1d4](https://github.com/posthtml/posthtml-render/commit/ad8f1d4))
* chore(release): 1.1.1 ([0361296](https://github.com/posthtml/posthtml-render/commit/0361296))
* style: fix lint report ([4539ef5](https://github.com/posthtml/posthtml-render/commit/4539ef5))
* style: use `standard` ([90b29ea](https://github.com/posthtml/posthtml-render/commit/90b29ea))
* refactor(lib): remove module wrapper && minor cleanups ([aed12f3](https://github.com/posthtml/posthtml-render/commit/aed12f3))
* test: refactor ([54562b4](https://github.com/posthtml/posthtml-render/commit/54562b4))
* build(rollup.config.js): use `rollup` for browser builds ([0b17496](https://github.com/posthtml/posthtml-render/commit/0b17496))
* docs(LICENSE): update year ([cb3b09e](https://github.com/posthtml/posthtml-render/commit/cb3b09e))
* docs(README): standardize ([9daa9e0](https://github.com/posthtml/posthtml-render/commit/9daa9e0))
* docs(RENDER): init JSDoc ([8066597](https://github.com/posthtml/posthtml-render/commit/8066597))
* ci(.travis): add node `stable` && `lts` ([f783b90](https://github.com/posthtml/posthtml-render/commit/f783b90))



## 1.1.0 (2018-01-18)

* v1.1.0 ([488fb69](https://github.com/posthtml/posthtml-render/commit/488fb69))
* build: rebuild mini ([53bbf3a](https://github.com/posthtml/posthtml-render/commit/53bbf3a))
* feat: allow regexps in singleTags option ([e4308c9](https://github.com/posthtml/posthtml-render/commit/e4308c9))



## <small>1.0.7 (2018-01-18)</small>

* Create MAINTAINERS ([2960f4b](https://github.com/posthtml/posthtml-render/commit/2960f4b))
* Update README.md ([0948079](https://github.com/posthtml/posthtml-render/commit/0948079))
* v1.0.7 ([508e094](https://github.com/posthtml/posthtml-render/commit/508e094))
* ci: try fix ([0719812](https://github.com/posthtml/posthtml-render/commit/0719812))
* jsdoc: parse -> render ([a8f5d5d](https://github.com/posthtml/posthtml-render/commit/a8f5d5d))



## <small>1.0.6 (2016-02-29)</small>

* 1.0.6 ([f72923c](https://github.com/posthtml/posthtml-render/commit/f72923c))
* fix immutable tree obj ([485e7fe](https://github.com/posthtml/posthtml-render/commit/485e7fe))



## <small>1.0.5 (2015-12-18)</small>

* 1.0.5 ([2a81c32](https://github.com/posthtml/posthtml-render/commit/2a81c32))
* fix render empty string attrs keys ([c47064b](https://github.com/posthtml/posthtml-render/commit/c47064b))



## <small>1.0.4 (2015-11-29)</small>

* 1.0.4 ([7557c70](https://github.com/posthtml/posthtml-render/commit/7557c70))
* fix when array in content ([12bde13](https://github.com/posthtml/posthtml-render/commit/12bde13))



## <small>1.0.3 (2015-10-25)</small>

* 1.0.3 ([afa7021](https://github.com/posthtml/posthtml-render/commit/afa7021))
* add npm scripts ([820d005](https://github.com/posthtml/posthtml-render/commit/820d005))
* fix render empty key attrs & number attrs key ([7a74dd6](https://github.com/posthtml/posthtml-render/commit/7a74dd6))



## <small>1.0.2 (2015-10-21)</small>

* no render false attrs ([0c76236](https://github.com/posthtml/posthtml-render/commit/0c76236))
* Release v1.0.2 ([7faa900](https://github.com/posthtml/posthtml-render/commit/7faa900))



## <small>1.0.1 (2015-10-20)</small>

* fix readme after transfer ([0dd2fdd](https://github.com/posthtml/posthtml-render/commit/0dd2fdd))
* more tests ([f061c65](https://github.com/posthtml/posthtml-render/commit/f061c65))
* Release v1.0.1 ([02674c2](https://github.com/posthtml/posthtml-render/commit/02674c2))
* render number & fix skip tag false ([0dce005](https://github.com/posthtml/posthtml-render/commit/0dce005))



## 1.0.0 (2015-10-19)

* add badge ([bf092f4](https://github.com/posthtml/posthtml-render/commit/bf092f4))
* add module wrapper ([36f506b](https://github.com/posthtml/posthtml-render/commit/36f506b))
* add package.json ([0837e12](https://github.com/posthtml/posthtml-render/commit/0837e12))
* basic tests ([cffae42](https://github.com/posthtml/posthtml-render/commit/cffae42))
* Initial commit ([18bd42f](https://github.com/posthtml/posthtml-render/commit/18bd42f))
* Release 1.0.0 ([35699e6](https://github.com/posthtml/posthtml-render/commit/35699e6))
* upd jscs config ([64b9f1a](https://github.com/posthtml/posthtml-render/commit/64b9f1a))
* upd Readme & add License file ([6036ea9](https://github.com/posthtml/posthtml-render/commit/6036ea9))
* feat(*): tests ([7ca52d5](https://github.com/posthtml/posthtml-render/commit/7ca52d5))



