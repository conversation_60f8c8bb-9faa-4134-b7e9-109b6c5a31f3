{"name": "scule", "version": "0.2.1", "description": "", "repository": "nuxt-contrib/scule", "license": "MIT", "sideEffects": false, "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "sir<PERSON> build", "lint": "eslint --ext .ts .", "prepublishOnly": "yarn build", "release": "yarn test && standard-version && git push --follow-tags && npm publish", "test": "yarn lint && jest"}, "devDependencies": {"@nuxtjs/eslint-config-typescript": "latest", "@types/flat": "latest", "@types/jest": "latest", "@types/node": "latest", "eslint": "latest", "jest": "latest", "siroc": "latest", "standard-version": "latest", "ts-jest": "latest", "typescript": "latest"}}