-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- 主机： 172.20.0.2
-- 生成日期： 2025-06-04 14:05:44
-- 服务器版本： 5.7.29
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `chatmoney`
--

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_square`
--

CREATE TABLE `cm_kb_robot_square` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `robot_id` int(10) UNSIGNED NOT NULL COMMENT '机器人ID',
  `cate_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分类ID',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序编号',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否显示: [0=否， 1=是]',
  `verify_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态：0-待审核；1-审核通过；2-审核不通过；',
  `verify_result` varchar(255) DEFAULT NULL COMMENT '审核结果',
  `total_revenue` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '累计分成收益',
  `use_count` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '使用次数',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人广场表' ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `cm_kb_robot_square`
--

INSERT INTO `cm_kb_robot_square` (`id`, `user_id`, `robot_id`, `cate_id`, `sort`, `is_show`, `verify_status`, `verify_result`, `total_revenue`, `use_count`, `create_time`, `update_time`, `delete_time`) VALUES
(1, 1, 3, 1, 0, 1, 1, '', '0.0000000', 0, 1748609869, 1748609899, NULL),
(2, 1, 2, 1, 0, 0, 0, NULL, '0.0000000', 0, 1748855578, 1748855578, NULL),
(3, 2, 4, 1, 0, 1, 1, '', '0.0000000', 0, 1748938301, 1748938314, NULL);

--
-- 转储表的索引
--

--
-- 表的索引 `cm_kb_robot_square`
--
ALTER TABLE `cm_kb_robot_square`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_square`
--
ALTER TABLE `cm_kb_robot_square`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键', AUTO_INCREMENT=4;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
