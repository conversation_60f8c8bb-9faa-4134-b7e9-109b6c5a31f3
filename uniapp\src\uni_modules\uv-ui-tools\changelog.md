## 1.1.25（2024-01-20）
1.1.20版本更新
## 1.1.24（2023-12-21）
1. luch-request更新
## 1.1.23（2023-12-12）
1. 1.1.19版本
## 1.1.22（2023-11-28）
1. 优化
## 1.1.21（2023-11-10）
1. 1.1.17版本
## 1.1.20（2023-10-30）
1. 1.1.16版本
## 1.1.19（2023-10-13）
1. 兼容vue3
## 1.1.18（2023-10-12）
1. 1.1.15版本
## 1.1.17（2023-09-27）
1. 1.1.14版本发布
## 1.1.16（2023-09-15）
1. 1.1.13版本发布
## 1.1.15（2023-09-15）
1. 更新button.js相关按钮支持open-type="agreePrivacyAuthorization"
## 1.1.14（2023-09-14）
1. 优化dayjs
## 1.1.13（2023-09-13）
1. 优化，$uv中增加unit参数，方便组件中使用
## 1.1.12（2023-09-10）
1. 升级版本
## 1.1.11（2023-09-04）
1. 1.1.11版本
## 1.1.10（2023-08-31）
1. 修复customStyle和customClass存在冲突的问题
## 1.1.9（2023-08-27）
1. 版本升级
2. 优化
## 1.1.8（2023-08-24）
1. 版本升级
## 1.1.7（2023-08-22）
1. 版本升级
## 1.1.6（2023-08-18）
uvui版本：1.1.6
## 1.0.15（2023-08-14）
1. 更新uvui版本号
## 1.0.13（2023-08-06）
1. 优化
## 1.0.12（2023-08-06）
1. 修改版本号
## 1.0.11（2023-08-06）
1. 路由增加events参数
2. 路由拦截修复
## 1.0.10（2023-08-01）
1. 优化
## 1.0.9（2023-06-28）
优化openType.js
## 1.0.8（2023-06-15）
1. 修改支付宝报错的BUG
## 1.0.7（2023-06-07）
1. 解决微信小程序使用uvui提示 Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors
2. 解决上述提示，需要在uni.scss配置$uvui-nvue-style: false; 然后在APP.vue下面引入uvui内置的基础样式:@import '@/uni_modules/uv-ui-tools/index.scss';
## 1.0.6（2023-06-04）
1.  uv-ui-tools 优化工具组件，兼容更多功能
2.  小程序分享功能优化等
## 1.0.5（2023-06-02）
1. 修改扩展使用mixin中方法的问题
## 1.0.4（2023-05-23）
1. 兼容百度小程序修改bem函数
## 1.0.3（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.2（2023-05-10）
1. 增加Http请求封装
2. 优化
## 1.0.1（2023-05-04）
1. 修改名称及备注
## 1.0.0（2023-05-04）
1. uv-ui工具集首次发布
