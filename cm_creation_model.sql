-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： 172.20.0.6
-- 生成日期： 2025-05-31 13:34:20
-- 服务器版本： 5.7.29-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `chatmoney`
--

-- --------------------------------------------------------

--
-- 表的结构 `cm_creation_model`
--

CREATE TABLE `cm_creation_model` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `name` varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '名称',
  `image` varchar(64) NOT NULL COMMENT '图标',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '类别',
  `status` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '状态：1-开启，0-关闭',
  `content` text COMMENT '主题内容',
  `tips` text COMMENT '提示文字',
  `context_num` int(5) UNSIGNED NOT NULL DEFAULT '2' COMMENT '上下文总数',
  `n` int(5) UNSIGNED NOT NULL DEFAULT '1' COMMENT '最大回复',
  `top_p` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.9' COMMENT '随机属性',
  `presence_penalty` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.5' COMMENT '话题属性',
  `frequency_penalty` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.5' COMMENT '重复属性',
  `temperature` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.6' COMMENT '词汇属性',
  `max_tokens` int(5) UNSIGNED NOT NULL DEFAULT '150' COMMENT '最大回复',
  `form` text NOT NULL COMMENT '表单数据',
  `virtual_use_num` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '虚拟使用',
  `system` text COMMENT '全局指令',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='创作模型表';

--
-- 转存表中的数据 `cm_creation_model`
--

INSERT INTO `cm_creation_model` (`id`, `name`, `image`, `sort`, `category_id`, `status`, `content`, `tips`, `context_num`, `n`, `top_p`, `presence_penalty`, `frequency_penalty`, `temperature`, `max_tokens`, `form`, `virtual_use_num`, `system`, `create_time`, `update_time`, `delete_time`) VALUES
(1, '周报日报', 'resource/image/creation/202309051150476e18c9430.png', 0, 7, 1, '根据我输入的${ljju8wlo}，使用下面提供的文本作为${lja6u9f7}的基础，生成一个简要的${lja6u9f7}，突出最重要的要点。${lja6u9f7}应以中文书写，且应易于阅读和理解。请首先编辑以下文本：${ljczht8s}', '输入工作内容，快速生成日报', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8y3\",\"props\":{\"field\":\"ljju8wlo\",\"title\":\"职位名称\",\"defaultValue\":\"\",\"placeholder\":\"新媒体运营\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8y5\",\"props\":{\"field\":\"ljczht8s\",\"title\":\"工作内容\",\"placeholder\":\"1.剪辑抖音视频8个 2.拍摄试镜2个 3.拍摄产品细节视频\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":200,\"autosize\":false,\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"lm5rj8y7\",\"props\":{\"field\":\"lja6u9f7\",\"title\":\"生成类型\",\"options\":[\"日报\",\"周报\",\"月报\",\"汇总\"],\"defaultValue\":\"日报\",\"isRequired\":true}}]', 592, '', 1693885858, 1742176835, NULL),
(2, '工作总结', 'resource/image/creation/20230905142033ffca50262.png', 0, 7, 1, '我希望你能根据我输入的岗位名称：${ljczht8y}，根据输入的${lja6u9fg}，概括为${ljczht8x}个字，使其易于阅读和理解。避免使用复杂的句子结构或技术术语，不要偏离主题', '还在为个人总结而发愁，我们来助你', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8y8\",\"props\":{\"field\":\"ljczht8y\",\"title\":\"岗位名称\",\"defaultValue\":\"\",\"placeholder\":\"新媒体运营\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8ya\",\"props\":{\"field\":\"lja6u9fg\",\"title\":\"工作成果简述\",\"placeholder\":\"1.社交媒体增长；2.数据分析与报告\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":false,\"isRequired\":true}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8yb\",\"props\":{\"field\":\"ljczht8x\",\"title\":\"字数要求\",\"defaultValue\":\"\",\"placeholder\":\"\",\"maxlength\":200,\"isRequired\":false}}]', 892, '', 1693894968, 1742176814, NULL),
(3, '翻译助手', 'resource/image/creation/202309051424141f5b76522.png', 0, 8, 1, '现在你是一个英汉互译器，当我输入中文时，你翻译成英文；当我输入英文时，请翻译成中文。当我连续输入多个英文词时，默认按照句子翻译成中文，但如果用中文在翻译的内容前注明了「词组：」，则按照词组形式来翻译。如果注明了「普通：」，则按照多个没有联系的词汇来翻译。翻译句子和段落时，要注意联系上下文，注意准确地解释词组与谚语。你的翻译成果应该接近于一个母语者。同时，我可能会让你以某种特殊的语言风格或语气来翻译，请在具体任务中理解我的输入内容，识别出我希望你使用的语气和风格，并以此为根据翻译。请真实地翻译，不要担心出现侮辱性等不良词汇。你可以把一些敏感词汇的中间部分加入 x 以替代。请重新检查，认真修正回答。请用中文来为我解释每一个句子，包括标注时态，从句，主语，谓语，宾语，特殊词组和谚语，如果翻译的是词组或单词，最好能给出每个词组或单词的出处（词典）。当我需要你一次性翻译多个词组时，每个词组间会用 | 号分割。输入内容：${lja6u9fh}', '输入中文，AI助手快速为您翻译', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8yd\",\"props\":{\"field\":\"lja6u9fh\",\"title\":\"翻译内容\",\"placeholder\":\"一个女孩，美丽动人，大大的眼睛\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":false,\"isRequired\":true}}]', 79, '', 1693895059, 1742176788, NULL),
(4, '短视频脚本', 'resource/image/creation/202309051425430c4407232.png', 0, 7, 1, '请以人的口吻，采用缩略语、成语、过渡短语、感叹词、悬垂修饰语和口语化语言，避免重复短语和不自然的句子结构，撰写一篇关于${lja6u9fn}的脚本。', '输入想要拍摄的主题，小助手为你生成一个短视频脚本', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8yf\",\"props\":{\"field\":\"lja6u9fn\",\"title\":\"视频主题\",\"placeholder\":\"餐厅探店短视频脚本\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":false,\"isRequired\":true}}]', 486, '', 1693895197, 1742176629, NULL),
(5, '星座测试', 'resource/image/creation/202309051438314a7e73466.png', 0, 5, 1, '根据我提供的星座${ljk4l2t0}，为我推测未来的感情、事业运势，还有一些建议，其他要求：${aaa}。', '输入您的星座，帮你预测运势', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8yg\",\"props\":{\"field\":\"ljk4l2t0\",\"title\":\"星座\",\"defaultValue\":\"\",\"placeholder\":\"白羊座\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"m8ce6l13\",\"props\":{\"field\":\"aaa\",\"title\":\"其他要求\",\"defaultValue\":\"\",\"placeholder\":\"\",\"maxlength\":200,\"isRequired\":false}}]', 387, '', 1693895917, 1742176595, NULL),
(6, '写故事', 'resource/image/creation/20230905143857412af3152.png', 0, 2, 1, '我希望你扮演一个讲故事的人，用中文回应。你会想出引人入胜、富有想象力、引人入胜的有趣故事。它可以是童话、教育故事或任何其他类型的故事，有可能吸引人们的注意力和想象力。根据目标受众的不同，你可以选择讲故事的特定主题或主题，例如，如果是孩子，那么你可以谈论动物；如果是成年人，那么基于历史的故事可能会更好地吸引他们，我的第一个要求是${ljavt5jb}', '根据关键词，快速生成一段故事文章', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8yi\",\"props\":{\"field\":\"ljavt5jb\",\"title\":\"故事内容\",\"placeholder\":\"从前有一座山，山里有一个小和尚\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":200,\"autosize\":false,\"isRequired\":true}}]', 0, '', 1693895997, 1703661390, 1703661390),
(7, '送女友礼物', 'resource/image/creation/202309051441570f6854553.png', 0, 5, 1, '根据我输入的节日${ljk2sk0b}，推荐相应的礼物送女朋友\n节日名称：${ljk2sk0b}', '根据节日推荐送什么礼物给女友', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8yj\",\"props\":{\"field\":\"ljk2sk0b\",\"title\":\"节日名称\",\"defaultValue\":\"\",\"placeholder\":\"情人节\",\"maxlength\":200,\"isRequired\":true}}]', 197, '', 1693896168, 1742176763, NULL),
(8, '表白信', 'resource/image/creation/20230905144307988878894.png', 0, 6, 1, '你是个感情专家和大作家，现在见到爱人${ljk1mnjp}，假设对方是${ljk1mnk0}，写一篇对${ljk1mnjw}的表白情书。我对你的要求是，以“你的爱人”结尾', '情感专家在线写情书', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8yk\",\"props\":{\"field\":\"ljk1mnjp\",\"title\":\"表白对象名字\",\"defaultValue\":\"\",\"placeholder\":\"刘亦菲\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"lm5rj8ym\",\"props\":{\"field\":\"ljk1mnk0\",\"title\":\"对象性别\",\"options\":[\"男\",\"女\"],\"defaultValue\":\"女\",\"isRequired\":true}}]', 79, '', 1693896274, 1742176448, NULL),
(9, '个性签名', 'resource/image/creation/20230905144538d9ba55830.png', 0, 9, 1, '根据我的要求：${ljk1d7jw}，结合网络流行词，生成几条独一无二的创意签名', '制作一条个性签名', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8yn\",\"props\":{\"field\":\"ljk1d7jw\",\"title\":\"要求\",\"defaultValue\":\"\",\"placeholder\":\"QQ个性签名、幽默\",\"maxlength\":200,\"isRequired\":true}}]', 539, '', 1693896349, 1742175905, NULL),
(10, '高情商回复', 'resource/image/creation/20230905150853852657376.png', 0, 5, 1, '我想让你充当一个高情商交流大师。我会说“${ljjx88qd}”，然后用幽默有趣的方式指导我怎么回复这句话，如果可以的话这句回复让她有机会给我们开展出新的话题让聊天方式不再单一。', '高情商回答', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8yp\",\"props\":{\"field\":\"ljjx88qd\",\"title\":\"输入你想说的话\",\"placeholder\":\"你好漂亮呀\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":true,\"isRequired\":true}}]', 687, '', 1693897738, 1742175950, NULL),
(11, '单身狗导师', 'resource/image/creation/2023090515100379fa89979.png', 0, 5, 1, '假设你是一位有经验的单身人士，是具有良好的人际交往能力和情感智慧，根据输入的内容描述${lja6u9f9}，帮助其他单身人士改善自己的情感状态，提高自己的约会技巧和恋爱能力，从而更好地融入社交圈，找到合适的伴侣。', '根据导师的人生经验，让AI给你建议', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8yq\",\"props\":{\"field\":\"lja6u9f9\",\"title\":\"文本内容\",\"defaultValue\":\"\",\"placeholder\":\"如何追一个心仪已久的女生\",\"maxlength\":200,\"isRequired\":true}}]', 239, '', 1693897809, 1742175925, NULL),
(12, '论文资料', 'resource/image/creation/2023090515583661d410599.png', 20, 8, 1, '根据输入论文主题${ljk4l2sh}，帮我推荐一些相关文献。要优先推荐国内的资料', '只需输入论文主题，便能帮你推荐一些相关文献', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8yr\",\"props\":{\"field\":\"ljk4l2sh\",\"title\":\"论文主题\",\"defaultValue\":\"\",\"placeholder\":\"科学与技术\",\"maxlength\":200,\"isRequired\":true}}]', 1291, '', 1693900770, 1742174400, NULL),
(13, '诗词创作', 'resource/image/creation/202309051612426ccec3826.png', 40, 8, 1, '根据提供的主题，写出关于${ljavt5jk}的精美诗词。', '根据主题写出及精美诗词', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8ys\",\"props\":{\"field\":\"ljavt5jk\",\"title\":\"主题内容\",\"defaultValue\":\"\",\"placeholder\":\"思念\",\"maxlength\":200,\"isRequired\":true}}]', 581, '', 1693901605, 1742174376, NULL),
(14, '外卖好评', 'resource/image/creation/202309051617269c7d89114.jpeg', 60, 9, 1, '我是一个消费者，请从口味、品相、送货速度、商家态度等方面给予商家好评，不少于50个字。我的餐品名称是：${ljk4l2t2}', '一键生成外卖好评', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8yt\",\"props\":{\"field\":\"ljk4l2t2\",\"title\":\"餐品名称\",\"defaultValue\":\"\",\"placeholder\":\"奶茶\",\"maxlength\":200,\"isRequired\":true}}]', 279, '', 1693901900, 1742174337, NULL),
(15, '阅读助手', 'resource/image/creation/202309051618494e7635274.png', 60, 8, 1, '在以下这个场景中，有人对我说了一句话，请帮我分析对方可能想表达什么意思，并提供一个合适的回应。场景：${ljiiy508}。说话人说：${ljiiy50b}。对方的意图可能是什么？我应该如何回应？', '对于一些无法理解的对话，提供对话背景让 AI 来进行解读并制定出适当的回应。', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8yu\",\"props\":{\"field\":\"ljiiy508\",\"title\":\"情境描述\",\"defaultValue\":\"\",\"placeholder\":\"吃饭的时候\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8yw\",\"props\":{\"field\":\"ljiiy50b\",\"title\":\"具体的话语\",\"placeholder\":\"你好厉害！\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":false,\"isRequired\":false}}]', 379, '', 1693902003, 1742174313, NULL),
(16, '挂号咨询', 'resource/image/creation/20230905162016830885613.png', 60, 6, 1, '你现在是一名全能医生，根据输入的描述，你可以告诉我要挂什么科，有哪些建议。我的要求是：${ljk2sk02}', 'AI医生助手', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8yy\",\"props\":{\"field\":\"ljk2sk02\",\"title\":\"病情描述\",\"placeholder\":\"牙齿疼，要挂什么科\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":false,\"isRequired\":true}}]', 195, '', 1693902498, 1742174276, NULL),
(17, '淘宝好评', 'resource/image/creation/20230905162840c84fa7514.png', 70, 9, 1, '请根据我的产品分类及产品特性，输出一段100字左右的产品好评文案，内容要符合日常消费者口吻，我的产品是：${ljavt5j7}', '自动生成商品评价', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8z0\",\"props\":{\"field\":\"ljavt5j7\",\"title\":\"商品名称和卖点\",\"placeholder\":\"万彩电动牙刷，五档模式、声波震动、IPX7级防水、杜邦刷毛、续航30天\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":200,\"autosize\":false,\"isRequired\":true}}]', 329, '', 1693902568, 1742174148, NULL),
(18, '节日祝福', 'resource/image/creation/20230905163544815366276.png', 70, 6, 1, '写一个关于${ljjx88qa}的节日祝福', '节日祝福与问候', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8z1\",\"props\":{\"field\":\"ljjx88qa\",\"title\":\"节日\",\"defaultValue\":\"\",\"placeholder\":\"母亲节\",\"maxlength\":200,\"isRequired\":true}}]', 921, '', 1693902986, 1742174220, NULL),
(19, '产品描述', 'resource/image/creation/2023090516364558eb53135.png', 75, 9, 1, '写一篇${ljjx88qf}的产品描述，用于产品推广，描述贴切、高级。', '根据提供的产品生成产品描述', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8z2\",\"props\":{\"field\":\"ljjx88qf\",\"title\":\"产品名称\",\"defaultValue\":\"\",\"placeholder\":\"电动牙刷\",\"maxlength\":200,\"isRequired\":true}}]', 624, '', 1693903046, 1742174193, NULL),
(20, '关键词标题', 'resource/image/creation/20230905163740d46128606.png', 80, 9, 1, '根据输入的关键词${ljk2sk09}，帮我生成五个吸引人的标题', '快速生成关键词标题', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8z3\",\"props\":{\"field\":\"ljk2sk09\",\"title\":\"关键词\",\"defaultValue\":\"\",\"placeholder\":\"网站引流\",\"maxlength\":200,\"isRequired\":true}}]', 892, '', 1693903103, 1742174204, NULL),
(21, '菜谱大全', 'resource/image/creation/2023090516383964d438185.png', 80, 6, 1, '现在你是一个经验丰富的厨师，你会做中餐西餐日料糕点等各种食物。我是一个新手厨师，因此你需要给我一份非常详细的菜谱，并在最后指出制作这道菜肴需要注意的地方。这样我才能做出更好的美食！我的想做的菜是：${ljk2sjzz}', '输入菜品名称，获取相关菜品做法', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8z5\",\"props\":{\"field\":\"ljk2sjzz\",\"title\":\"菜品名称\",\"placeholder\":\"鱼香肉丝\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":false,\"isRequired\":true}}]', 2892, '', 1693903169, 1742174163, NULL),
(22, '广告文案', 'resource/image/creation/20230905163955add4f8121.png', 80, 9, 1, '我想让你充当广告策划。创建一个活动来推广产品或服务，请选择目标受众，制定关键信息和口号，选择宣传媒体渠道，并决定实现目标所需的任何其他活动，尽量的详细并且在1000字左右。我的第一个产品和要求是：${ljjx88qi}', '生成广告文案和口号，选择宣传媒体渠道，并决定实现目标所需的任何其他活动', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8z7\",\"props\":{\"field\":\"ljjx88qi\",\"title\":\"产品要求\",\"placeholder\":\"我需要一份针对年轻女性跑步鞋的广告文案\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":true,\"isRequired\":true}}]', 523, '', 1693903251, 1741940881, NULL),
(23, '电影推荐', 'resource/image/creation/2023090516411568c7f2589.png', 90, 6, 1, '根据输入电影主题${ljavt5ji}，结合豆瓣评分，帮我推荐几部近期好看的电影，如果我没有说明国内还是国外的，就优先推荐国内的电影；并写出推荐理由', 'AI为您推荐好看的电影', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8z8\",\"props\":{\"field\":\"ljavt5ji\",\"title\":\"电影主题\",\"defaultValue\":\"\",\"placeholder\":\"教育\",\"maxlength\":200,\"isRequired\":true}}]', 643, '', 1693903328, 1741940866, NULL),
(24, '旅游计划', 'resource/image/creation/20230905164220145132222.png', 100, 6, 1, '用${ljk4l2st}语言写一篇关于${ljk4l2sn}的出游攻略，包括日期，时间点，景点，往返程路线等', '制作出游攻略，包括日期，时间点，景点，往返程路线等', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"lm5rj8z9\",\"props\":{\"field\":\"ljk4l2sn\",\"title\":\"地点\",\"defaultValue\":\"\",\"placeholder\":\"云南\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"lm5rj8zb\",\"props\":{\"field\":\"ljk4l2st\",\"title\":\"语言输出\",\"options\":[\"中文\",\"英语\",\"韩语\",\"泰语\"],\"defaultValue\":\"中文\",\"isRequired\":true}}]', 1135, '', 1693903411, 1741940564, NULL),
(25, '英文写作', 'resource/image/creation/20230905164346b19d70271.png', 100, 8, 1, '根据我输入的要求，帮我润色成优美的英文作文，我的要求是：${ljjflsw8}', '输入英文或中文，帮你润色成优美的英文', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8zd\",\"props\":{\"field\":\"ljjflsw8\",\"title\":\"写作内容\",\"placeholder\":\"写一封邀请信，邀请英国大学的一位教授组队来中国参加国际创新大赛\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":false,\"isRequired\":true}},{\"name\":\"WidgetSelect\",\"title\":\"下拉选项\",\"id\":\"lm5rj8zf\",\"props\":{\"field\":\"ljw9oucw\",\"title\":\"测试\",\"options\":[\"1\",\"2\"],\"defaultValue\":\"\",\"isRequired\":false}}]', 1235, '', 1693903489, 1741940529, NULL),
(26, '朋友圈文案', 'resource/image/creation/20230905164505a83629538.png', 110, 9, 1, '根据输入的关键词${ljigx1qd}，帮我生成吸引人的朋友圈文案。', '输入关键词，生成吸引人的朋友圈文案。', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8zh\",\"props\":{\"field\":\"ljigx1qd\",\"title\":\"关键词\",\"placeholder\":\"周末去喝了咖啡，和朋友聚了聚\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":200,\"autosize\":false,\"isRequired\":true}}]', 1232, '', 1693903576, 1741940474, NULL),
(27, '小红书文案', 'resource/image/creation/20230905164637b12637007.png', 120, 9, 1, '使用 Emoji 小红书风格编辑以下段落，该风格以引人入胜的标题、每个段落中包含表情符号和在末尾添加相关标签为特点。请确保保持原文的意思。以下是需要编辑的内容：${ljiiy50c}', '“一看就种草，看完就下单，拦都拦不住”', 2, 1, 0.9, 1.0, 0.5, 1.0, 150, '[{\"name\":\"WidgetTextarea\",\"title\":\"多行文本\",\"id\":\"lm5rj8zj\",\"props\":{\"field\":\"ljiiy50c\",\"title\":\"简要描述\",\"placeholder\":\"种草了一款泡泡袖连衣裙\",\"rows\":4,\"defaultValue\":\"\",\"maxlength\":500,\"autosize\":false,\"isRequired\":true}}]', 1087, '', 1693903654, 1741940464, NULL),
(28, '骑行训练计划', 'uploads/images/20241205/20241205160943d1c325192.png', 1, 6, 1, '你是一名世巡赛级别的自行车运动教练，请根据以下信息为一名自行车运动员制定一套科学严谨的详细骑行训练计划，包括但不限于训练内容、饮食注意事项、休息时间等。骑行训练目标为：${lxy2jyfu}，计划时长为：${lxy2jyfw}，目前历史单次最长骑行距离为：${lxy2jyfy}，体重为：${lxy2jyg2}KG，训练频次是：${eee}，FTP是：${lxy2jyg0}W，最大摄氧量是：${lxy2jyg4}ml/kg/min。其他要求：${ddd}。内容尽量详尽，并有可操作性。', '根据您的骑行训练目标，制定科学高效的骑行训练计划。', 0, 1, 0.9, 0.5, 0.0, 0.7, 0, '[{\"name\":\"WidgetCheckbox\",\"title\":\"多选\",\"id\":\"m6raq7dj\",\"props\":{\"field\":\"lxy2jyfu\",\"title\":\"骑行训练目标（可多选，推荐使用Deepseek模型）\",\"options\":[\"FTP训练\",\"最大摄氧量训练\",\"爬坡训练\",\"冲刺训练\",\"耐力训练\"],\"defaultValue\":[\"FTP训练\"],\"isRequired\":true}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"m6raq7dk\",\"props\":{\"field\":\"lxy2jyfw\",\"title\":\"计划时长\",\"defaultValue\":\"\",\"placeholder\":\"计划持续的时间，如3个月\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetSelect\",\"title\":\"下拉选项\",\"id\":\"m6raq7do\",\"props\":{\"field\":\"lxy2jyfy\",\"title\":\"历史单次最长骑行距离\",\"options\":[\"10公里以内\",\"10-30公里\",\"30-50公里\",\"50-100公里\",\"100-200公里\",\"200-500公里\",\"500公里以上\"],\"defaultValue\":\"50-100公里\",\"isRequired\":true}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"m6raq7dm\",\"props\":{\"field\":\"lxy2jyg2\",\"title\":\"体重\",\"defaultValue\":\"\",\"placeholder\":\"65（不需要填单位，默认单位为KG）\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8ce6l18\",\"props\":{\"field\":\"eee\",\"title\":\"训练频次\",\"options\":[\"每周1-2次\",\"每周3-4次\",\"每周5-6次\",\"平均每天1次以上\",\"平均每天2次\"],\"defaultValue\":\"每周3-4次\",\"isRequired\":true}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"m6raq7dp\",\"props\":{\"field\":\"lxy2jyg0\",\"title\":\"FTP值（非必填项，推荐填写）\",\"defaultValue\":\"\",\"placeholder\":\"220（不需要填单位，默认单位为W）\",\"maxlength\":200,\"isRequired\":false}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"m6raq7dq\",\"props\":{\"field\":\"lxy2jyg4\",\"title\":\"最大摄氧量值（非必填项）\",\"defaultValue\":\"\",\"placeholder\":\"60（无需填单位，默认单位为ml/kg/min）\",\"maxlength\":200,\"isRequired\":false}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"m8ce6l10\",\"props\":{\"field\":\"ddd\",\"title\":\"其他要求（非必填项）\",\"defaultValue\":\"\",\"placeholder\":\"FTP达到300W\",\"maxlength\":200,\"isRequired\":false}}]', 856, '', 1738724202, 1742268754, NULL),
(29, '旅游计划-1', 'uploads/images/20250307/5003c4987e47ea0f0ff6ad25f69c236a.png', 0, 6, 1, '你是一名专业的导游，去过世界各地，请你根据要求${35d86b}，写一篇关于${ebda25}的${旅游天数}天出游攻略，包括日期，时间点，景点，往返程路线等', '量身制作你的旅游指南，出行攻略', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"\\u5355\\u884c\\u6587\\u672c\",\"id\":\"ad91204d\",\"props\":{\"title\":\"\\u5730\\u70b9\",\"field\":\"ebda25\",\"defaultValue\":\"\",\"placeholder\":\"\\u5e7f\\u5dde\",\"maxlength\":200,\"isRequired\":true},\"key\":\"field1\"},{\"name\":\"WidgetTextarea\",\"title\":\"\\u591a\\u884c\\u6587\\u672c\",\"id\":\"91c7a738\",\"props\":{\"title\":\"\\u8981\\u6c42\",\"field\":\"35d86b\",\"placeholder\":\"\\u7701\\u94b1\",\"defaultValue\":\"\",\"rows\":\"4\",\"maxlength\":200,\"autosize\":true,\"isRequired\":true},\"key\":\"field2\"}]', 0, NULL, 1741329813, 1742175859, 1742175859),
(30, '广告文案-1', 'resource/image/adminapi/default/cs_app.png', 0, 7, 1, '你是一名广告营销经理，请根据我提供的产品名称为${f3ec44}，广告风格为${148091}，帮我打造一条创意十足，吸睛的网络广告文案', '量身打造广告文案，轻松打造吸睛推广', 2, 1, 0.9, 0.5, 0.5, 0.6, 150, '[{\"name\":\"WidgetInput\",\"title\":\"\\u5355\\u884c\\u6587\\u672c\",\"id\":\"68ebea37\",\"props\":{\"title\":\"\\u4ea7\\u54c1\",\"field\":\"f3ec44\",\"defaultValue\":\"\",\"placeholder\":\"\\u7ebf\\u4e0a\\u6559\\u5b66\\u5e73\\u53f0\",\"maxlength\":200,\"isRequired\":true},\"key\":\"field1\"},{\"name\":\"WidgetSelect\",\"title\":\"\\u4e0b\\u62c9\\u9009\\u9879\",\"id\":\"82458b13\",\"props\":{\"title\":\"\\u5e7f\\u544a\\u98ce\\u683c\",\"field\":\"148091\",\"options\":[\"\\u5b98\\u65b9\",\"\\u6d6a\\u6f2b\",\"\\u6b22\\u5757\",\"\\u5e7d\\u9ed8\",\"\\u641e\\u602a\"],\"defaultValue\":\"\\u5b98\\u65b9\",\"isRequired\":true},\"key\":\"field3\"}]', 0, NULL, 1741329813, 1742175855, 1742175855),
(31, 'AI风水（基础版）', 'uploads/images/20250317/202503171035210d0a91339.jpg', 60, 5, 1, '你是一名资深风水大师，给一个住宅看风水，这个住宅的情况如下：进入户门${aaa}客厅，入户门${bbb}卫生间，入户门${ccc}厨房，卫生间门${ddd}卧室门，入户门${eee}卧室门，卧室门${fff}其他卧室门，入户门在房屋的${ggg}，厨房在房屋的${hhh}，卫生间在房屋的${iii}。请根据以上情况，对住宅风水进行评分（满分100分），并进行详细分析，并给出简单易行的补救措施。', 'DeepSeek大模型测试住宅风水，找出风水缺陷并给出补救建议。', 0, 1, 0.9, 0.5, 0.0, 0.7, 0, '[{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m88f6k6v\",\"props\":{\"field\":\"aaa\",\"title\":\"您的房屋是不是进门就能直接看到客厅（有玄关等遮挡视为不能直接看到）\",\"options\":[\"能直接看到\",\"不能直接看到\"],\"defaultValue\":\"能直接看到\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m88f6k6z\",\"props\":{\"field\":\"bbb\",\"title\":\"您的房屋入户门是否对着卫生间\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m88f6k71\",\"props\":{\"field\":\"ccc\",\"title\":\"您的房屋入户门是否对着厨房\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m88f6k73\",\"props\":{\"field\":\"ddd\",\"title\":\"您的卫生间门是否对着卧室门\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m88f6k75\",\"props\":{\"field\":\"eee\",\"title\":\"您的房屋入户门是否对着卧室门\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m88f6k77\",\"props\":{\"field\":\"fff\",\"title\":\"您的卧室门是否对着其他卧室门\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m88f6k7a\",\"props\":{\"field\":\"ggg\",\"title\":\"您的入户门在房屋的什么方位\",\"options\":[\"正北方\",\"东北方\",\"正东方\",\"东南方\",\"正南方\",\"西南方\",\"正西方\",\"西北方\"],\"defaultValue\":\"正北方\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m88f6k7c\",\"props\":{\"field\":\"hhh\",\"title\":\"您的厨房在房屋的什么方位\",\"options\":[\"正北方\",\"东北方\",\"正东方\",\"东南方\",\"正南方\",\"西南方\",\"正西方\",\"西北方\",\"中间位置\"],\"defaultValue\":\"正北方\",\"isRequired\":true}},{\"name\":\"WidgetCheckbox\",\"title\":\"多选\",\"id\":\"m8gwl86a\",\"props\":{\"field\":\"iii\",\"title\":\"您的卫生间在房屋的什么方位（可多选）\",\"options\":[\"正北方  \",\"东北方  \",\"正东方  \",\"东南方  \",\"正南方\",\"西南方  \",\"正西方  \",\"西北方  \",\"中间位置  \"],\"defaultValue\":[\"正北方  \"],\"isRequired\":true}}]', 21891, '', 1741935716, 1746587199, NULL),
(32, 'AI风水（进阶版）', 'uploads/images/20250317/20250317103514234903506.jpg', 60, 5, 1, '你是一名资深风水大师，具有丰富的阳宅风水调理经验，给一个住宅看风水，这个住宅的情况如下：客厅在房屋的${a777}，房屋${a111}，${a222}，进入户门${aaa}客厅，入户门${bbb}卫生间，入户门${ccc}厨房，卫生间门${ddd}卧室门，厨房门${a888}卧室门，入户门${eee}卧室门，卧室门${fff}其他卧室门，入户门在房屋的${ggg}，厨房在房屋的${hhh}，卫生间在房屋的${iii}，房屋过道${a333}房屋中间，将房屋一分为二（如房屋过道不在房屋中间则无此情况），卫生间${a444}走廊的尽头，卧室户型${a555}，客厅户型${a666}，卧室里${a999}卫生间。请根据以上情况，对住宅风水进行评分（满分100分），并进行详细分析，并给出简单易行的补救措施。', 'DeepSeek大模型测试住宅风水，找出风水缺陷并给出补救建议。', 0, 1, 0.9, 0.5, 0.0, 0.7, 0, '[{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwl86k\",\"props\":{\"field\":\"a777\",\"title\":\"您的客厅在房屋的什么方位\",\"options\":[\"正北方\",\"东北方\",\"正东方\",\"东南方\",\"正南方\",\"西南方\",\"正西方\",\"西北方\",\"中间位置\"],\"defaultValue\":\"正北方\",\"isRequired\":true}},{\"name\":\"WidgetCheckbox\",\"title\":\"多选\",\"id\":\"m8gwgzi3\",\"props\":{\"field\":\"a111\",\"title\":\"房屋缺角情况（可多选，将房屋平面图划分为九宫格--3x3的方格，每个方格对应一个方位，如果某个方位的方格缺失超过1/3，则视为该方位缺角）\",\"options\":[\"无缺角  \",\"正北方缺角  \",\"东北方缺角  \",\"正东方缺角  \",\"东南方缺角  \",\"正南方缺角  \",\"西南方缺角  \",\"正西方缺角  \",\"西北方缺角  \"],\"defaultValue\":[],\"isRequired\":true}},{\"name\":\"WidgetCheckbox\",\"title\":\"多选\",\"id\":\"m8gwgzi5\",\"props\":{\"field\":\"a222\",\"title\":\"房屋凸角情况（可多选，将房屋平面图划分为九宫格--3x3的方格，每个方格对应一个方位，如果某个方位的方格向外突出超过1/3，则视为该方位凸角）\",\"options\":[\"无凸角  \",\"正北方凸角  \",\"东北方凸角  \",\"正东方凸角  \",\"东南方凸角  \",\"正南方凸角  \",\"西南方凸角  \",\"正西方凸角  \",\"西北方凸角  \"],\"defaultValue\":[],\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwgzhl\",\"props\":{\"field\":\"aaa\",\"title\":\"您的房屋是不是进门就能直接看到客厅（有玄关等遮挡视为不能直接看到）\",\"options\":[\"能直接看到\",\"不能直接看到\"],\"defaultValue\":\"能直接看到\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwgzhn\",\"props\":{\"field\":\"bbb\",\"title\":\"您的房屋入户门是否对着卫生间\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwgzhp\",\"props\":{\"field\":\"ccc\",\"title\":\"您的房屋入户门是否对着厨房\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwgzhr\",\"props\":{\"field\":\"ddd\",\"title\":\"您的卫生间门是否对着卧室门\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwl86m\",\"props\":{\"field\":\"a888\",\"title\":\"您的厨房门是否对着卧室门\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwgzht\",\"props\":{\"field\":\"eee\",\"title\":\"您的房屋入户门是否对着卧室门\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwgzhv\",\"props\":{\"field\":\"fff\",\"title\":\"您的卧室门是否对着其他卧室门\",\"options\":[\"不对着\",\"对着\"],\"defaultValue\":\"不对着\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwgzhx\",\"props\":{\"field\":\"ggg\",\"title\":\"您的入户门在房屋的什么方位\",\"options\":[\"正北方\",\"东北方\",\"正东方\",\"东南方\",\"正南方\",\"西南方\",\"正西方\",\"西北方\"],\"defaultValue\":\"正北方\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwgzhz\",\"props\":{\"field\":\"hhh\",\"title\":\"您的厨房在房屋的什么方位\",\"options\":[\"正北方\",\"东北方\",\"正东方\",\"东南方\",\"正南方\",\"西南方\",\"正西方\",\"西北方\",\"中间位置\"],\"defaultValue\":\"正北方\",\"isRequired\":true}},{\"name\":\"WidgetCheckbox\",\"title\":\"多选\",\"id\":\"m8gwl86p\",\"props\":{\"field\":\"iii\",\"title\":\"您的卫生间在房屋的什么方位（多个卫生间可多选）\",\"options\":[\"正北方  \",\"东北方  \",\"正东方  \",\"东南方  \",\"正南方  \",\"西南方  \",\"正西方  \",\"西北方  \",\"中间位置  \"],\"defaultValue\":[\"正北方  \"],\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwl86c\",\"props\":{\"field\":\"a333\",\"title\":\"您的房屋过道是不是在房屋中间，将房屋一分为二（没有过道或过道很短可选“不在”）\",\"options\":[\"不在\",\"在\"],\"defaultValue\":\"不在\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwl86e\",\"props\":{\"field\":\"a444\",\"title\":\"您的卫生间是否在走廊的尽头\",\"options\":[\"不在\",\"在\"],\"defaultValue\":\"不在\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwl86g\",\"props\":{\"field\":\"a555\",\"title\":\"您的卧室户型形状是否规则\",\"options\":[\"是规则的正方形或长方形\",\"形状不规则\"],\"defaultValue\":\"是规则的正方形或长方形\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwl86i\",\"props\":{\"field\":\"a666\",\"title\":\"您的客厅户型形状是否规则\",\"options\":[\"是规则的正方形或长方形\",\"形状不规则\"],\"defaultValue\":\"是规则的正方形或长方形\",\"isRequired\":true}},{\"name\":\"WidgetRadio\",\"title\":\"单选\",\"id\":\"m8gwl86o\",\"props\":{\"field\":\"a999\",\"title\":\"您的卧室里有没有卫生间\",\"options\":[\"有\",\"没有\"],\"defaultValue\":\"有\",\"isRequired\":true}}]', 2890, '', 1742448007, 1746587210, NULL),
(33, '马拉松跑步训练计划', 'uploads/images/20241205/20241205160952a93941362.png', 1, 6, 1, '你是一名专业的马拉松运动教练，请根据以下信息为一名马拉松运动员制定一套科学严谨的详细骑行训练计划，包括但不限于训练内容、饮食注意事项、休息时间等。年龄为：${aaa}岁，身高${bbb}cm，体重${ccc}kg，最大单次跑程${ddd}km，半马PB${eee}。内容尽量详尽，并有可操作性。', 'DeepSeek指定马拉松跑步训练计划', 0, 1, 0.9, 0.5, 0.0, 0.7, 0, '[{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"madc1b8p\",\"props\":{\"field\":\"aaa\",\"title\":\"年龄（岁）\",\"defaultValue\":\"\",\"placeholder\":\"26\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"madc1b8q\",\"props\":{\"field\":\"bbb\",\"title\":\"身高（cm）\",\"defaultValue\":\"\",\"placeholder\":\"172\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"madc1b8r\",\"props\":{\"field\":\"ccc\",\"title\":\"体重（kg）\",\"defaultValue\":\"\",\"placeholder\":\"60\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"madc1b8s\",\"props\":{\"field\":\"ddd\",\"title\":\"最大单次跑程（km）\",\"defaultValue\":\"\",\"placeholder\":\"15\",\"maxlength\":200,\"isRequired\":true}},{\"name\":\"WidgetInput\",\"title\":\"单行文本\",\"id\":\"madc1b8t\",\"props\":{\"field\":\"eee\",\"title\":\"半马PB\",\"defaultValue\":\"\",\"placeholder\":\"1:25\",\"maxlength\":200,\"isRequired\":false}}]', 2879, '', 1746605089, 1746605089, NULL);

--
-- 转储表的索引
--

--
-- 表的索引 `cm_creation_model`
--
ALTER TABLE `cm_creation_model`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `cm_creation_model`
--
ALTER TABLE `cm_creation_model`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键', AUTO_INCREMENT=34;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
