<template>
  <page-meta :page-style="$theme.pageStyle"></page-meta>
  <view class="select-user">
    <!-- 导航栏 -->
    <nav-bar title="选择用户" :show-back="true"></nav-bar>
    
    <view class="content">
      <!-- 搜索区域 -->
      <view class="search-section p-4">
        <view class="search-box bg-white rounded-xl p-3 mb-4">
          <u-search
            v-model="searchKeyword"
            placeholder="请输入完整的用户ID（8位数字）"
            height="40"
            bg-color="transparent"
            :show-action="false"
            :maxlength="8"
            @search="searchUser"
            @custom="searchUser"
            @clear="clearSearch"
          />
        </view>
        
        <u-button
          type="primary"
          shape="circle"
          :disabled="!searchKeyword || searching"
          :loading="searching"
          loading-text="搜索中..."
          @click="searchUser"
        >
          搜索用户
        </u-button>
      </view>

      <!-- 最近赠送用户 -->
      <view v-if="recentUsers.length > 0" class="recent-section p-4">
        <view class="section-title text-main font-medium mb-3">最近赠送</view>
        <scroll-view scroll-x="true" class="recent-scroll">
          <view class="recent-list flex">
            <view 
              v-for="user in recentUsers" 
              :key="user.id"
              class="recent-item flex flex-col items-center mr-4"
              @click="selectRecentUser(user)"
            >
              <u-avatar :src="user.avatar" size="64" class="mb-2"></u-avatar>
              <text class="text-xs text-center max-w-16 truncate text-content">{{ user.nickname }}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 搜索结果 -->
      <view v-if="searchResult" class="result-section p-4">
        <view class="section-title text-main font-medium mb-3">搜索结果</view>
        <view 
          class="user-card bg-white rounded-xl p-4 cursor-pointer"
          @click="selectSearchUser(searchResult)"
        >
          <view class="flex items-center">
            <u-avatar :src="searchResult.avatar" size="48" class="mr-4"></u-avatar>
            <view class="flex-1">
              <view class="font-medium text-lg text-main">{{ searchResult.nickname }}</view>
              <view class="text-sm text-tips">ID: {{ searchResult.sn || searchResult.id }}</view>
            </view>
            <view class="select-indicator">
              <u-icon name="arrow-right" size="16" color="#909399"></u-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="showEmptyState" class="empty-state text-center py-20">
        <u-icon name="account" size="80" color="#c0c4cc" class="mb-4"></u-icon>
        <text class="text-tips">{{ emptyStateText }}</text>
      </view>

      <!-- 使用说明 -->
      <view class="tips-section p-4 mt-8">
        <view class="tips-card bg-info-light border border-info-disabled rounded-xl p-4">
          <view class="flex items-center mb-2">
            <u-icon name="info-circle" size="18" color="#909399" class="mr-2"></u-icon>
            <text class="font-medium text-info">使用说明</text>
          </view>
          <view class="text-sm text-info leading-relaxed">
            <view>• 请输入完整的用户ID，不支持模糊搜索</view>
            <view>• 用户ID为8位数字组合</view>
            <view>• 为防止恶意搜索，每分钟最多可搜索{{ SEARCH_LIMIT }}次</view>
            <view>• 不能给自己赠送</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { getUserById, getRecentGiftUsers } from '@/api/gift'
import type { UserInfo } from '@/api/gift'

const userStore = useUserStore()

// 响应式数据
const searchKeyword = ref('')
const searchResult = ref<UserInfo | null>(null)
const recentUsers = ref<UserInfo[]>([])
const searching = ref(false)
const hasSearched = ref(false)

// 搜索频次限制
const searchAttempts = ref(0)
const lastSearchTime = ref(0)
const SEARCH_LIMIT = 10 // 每分钟最多搜索10次
const SEARCH_WINDOW = 60000 // 1分钟时间窗口
const MIN_SEARCH_INTERVAL = 2000 // 最小搜索间隔2秒

// 计算属性
const showEmptyState = computed(() => {
  return hasSearched.value && !searchResult.value && !searching.value
})

const emptyStateText = computed(() => {
  return searchKeyword.value ? '未找到该用户' : '请输入用户ID进行搜索'
})

// 方法
const clearSearch = () => {
  searchKeyword.value = ''
  searchResult.value = null
  hasSearched.value = false
}

// 检查搜索频次限制
const checkSearchLimit = () => {
  const now = Date.now()
  
  // 重置时间窗口
  if (now - lastSearchTime.value > SEARCH_WINDOW) {
    searchAttempts.value = 0
  }
  
  // 检查搜索间隔
  if (now - lastSearchTime.value < MIN_SEARCH_INTERVAL) {
    uni.showToast({
      title: '搜索过于频繁，请稍后再试',
      icon: 'none'
    })
    return false
  }
  
  // 检查搜索次数限制
  if (searchAttempts.value >= SEARCH_LIMIT) {
    uni.showToast({
      title: '搜索次数过多，请1分钟后再试',
      icon: 'none'
    })
    return false
  }
  
  return true
}

// 验证用户ID格式
const validateUserId = (userId: string) => {
  // 用户ID应该是8位数字
  const userIdRegex = /^\d{8}$/
  return userIdRegex.test(userId)
}

const searchUser = async () => {
  const userSn = searchKeyword.value.trim()
  
  // 清除之前的搜索结果
  searchResult.value = null
  
  if (!userSn) {
    uni.showToast({
      title: '请输入用户ID',
      icon: 'none'
    })
    return
  }
  
  // 验证用户ID格式
  if (!validateUserId(userSn)) {
    uni.showToast({
      title: '请输入正确的用户ID（8位数字）',
      icon: 'none'
    })
    return
  }
  
  // 检查是否是自己
  if (userSn === userStore.userInfo.sn) {
    uni.showToast({
      title: '不能给自己赠送',
      icon: 'none'
    })
    return
  }
  
  // 检查搜索频次限制
  if (!checkSearchLimit()) {
    return
  }
  
  // 更新搜索记录
  searchAttempts.value++
  lastSearchTime.value = Date.now()
  
  try {
    searching.value = true
    hasSearched.value = true
    
    const res = await getUserById(userSn)
    
    // 由于HTTP拦截器的处理，成功的请求会直接返回data
    if (res && res.id) {
      searchResult.value = res
      uni.showToast({
        title: '用户查找成功',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: '未查询到用户信息，请确认用户ID是否正确',
        icon: 'none'
      })
    }
    
  } catch (error: any) {
    searchResult.value = null
    console.error('搜索用户失败:', error)
    
    // 处理不同类型的错误，参考PC端的错误处理机制
    let errorMessage = '查询失败，请稍后重试'
    
    if (typeof error === 'string') {
      // 直接的错误信息字符串
      errorMessage = error
    } else if (error?.message) {
      // 错误对象的message属性
      errorMessage = error.message
    } else if (error?.response?.data?.msg) {
      // HTTP响应中的错误信息
      errorMessage = error.response.data.msg
    } else if (error?.response?.status) {
      // 根据HTTP状态码提供友好的错误信息
      switch (error.response.status) {
        case 400:
          errorMessage = '请求参数错误'
          break
        case 401:
          errorMessage = '请先登录'
          break
        case 403:
          errorMessage = '没有权限执行此操作'
          break
        case 404:
          errorMessage = '未查询到用户信息，请确认用户ID是否正确'
          break
        case 429:
          errorMessage = '搜索过于频繁，请稍后再试'
          break
        case 500:
          errorMessage = '服务器错误，请稍后重试'
          break
        default:
          errorMessage = '查询失败，请稍后重试'
      }
    }
    
    // 特殊错误处理
    if (errorMessage.includes('登录') || errorMessage.includes('token') || errorMessage.includes('未登录')) {
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      })
      // 可以考虑跳转到登录页面
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
      return
    }
    
    uni.showToast({
      title: errorMessage,
      icon: 'none'
    })
  } finally {
    searching.value = false
  }
}

const selectSearchUser = (user: UserInfo) => {
  selectUser(user)
}

const selectRecentUser = (user: UserInfo) => {
  if (user.id === userStore.userInfo.id) {
    uni.showToast({
      title: '不能给自己赠送',
      icon: 'none'
    })
    return
  }
  selectUser(user)
}

const selectUser = (user: UserInfo) => {
  console.log('选择用户:', user)
  
  // 防止重复点击
  if (searching.value) return
  
  try {
    // 使用 uni.$emit 发送事件
    uni.$emit('selectUser', user)
    
    // 也使用页面传值作为备用方案
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    
    if (prevPage) {
      (prevPage as any).selectedUser = user
    }
    
    // 显示选择成功提示
    uni.showToast({
      title: '已选择用户',
      icon: 'success',
      duration: 1000
    })
    
    // 延迟返回，确保提示显示
    setTimeout(() => {
      uni.navigateBack()
    }, 500)
    
  } catch (error) {
    console.error('选择用户失败:', error)
    uni.showToast({
      title: '选择失败，请重试',
      icon: 'none'
    })
  }
}

const loadRecentUsers = async () => {
  try {
    const data = await getRecentGiftUsers()
    recentUsers.value = data.data || []
  } catch (error: any) {
    console.error('加载最近用户失败:', error)
    recentUsers.value = []
  }
}

onMounted(() => {
  loadRecentUsers()
})
</script>

<style lang="scss" scoped>
.select-user {
  min-height: 100vh;
  background: $u-bg-color;
}

.search-section {
  .search-box {
    border: 1rpx solid $u-border-color;
  }
}

.recent-section {
  .recent-scroll {
    white-space: nowrap;
  }
  
  .recent-list {
    display: inline-flex;
    padding-bottom: 20rpx;
  }
  
  .recent-item {
    flex-shrink: 0;
    width: 120rpx;
    cursor: pointer;
    
    &:active {
      opacity: 0.7;
    }
  }
}

.result-section {
  .user-card {
    border: 1rpx solid $u-border-color;
    transition: all 0.2s;
    
    &:active {
      background: $u-bg-color;
      transform: scale(0.98);
    }
  }
}

.empty-state {
  .text-tips {
    display: block;
    font-size: 28rpx;
    color: $u-tips-color;
  }
}

.tips-section {
  .tips-card {
    background-color: $u-type-info-light;
    border-color: $u-type-info-disabled;
    
    view {
      margin-bottom: 8rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 使用系统颜色变量
.text-main {
  color: $u-main-color;
}

.text-content {
  color: $u-content-color;
}

.text-tips {
  color: $u-tips-color;
}

.text-info {
  color: $u-type-info;
}

.bg-info-light {
  background-color: $u-type-info-light;
}

.border-info-disabled {
  border-color: $u-type-info-disabled;
}
</style> 