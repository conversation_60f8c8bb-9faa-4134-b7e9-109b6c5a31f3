#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/telemetry/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/telemetry/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/telemetry/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/telemetry/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@nuxt/telemetry/bin/nuxt-telemetry.mjs" "$@"
else
  exec node  "$basedir/../@nuxt/telemetry/bin/nuxt-telemetry.mjs" "$@"
fi
