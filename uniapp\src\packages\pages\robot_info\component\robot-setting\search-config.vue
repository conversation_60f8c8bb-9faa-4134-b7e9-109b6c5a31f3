<template>
    <view>
        <u-form-item label="AI模型" prop="model_sub_id" required>
            <view class="flex-1 min-w-0">
                <model-picker
                    :setDefault="false"
                    v-model:id="formData.model_id"
                    v-model:sub_id="formData.model_sub_id"
                >
                    <template #default="{ item }">
                        <view class="input flex">
                            <view class="flex-1 min-w-0">
                                <view class="line-clamp-1">
                                    <text v-if="item.alias">{{ item.alias }}</text>
                                    <text class="text-[#888]" v-else>请选择</text>
                                    <text class="text-muted" v-if="item.alias && item.price == '0'">
                                        (免费)
                                    </text>
                                    <text class="text-muted" v-else-if="item.alias">
                                        ({{ `消耗${item.price}${appStore.getTokenUnit}/1千字符` }})
                                    </text>
                                </view>
                            </view>
                            <view
                                class="text-muted flex flex-none mx-[20rpx]"
                                v-if="formData.model_id"
                                @click.stop="formData.model_id = '';formData.model_sub_id=''"
                            >
                                <u-icon name="close-circle"/>
                            </view>
                            <view class="text-muted flex">
                                <u-icon name="arrow-down" />
                            </view>
                        </view>
                    </template>
                </model-picker>
            </view>
        </u-form-item>
        <u-form-item label="相似度" prop="search_similarity">
            <view class="flex-1">
                <view>
                    <app-slider
                        :min="0"
                        :max="1"
                        :step="0.001"
                        :activeColor="$theme.primaryColor"
                        v-model="formData.search_similarity"
                    ></app-slider>
                </view>

                <view class="text-muted">
                    输入0-1之间的数值，支持3位小数点；高相似度推荐设置0.8以上
                </view>
            </view>
        </u-form-item>
        <u-form-item label="单次搜索数量" prop="search_limits">
            <view class="flex-1 w-full">
                <view>
                    <app-slider
                        :min="0"
                        :max="20"
                        :activeColor="$theme.primaryColor"
                        v-model="formData.search_limits"
                    ></app-slider>
                </view>

                <view class="text-muted">
                    默认设置为5，请输入0-20之间的整数数值
                </view>
            </view>
        </u-form-item>
        <u-form-item label="空搜索回复" label-position="left" label-width="150">
            <view class="flex-1 flex justify-end">
                <u-radio-group v-model="formData.search_empty_type">
                    <u-radio :name="1"> AI回复 </u-radio>
                    <u-radio :name="2"> 自定义回复 </u-radio>
                </u-radio-group>
            </view>
        </u-form-item>
        <u-form-item v-if="formData.search_empty_type === 2">
            <view class="flex-1 min-w-0">
                <u-input
                    v-model="formData.search_empty_text"
                    type="textarea"
                    :height="200"
                    placeholder="请输入回复内容，当搜索匹配不上内容时，直接回复填写的内容"
                    :border="true"
                />
            </view>
        </u-form-item>

        <u-form-item label="上下文" prop="context_num">
            <view class="flex-1">
                <view>
                    <app-slider
                        :min="0"
                        :max="5"
                        :step="1"
                        :activeColor="$theme.primaryColor"
                        v-model="formData.context_num"
                    ></app-slider>
                </view>
                <view class="text-muted">
                    生成文本的最大长度，取值范围为0~5之间的整数
                </view>
            </view>
        </u-form-item>

        <u-form-item
            label="文件解析"
            label-position="left"
            label-width="200"
            prop="support_file"
        >
            <view class="flex-1">
                <view class="flex justify-end items-center">
                    <u-switch
                        v-model="formData.support_file"
                        :active-value="1"
                        :inactive-value="0"
                        :size="40"
                    ></u-switch>
                    <view class="ml-[12rpx]">
                        {{ formData.support_file == 1 ? '启用' : '关闭' }}
                    </view>
                </view>
            </view>
        </u-form-item>
        <view class="text-muted">开启后对话时支持上传文件，需消耗大量token，按需启用</view>

        <u-form-item
            label="重排功能"
            label-position="left"
            label-width="200"
            prop="ranking_status"
        >
            <view class="flex-1">
                <view class="flex justify-end items-center">
                    <u-radio-group v-model="formData.ranking_status">
                        <u-radio :name="0"> 关闭 </u-radio>
                        <u-radio :name="1"> 启用 </u-radio>
                    </u-radio-group>
                </view>
            </view>
        </u-form-item>
        <view class="text-muted">开启后，则会对从数据库检索的内容进行重新排序(去最高分数据)</view>

        <u-form-item v-if="formData.ranking_status === 1" label="重排分数" prop="ranking_score">
            <view class="flex-1">
                <view>
                    <app-slider
                        :min="0"
                        :max="1"
                        :step="0.001"
                        :activeColor="$theme.primaryColor"
                        v-model="formData.ranking_score"
                    ></app-slider>
                </view>
                <view class="text-muted">表示如果数据重排后，分数没有达到该值则会过滤掉</view>
            </view>
        </u-form-item>

        <u-form-item v-if="formData.ranking_status === 1" label="重排模型" prop="ranking_model" required>
            <view class="flex-1 min-w-0">
                <model-picker
                    type="rankingModels"
                    :setDefault="false"
                    v-model:id="formData.ranking_model"
                >
                    <template #default="{ item }">
                        <view class="input flex">
                            <view class="flex-1 min-w-0">
                                <view class="line-clamp-1">
                                    <text v-if="item.alias">{{ item.alias }}</text>
                                    <text class="text-[#888]" v-else>请选择重排模型</text>
                                </view>
                            </view>
                            <view
                                class="text-muted flex flex-none mx-[20rpx]"
                                v-if="formData.ranking_model"
                                @click.stop="formData.ranking_model = ''"
                            >
                                <u-icon name="close-circle"/>
                            </view>
                            <view class="text-muted flex">
                                <u-icon name="arrow-down" />
                            </view>
                        </view>
                    </template>
                </model-picker>
            </view>
        </u-form-item>
        <view v-if="formData.ranking_status === 1" class="text-muted">选择用于重排的AI模型，不同模型的准确性和响应速度不同</view>
    </view>
</template>

<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { useAppStore } from '@/stores/app'
const props = withDefaults(
    defineProps<{
        modelValue: Record<string, any>
    }>(),
    {}
)

const emit = defineEmits<{
    (event: 'update:modelValue', value: Record<string, any>): void
}>()
const appStore = useAppStore()
const formData = useVModel(props, 'modelValue', emit)
</script>

<style lang="scss" scoped>
.input {
    min-height: 70rpx;
    width: 100%;
    border-radius: 8rpx;
    line-height: 50rpx;
    border: 1px solid var(--color-light, #e5e5e5);
    &-placeholder {
        color: rgb(136, 136, 136);
    }
    padding: 10rpx 20rpx;
    box-sizing: border-box;
}
</style>
