UPDATE `cm_creation_model` SET
`name` = '马拉松跑步训练计划',
`image` = 'uploads/images/20241205/20241205160952a93941362.png',
`sort` = 1,
`category_id` = 6,
`status` = 1,
`content` = '你是一名专业的马拉松运动教练，拥有丰富的跑步训练和运动科学知识。请根据以下信息，为该用户制定一套科学严谨、个性化且详细的马拉松跑步训练计划。计划应包括但不限于：每周的训练内容（跑量、配速、间歇、LSD等）、力量训练建议、交叉训练建议、详细的饮食和补水策略（赛前、赛中、赛后）、充足的休息和恢复方法、推荐的跑步装备（跑鞋、服装等）、以及常见的伤病预防和处理技巧。\n\n用户信息如下：\n年龄：${aaa}岁\n身高：${bbb}cm\n体重：${ccc}kg\n跑步目标：${lxy2jyg5}\n目标完赛时间：${lxy2jyg6}\n历史最大单次跑程：${ddd}km\n半马PB（如有）：${eee}\n每周可训练天数：${lxy2jyg7}\n当前健康状况及伤病史：${lxy2jyg8}\n是否有智能穿戴设备：${lxy2jyg9}\n其他特殊要求：${lxy2jyga}\n\n请确保计划具有可操作性，循序渐进，并能帮助用户安全有效地达成跑步目标。在计划开始前，请提醒用户进行健康评估，并在训练过程中注意身体反应，及时调整。提供一些激励性的话语。内容尽量详尽。',
`tips` = '根据您的跑步目标和身体状况，DeepSeek教练为您量身定制马拉松训练计划。',
`context_num` = 0,
`n` = 1,
`top_p` = 0.9,
`presence_penalty` = 0.5,
`frequency_penalty` = 0.0,
`temperature` = 0.7,
`max_tokens` = 0, -- 通常表示使用模型的默认最大值或由后端配置
`form` = '[{"name":"WidgetInput","title":"单行文本","id":"madc1b8p","props":{"field":"aaa","title":"年龄（岁）","defaultValue":"","placeholder":"例如：30","maxlength":3,"isRequired":true}},{"name":"WidgetInput","title":"单行文本","id":"madc1b8q","props":{"field":"bbb","title":"身高（cm）","defaultValue":"","placeholder":"例如：175","maxlength":3,"isRequired":true}},{"name":"WidgetInput","title":"单行文本","id":"madc1b8r","props":{"field":"ccc","title":"体重（kg）","defaultValue":"","placeholder":"例如：65","maxlength":3,"isRequired":true}},{"name":"WidgetSelect","title":"下拉选项","id":"m8gzl86a","props":{"field":"lxy2jyg5","title":"您的跑步目标","options":["完成首个5公里","完成首个10公里","完成首个半程马拉松","完成首个全程马拉松","提高半马成绩","提高全马成绩","日常健康跑/健身"],"defaultValue":"完成首个全程马拉松","isRequired":true}},{"name":"WidgetInput","title":"单行文本","id":"m8gzl86b","props":{"field":"lxy2jyg6","title":"目标完赛时间（选填）","defaultValue":"","placeholder":"例如：全马4小时内 / 半马2小时内","maxlength":50,"isRequired":false}},{"name":"WidgetInput","title":"单行文本","id":"madc1b8s","props":{"field":"ddd","title":"近期最大单次跑程（km）","defaultValue":"","placeholder":"例如：10","maxlength":3,"isRequired":true}},{"name":"WidgetInput","title":"单行文本","id":"madc1b8t","props":{"field":"eee","title":"半程马拉松PB（个人最好成绩，选填）","defaultValue":"","placeholder":"例如：1:45:00 或 1小时45分钟","maxlength":50,"isRequired":false}},{"name":"WidgetSelect","title":"下拉选项","id":"m8gzl86c","props":{"field":"lxy2jyg7","title":"每周可用于训练的天数","options":["2-3天","3-4天","4-5天","5-6天","6-7天"],"defaultValue":"3-4天","isRequired":true}},{"name":"WidgetTextarea","title":"多行文本","id":"m8gzl86d","props":{"field":"lxy2jyg8","title":"当前健康状况及主要伤病史（选填）","placeholder":"例如：膝盖偶有不适、无重大疾病史","rows":3,"defaultValue":"","maxlength":300,"autosize":false,"isRequired":false}},{"name":"WidgetRadio","title":"单选","id":"m8gzl86e","props":{"field":"lxy2jyg9","title":"是否有智能穿戴设备（如心率表、GPS手表）","options":["有","没有"],"defaultValue":"有","isRequired":true}},{"name":"WidgetTextarea","title":"多行文本","id":"m8gzl86f","props":{"field":"lxy2jyga","title":"其他特殊要求或备注（选填）","placeholder":"例如：希望包含较多的核心力量训练","rows":3,"defaultValue":"","maxlength":300,"autosize":false,"isRequired":false}}]',
`virtual_use_num` = 2879, -- 保持原有值或根据需要更新
`system` = '你是一名顶级的马拉松跑步教练和运动营养专家。你的回答应该专业、细致、富有鼓励性，并始终将用户的健康和安全放在首位。',
`update_time` = UNIX_TIMESTAMP() -- 设置为当前时间
WHERE `id` = 33; -- 假设马拉松计划的ID为33 