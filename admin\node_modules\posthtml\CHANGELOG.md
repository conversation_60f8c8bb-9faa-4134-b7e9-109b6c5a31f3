<a name="0.9.2"></a>
## [0.9.2](https://github.com/posthtml/posthtml/compare/v0.9.1...v0.9.2) (2017-01-20)


### Bug Fixes

* Cannot read property 'length' of undefined ([03da01e](https://github.com/posthtml/posthtml/commit/03da01e))
* **index:** trailing space ([3cd00b7](https://github.com/posthtml/posthtml/commit/3cd00b7))
* **index:** typo in require method ([088c9e3](https://github.com/posthtml/posthtml/commit/088c9e3))
* **test:** fix tree for test options set ([03286ea](https://github.com/posthtml/posthtml/commit/03286ea))



<a name="0.9.1"></a>
## [0.9.1](https://github.com/posthtml/posthtml/compare/v0.9.0...v0.9.1) (2016-09-29)



<a name="0.9.0"></a>
# [0.9.0](https://github.com/posthtml/posthtml/compare/v0.8.7...v0.9.0) (2016-07-06)


### Bug Fixes

* fix run jscs after version update ([124f857](https://github.com/posthtml/posthtml/commit/124f857))


### Features

* make parser configurable ([bfa3e6d](https://github.com/posthtml/posthtml/commit/bfa3e6d))
* **lib:** make use method variadic ([43d6a6b](https://github.com/posthtml/posthtml/commit/43d6a6b))



<a name="0.8.7"></a>
## [0.8.7](https://github.com/posthtml/posthtml/compare/v0.8.6...v0.8.7) (2016-05-04)


### Performance Improvements

* **lib:** on demand html rendering ([4ef0df9](https://github.com/posthtml/posthtml/commit/4ef0df9))



<a name="0.8.6"></a>
## [0.8.6](https://github.com/posthtml/posthtml/compare/v0.8.5...v0.8.6) (2016-04-26)


### Bug Fixes

* **api:** report true for boolean attrs ([70cec9d](https://github.com/posthtml/posthtml/commit/70cec9d))
* **docs:** correct typo ([b5133f5](https://github.com/posthtml/posthtml/commit/b5133f5))



<a name="0.8.5"></a>
## [0.8.5](https://github.com/posthtml/posthtml/compare/v0.8.4...v0.8.5) (2016-03-31)


### Bug Fixes

* **README:** fix style in Readme ([9944446](https://github.com/posthtml/posthtml/commit/9944446))

### Performance Improvements

* **api:** remove extra slice call ([8a3ff11](https://github.com/posthtml/posthtml/commit/8a3ff11))



<a name="0.8.4"></a>
## [0.8.4](https://github.com/posthtml/posthtml/compare/v0.8.3...v0.8.4) (2016-03-30)


### Bug Fixes

* promise check and tests ([79bcff9](https://github.com/posthtml/posthtml/commit/79bcff9))

### Features

* **docs:** update README, add docs folder ([619a97c](https://github.com/posthtml/posthtml/commit/619a97c))



<a name="0.8.3"></a>
## [0.8.3](https://github.com/posthtml/posthtml/compare/v0.8.2...v0.8.3) (2016-02-26)


### Bug Fixes

* promise check and tests ([0162f97](https://github.com/posthtml/posthtml/commit/0162f97))



<a name="0.8.2"></a>
## [0.8.2](https://github.com/posthtml/posthtml/compare/v0.8.1...v0.8.2) (2016-02-05)


### Features

* remove compilations. Support for NodeJS v0.10 ([ac437b7](https://github.com/posthtml/posthtml/commit/ac437b7))



<a name"0.8.1"></a>
### 0.8.1 (2016-01-08)


#### Bug Fixes

* **api:** fix binds tree for import API ([a8f25007](https://github.com/posthtml/posthtml/commit/a8f25007))
* **docs:** fix readme ([cd61bc11](https://github.com/posthtml/posthtml/commit/cd61bc11))


#### Features

* **docs:**
  * add more plugins in plugin list ([52bfcad2](https://github.com/posthtml/posthtml/commit/52bfcad2))
  * add Gitter chat badge ([97ba9847](https://github.com/posthtml/posthtml/commit/97ba9847))
  * add new plugins link & add plugin boilerplate link ([40644039](https://github.com/posthtml/posthtml/commit/40644039))


<a name"0.8.0"></a>
## 0.8.0 (2015-11-24)


#### Bug Fixes

* **docs:** delete parse section ([16511096](https://github.com/posthtml/posthtml/commit/16511096))


#### Features

* ***:** upd nodejs deps in travis.yml ([481378db](https://github.com/posthtml/posthtml/commit/481378db))
* **api:** chaining ([03080a6e](https://github.com/posthtml/posthtml/commit/03080a6e))
* **docs:** upd plugin sections in Readme ([c69bc2b9](https://github.com/posthtml/posthtml/commit/c69bc2b9))


<a name"0.7.0"></a>
## 0.7.0 (2015-10-21)


#### Features

* ***:** new dependencies·for posthtml-parser posthtml-render ([a5bc312b](https://github.com/posthtml/posthtml/commit/a5bc312b))
* **api:** delete matchClass method ([912f72ad](https://github.com/posthtml/posthtml/commit/912f72ad))
* **docs:** add dependency info to readme ([6c3419cf](https://github.com/posthtml/posthtml/commit/6c3419cf))


<a name"0.6.0"></a>
## 0.6.0 (2015-10-17)


#### Features

* ***:** fix code style ([7a743f78](https://github.com/posthtml/posthtml/commit/7a743f78))
* **api:** Support RegExp in matcher Object ([e3bd9918](https://github.com/posthtml/posthtml/commit/e3bd9918))
* **docs:** add matchClass deprecated info ([14f1757e](https://github.com/posthtml/posthtml/commit/14f1757e))


<a name"0.5.0"></a>
## 0.5.0 (2015-10-10)


#### Features

* **api:** support RegExp matchers ([783c5663](https://github.com/posthtml/posthtml/commit/783c5663))
* **docs:**
  * add project-stub link. Fix long titles. Upd PostHTMLTree example ([57f48334](https://github.com/posthtml/posthtml/commit/57f48334))
  * add posthtml-retext info & upd links ([541dbc03](https://github.com/posthtml/posthtml/commit/541dbc03))
  * add license info to readme ([32300239](https://github.com/posthtml/posthtml/commit/32300239))
  * add posthtml-bem plugin in readme ([2eea4b19](https://github.com/posthtml/posthtml/commit/2eea4b19))


<a name"0.4.1"></a>
### 0.4.1 (2015-10-04)


#### Bug Fixes

* **posthtml:** fix build ES2015 ([829ba49c](https://github.com/posthtml/posthtml/commit/829ba49c))


<a name"0.4.0"></a>
## 0.4.0 (2015-10-03)


#### Bug Fixes

* **lint:** fix jscsrc ([a534e0a0](https://github.com/posthtml/posthtml/commit/a534e0a0))
* **posthtml:**
  * extend new object with api methods on each plugin call ([82e096ea](https://github.com/posthtml/posthtml/commit/82e096ea))
  * code style fix ([d1b3484d](https://github.com/posthtml/posthtml/commit/d1b3484d))
  * code style fix ([26e6d7e3](https://github.com/posthtml/posthtml/commit/26e6d7e3))


#### Features

* **api:** handle array matchers ([335b5aac](https://github.com/posthtml/posthtml/commit/335b5aac))
* **docs:**
  * write array matchers example in jsdocs/readme ([a14b7675](https://github.com/posthtml/posthtml/commit/a14b7675))
  * add logo to readme ([78740c34](https://github.com/posthtml/posthtml/commit/78740c34))
* **lint:** upd jscs ([cef42d5d](https://github.com/posthtml/posthtml/commit/cef42d5d))
* **posthtml:** implement truly sync and async modes, and tests for them ([337243f5](https://github.com/posthtml/posthtml/commit/337243f5))


<a name"0.3.0"></a>
## 0.3.0 (2015-09-25)


#### Features

* **parser:** skip template if tag === false ([3cc9e59f](https://github.com/posthtml/posthtml/commit/3cc9e59f))


<a name"0.2.1"></a>
### 0.2.1 (2015-09-24)


#### Features

* **api:** optimize matchClass func ([b0311cd7](https://github.com/posthtml/posthtml/commit/b0311cd7))
* **docs:**
  * Write example Gulp plugin for PostHTML in docs ([b9c8ceff](https://github.com/posthtml/posthtml/commit/b9c8ceff))
  * Add Textr plugin to Readme ([bdd3270b](https://github.com/posthtml/posthtml/commit/bdd3270b))


<a name"0.2.0"></a>
## 0.2.0 (2015-09-23)


#### Bug Fixes

* **test:** fix typo & cleanup ([e33ba7fa](https://github.com/posthtml/posthtml/commit/e33ba7fa))


#### Features

* **api:**
  * rename eachClass to matchClass ([efc9b349](https://github.com/posthtml/posthtml/commit/efc9b349))
  * use options in plugin ([0d8c4555](https://github.com/posthtml/posthtml/commit/0d8c4555))
* **docs:** `options` docs write in readme ([d72c2741](https://github.com/posthtml/posthtml/commit/d72c2741))
* **lint:** own jscs config ([74332ab8](https://github.com/posthtml/posthtml/commit/74332ab8))
* **parser:**
  * toHtml tests ([789ee545](https://github.com/posthtml/posthtml/commit/789ee545))
  * own render html func ([1520e5ff](https://github.com/posthtml/posthtml/commit/1520e5ff))


<a name"0.1.0"></a>
## 0.1.0 (2015-09-14)


#### Features

* **api:** base API ([096654a6](https://github.com/posthtml/posthtml/commit/096654a6))


<a name"0.0.4"></a>
### 0.0.4 (2015-09-13)


#### Bug Fixes

* **lib:** fix option declare ([db95e066](https://github.com/posthtml/posthtml/commit/db95e066))


#### Features

* ***:** upd deps ([054bd94c](https://github.com/posthtml/posthtml/commit/054bd94c))
* **lib:** delete getOptions method ([931a03a0](https://github.com/posthtml/posthtml/commit/931a03a0))
* **parser:**
  * no skip parse text simbols ([42b4d156](https://github.com/posthtml/posthtml/commit/42b4d156))
  * Own parser html to tree ([748d8f1e](https://github.com/posthtml/posthtml/commit/748d8f1e))
  * init ([1ca1b39b](https://github.com/posthtml/posthtml/commit/1ca1b39b))
* **readme:** add tree example ([e46e9bc2](https://github.com/posthtml/posthtml/commit/e46e9bc2))


<a name"0.0.3"></a>
### 0.0.3 (2015-08-13)

* ***:** upd dependencies ([b1f4f2664106034d6fd530962a4f9bd9c378d17a](https://github.com/posthtml/posthtml/commit/b1f4f2664106034d6fd530962a4f9bd9c378d17a))

#### Features

* **lint:** use jshint ([504f3c06](https://github.com/posthtml/posthtml/commit/504f3c06))


<a name"0.0.2"></a>
### 0.0.2 (2015-07-02)


#### Features

* ***:**
  * add-travis-and-coverage ([92c9ee81](https://github.com/posthtml/posthtml/commit/92c9ee81))
  * changelog build ([044fd58d](https://github.com/posthtml/posthtml/commit/044fd58d))
  * deep results objects #13 ([96b86c90](https://github.com/posthtml/posthtml/commit/96b86c90))
  * add skipParse options ([45eeb9e3](https://github.com/posthtml/posthtml/commit/45eeb9e3))
* **gulp:** refactor config ([2fe4ecb2](https://github.com/posthtml/posthtml/commit/2fe4ecb2))


<a name"0.0.1"></a>
### 0.0.1
init
