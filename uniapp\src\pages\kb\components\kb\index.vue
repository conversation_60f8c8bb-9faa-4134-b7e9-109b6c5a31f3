<template>
    <view class="content">
        <z-paging
            ref="pagingRef"
            v-model="dataList"
            :auto-clean-list-when-reload="false"
            @query="queryList"
            :fixed="false"
        >
            <template #top>
                <!-- 醒目的新增操作区域 -->
                <view class="px-[30rpx] py-[20rpx] bg-white">
                    <view class="bg-gradient-to-r from-green-50 to-blue-50 rounded-[20rpx] p-[30rpx] mb-[20rpx]">
                        <view class="text-center">
                            <view class="text-[32rpx] font-bold text-[#333] mb-[16rpx]">创建您的专属知识库</view>
                            <view class="text-[26rpx] text-[#666] mb-[30rpx]">构建智能问答系统，提升工作效率</view>
                            
                            <view class="flex justify-center">
                                <!-- 快速创建 -->
                                <view class="flex flex-col items-center" @click="handelShowAdd">
                                    <view class="w-[120rpx] h-[120rpx] kb-create-btn rounded-[20rpx] flex items-center justify-center mb-[20rpx] shadow-lg relative">
                                        <u-icon name="plus-circle" color="#fff" size="40" />
                                        <view class="absolute -top-[6rpx] -right-[6rpx] w-[24rpx] h-[24rpx] bg-[#ff4757] rounded-full flex items-center justify-center">
                                            <text class="text-white text-[20rpx] font-bold">+</text>
                                        </view>
                                    </view>
                                    <view class="text-[28rpx] font-bold text-[#333]">快速创建</view>
                                    <view class="text-[24rpx] text-[#666] mt-[8rpx]">立即开始</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                
                <view class="py-[20rpx] px-[20rpx]">
                    <view class="bg-white p-[8rpx] flex rounded-[8rpx]">
                        <view
                            v-for="item in tabState.list"
                            :key="item.type"
                            class="px-[9rpx] py-[14rpx] rounded-[8rpx] flex-1 text-center"
                            :class="{
                                'tab-active': item.type == tabState.current
                            }"
                            @click="tabsChange(item.type)"
                        >
                            {{ item.name }}
                        </view>
                    </view>
                </view>
            </template>
            <view class="grid grid-cols-2 gap-2 px-[20rpx]">
                <view
                    class="bg-white rounded-lg overflow-hidden"
                    v-for="item in dataList"
                    :key="item.id"
                    @click="toDetail(item.id)"
                >
                    <view class="flex relative">
                        <u-image
                            class="w-full"
                            :src="item.image"
                            width="100%"
                            height="272rpx"
                        ></u-image>
                        <view
                            class="bg-[rgba(0,0,0,0.4)] text-white absolute px-[10rpx] py-[2rpx] right-[10rpx] bottom-[10rpx] rounded-[6rpx] flex items-center"
                        >
                            <u-icon name="man-add" />
                            <view class="ml-1 text-sm">
                                {{ item.team_people }}
                            </view>
                        </view>
                    </view>

                    <view class="p-[20rpx]">
                        <view class="text-xl font-bold">{{ item?.name }}</view>
                        <view
                            class="text-sm text-info mt-[24rpx] line-clamp-3"
                            >{{ item?.intro || '这个知识库还没有介绍~' }}</view
                        >
                    </view>
                </view>
            </view>
        </z-paging>
        <addBtn @click="handelShowAdd"></addBtn>
        <addPopup
            v-model="showAdd"
            @close="
                () => {
                    showAdd = false
                    reload()
                }
            "
        />
    </view>
</template>

<script lang="ts" setup>
import addBtn from '../add-btn.vue'
import addPopup from './add-popup.vue'
import { getKBList } from '@/api/kb'
import { nextTick, reactive, ref, shallowRef } from 'vue'
import { useRouter } from 'uniapp-router-next'
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()
const props = defineProps<{
    tabIndex: number
    currentIndex: number
}>()

const tabState = reactive({
    list: [
        {
            name: '全部知识库',
            type: 0
        },
        {
            name: '我的知识库',
            type: 1
        },
        {
            name: '共享给我',
            type: 2
        }
    ],
    current: 0
})

const tabsChange = (type: number) => {
    tabState.current = type
    reload()
}

const showAdd = ref(false)

const dataList = ref()

const pagingRef = shallowRef()

const router = useRouter()

const queryList = async (pageNo: number, pageSize: number) => {
    try {
        const { lists = [] } = await getKBList({
            page_size: pageSize,
            page_no: pageNo,
            type: tabState.current
        })

        pagingRef.value?.complete(lists)
    } catch (error) {
        pagingRef.value?.complete(false)
    }
}

const toDetail = (id: number) => {
    router.navigateTo({
        path: `/packages/pages/kb_info/kb_info?id=${id}`
    })
}

const handelShowAdd = async () => {
    if (!userStore.isLogin) return router.navigateTo('/pages/login/login')

    showAdd.value = true
}

const reload = () => {
    nextTick(() => {
        // 刷新列表数据(如果不希望列表pageNo被重置可以用refresh代替reload方法)
        pagingRef.value && pagingRef.value.refresh()
    })
}


</script>

<style lang="scss" scoped>
/* 注意:父节点需要固定高度，z-paging的height:100%才会生效 */
.content {
    height: 100%;
}

.tab-active {
    background: linear-gradient(
        90deg,
        var(--color-minor) 0%,
        var(--color-primary) 100%
    );
    @apply text-white;
}

/* 渐变背景样式 */
.bg-gradient-to-r {
    background: linear-gradient(90deg, #f0fdf4 0%, #eff6ff 100%);
}

.bg-gradient-to-br {
    background: linear-gradient(135deg, var(--from-color), var(--to-color));
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 自定义渐变色 */
.kb-create-btn {
    background: linear-gradient(135deg, #10b981, #059669);
}
</style>
