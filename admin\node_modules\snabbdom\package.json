{"name": "snabbdom", "version": "3.6.2", "description": "A virtual DOM library with focus on simplicity, modularity, powerful features and performance.", "homepage": "https://github.com/snabbdom/snabbdom#readme", "repository": {"type": "git", "url": "git+https://github.com/snabbdom/snabbdom.git"}, "keywords": ["virtual", "dom", "vdom", "light", "kiss", "performance"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/snabbdom/snabbdom/issues"}, "engines": {"node": ">=12.17.0"}, "files": ["build"], "type": "module", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "tsc && ts-add-js-extension --dir=build && replace-in-file '\"./h\"' '\"./h.js\"' build/jsx.*", "examples": "serve .", "format": "prettier --write .", "format-check": "prettier --check .", "prepare": "husky install", "lint": "eslint .", "unit": "web-test-runner \"test/unit/*.{ts,tsx}\" --node-resolve --coverage", "release": "npm run test && release-it", "test": "npm run build && npm run lint && npm run unit", "test:watch": "web-test-runner \"test/unit/*.{tsx,tsx}\" --node-resolve --watch"}, "devDependencies": {"@esm-bundle/chai": "^4.3.4-fix.0", "@release-it/conventional-changelog": "^7.0.2", "@types/chai": "4.3.9", "@types/mocha": "10.0.3", "@typescript-eslint/eslint-plugin": "6.8.0", "@typescript-eslint/parser": "^6.8.0", "@web/dev-server-esbuild": "^0.4.3", "@web/test-runner": "^0.17.2", "@web/test-runner-browserstack": "^0.6.2", "commithelper": "^1.2.0", "conventional-changelog-angular": "^7.0.0", "eslint": "8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.29.0", "eslint-plugin-markdown": "3.0.1", "eslint-plugin-node": "11.1.0", "husky": "8.0.3", "lint-staged": "^15.0.2", "mocha": "10.2.0", "prettier": "^3.0.3", "release-it": "^16.2.1", "replace-in-file": "^7.1.0", "serve": "^14.2.1", "ts-add-js-extension": "^1.6.0", "typescript": "5.2.2"}, "prettier": {"trailingComma": "none"}, "lint-staged": {"*.(ts|tsx|js|md)": "prettier --write"}, "release-it": {"git": {"commitMessage": "chore(release): v${version}"}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "commithelper": {"scopeOverrides": {"chore": ["tools", "refactor", "release", "test", "deps", "docs", "examples"]}}}