import request from '@/utils/request'

// 获取模板库列表
export function getTemplateList(params: any) {
    return request.get({ url: '/kb.template/lists', params })
}

// 获取模板库详情
export function getTemplateDetail(params: any) {
    return request.get({ url: '/kb.template/detail', params })
}

// 添加模板库
export function addTemplate(params: any) {
    return request.post({ url: '/kb.template/add', params })
}

// 编辑模板库
export function editTemplate(params: any) {
    return request.post({ url: '/kb.template/edit', params })
}

// 删除模板库
export function deleteTemplate(params: any) {
    return request.post({ url: '/kb.template/del', params })
}

// 修改模板库状态
export function updateTemplateStatus(params: any) {
    return request.post({ url: '/kb.template/status', params })
}

// 获取类别下拉选择列表
export function getTemplateCategoryList() {
    return request.get({ url: '/kb.template/getCategoryList' })
}

// 根据类别ID获取模板列表（用于PC/H5端）
export function getTemplateListByCategory(params: any) {
    return request.get({ url: '/kb.template/getListByCategoryId', params })
}

// 获取所有模板（按类别分组，用于PC/H5端）
export function getAllTemplates() {
    return request.get({ url: '/kb.template/getAllTemplates' })
}

// 下载模板（记录下载次数）
export function downloadTemplate(params: any) {
    return request.get({ url: '/kb.template/download', params })
} 