-- 智能体分成收益定时任务自动添加SQL
-- 执行日期: 2024-12-19
-- 功能说明: 在系统定时任务表中添加智能体分成收益批量结算任务

-- 注意：请根据实际表前缀调整表名
-- 默认使用cm_前缀，如果不同请修改

-- 添加智能体分成收益批量结算定时任务
INSERT INTO `cm_dev_crontab` (`name`, `type`, `system`, `remark`, `command`, `params`, `status`, `expression`, `error`, `last_time`, `time`, `max_time`, `create_time`, `update_time`) 
VALUES 
(
    '智能体分成收益批量结算', 
    1, 
    0, 
    '每日自动结算智能体分成收益，处理待结算记录并分配收益给分享者', 
    'robot_revenue_settle', 
    '200', 
    2, 
    '0 2 * * *', 
    NULL, 
    NULL, 
    '0', 
    '0', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP()
);

-- 字段说明：
-- name: 任务名称
-- type: 1-定时任务
-- system: 0-非系统任务（可在后台管理）
-- remark: 任务描述
-- command: 对应Console命令名称
-- params: 命令参数，这里设置批量大小为200
-- status: 2-停止状态（需要手动启动）
-- expression: cron表达式，每日凌晨2点执行
-- create_time/update_time: 时间戳

-- 使用说明：
-- 1. 执行此SQL后，定时任务会出现在后台管理界面
-- 2. 可以在"系统设置 → 定时任务"中管理这个任务
-- 3. 需要手动启动任务（status改为1）
-- 4. 可以调整执行时间和批量大小
-- 5. 支持在线启动/停止/编辑任务 