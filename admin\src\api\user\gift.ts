import request from '@/utils/request'

// 获取赠送记录列表
export function giftRecordsApi(params: any) {
    return request.get({ url: '/user.gift/records', params })
}

// 获取赠送记录详情
export function giftDetailApi(params: { id: number }) {
    return request.get({ url: '/user.gift/detail', params })
}

// 撤回赠送记录
export function giftRevokeApi(data: { id: number; remark?: string }) {
    return request.post({ url: '/user.gift/revoke', data })
}

// 导出赠送记录
export function giftExportApi(params: any) {
    return request.get({ url: '/user.gift/export', params })
}

// 获取赠送配置
export function giftGetConfigApi() {
    return request.get({ url: '/user.gift/getConfig' })
}

// 保存赠送配置
export function giftSaveConfigApi(data: any) {
    return request.post({ url: '/user.gift/saveConfig', data })
}

// 获取统计数据
export function giftGetStatisticsApi(params?: any) {
    return request.get({ url: '/user.gift/getStatistics', params })
} 