#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/nitropack@2.10.4_typescript@4.9.3/node_modules/nitropack/dist/cli/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nitropack@2.10.4_typescript@4.9.3/node_modules/nitropack/dist/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nitropack@2.10.4_typescript@4.9.3/node_modules/nitropack/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nitropack@2.10.4_typescript@4.9.3/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/nitropack@2.10.4_typescript@4.9.3/node_modules/nitropack/dist/cli/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nitropack@2.10.4_typescript@4.9.3/node_modules/nitropack/dist/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nitropack@2.10.4_typescript@4.9.3/node_modules/nitropack/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nitropack@2.10.4_typescript@4.9.3/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../nitropack/dist/cli/index.mjs" "$@"
else
  exec node  "$basedir/../../../nitropack/dist/cli/index.mjs" "$@"
fi
