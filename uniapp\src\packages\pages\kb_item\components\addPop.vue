<template>
    <u-popup
        v-model="show"
        safe-area-inset-bottom
        closeable
        border-radius="16"
        mode="bottom"
        @close="$emit('close')"
    >
        <view class="h-[70vh] flex flex-col mx-[20rpx]">
            <view
                class="text-xl py-[28rpx] font-bold border-b border-solid border-light border-0"
            >
                录入数据
            </view>
            
            <!-- 重要提示 -->
            <view class="important-notice mt-[20rpx] mb-[20rpx]">
                <view class="notice-header">
                    <text class="notice-icon">⚠️</text>
                    <text class="notice-title">重要提示</text>
                </view>
                <view class="notice-content">
                    为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。
                </view>
            </view>
            
            <!-- 醒目提示 -->
            <view class="example-tip mb-[20rpx]">
                <view class="tip-header">
                    <text class="tip-icon">💡</text>
                    <text class="tip-title">小贴士</text>
                </view>
                <view class="tip-content">
                    可以试试直接选择示例，快速掌握知识库使用方法，成为AI达人
                </view>
            </view>
            
            <scroll-view class="flex min-h-0 h-full" scroll-y>
                <view class="mb-[20rpx]">
                    <u-row gutter="20">
                        <u-col span="6">
                            <u-form-item>
                                <!-- #ifdef H5 -->
                                <select
                                    @change="handleCategoryChangeH5"
                                    class="w-full p-[20rpx] border border-solid border-[#DCDFE6] rounded"
                                    :disabled="pickerCategories.length === 0"
                                >
                                    <option value="">请选择示例类别 ({{ pickerCategories.length }}个)</option>
                                    <option 
                                        v-for="(category, index) in pickerCategories" 
                                        :key="category.id" 
                                        :value="index"
                                    >
                                        {{ category.name }}
                                    </option>
                                </select>
                                <!-- #endif -->
                                
                                <!-- #ifndef H5 -->
                                <picker
                                    mode="selector"
                                    :range="pickerCategories"
                                    range-key="name"
                                    @change="handleCategoryChange"
                                    :disabled="pickerCategories.length === 0"
                                >
                                    <view class="p-[20rpx] border border-solid border-[#DCDFE6] rounded">
                                        <text v-if="!selectedCategoryName" class="text-gray">请选择示例类别 ({{ pickerCategories.length }}个)</text>
                                        <text v-else>{{ selectedCategoryName }}</text>
                                    </view>
                                </picker>
                                <!-- #endif -->
                            </u-form-item>
                        </u-col>
                        <u-col span="6">
                            <u-form-item>
                                <!-- #ifdef H5 -->
                                <select
                                    @change="handleExampleChangeH5"
                                    class="w-full p-[20rpx] border border-solid border-[#DCDFE6] rounded"
                                    :disabled="!selectedCategoryId"
                                    :class="{ 'opacity-50': !selectedCategoryId }"
                                >
                                    <option value="">请选择具体示例</option>
                                    <option 
                                        v-for="(example, index) in currentExamples" 
                                        :key="example.id" 
                                        :value="index"
                                    >
                                        {{ example.title }}
                                    </option>
                                </select>
                                <!-- #endif -->
                                
                                <!-- #ifndef H5 -->
                                <picker
                                    mode="selector"
                                    :range="currentExamples"
                                    range-key="title"
                                    @change="handleExampleChange"
                                    :disabled="!selectedCategoryId"
                                >
                                    <view class="p-[20rpx] border border-solid border-[#DCDFE6] rounded" :class="{ 'opacity-50': !selectedCategoryId }">
                                        <text v-if="!selectedExampleTitle" class="text-gray">请选择具体示例</text>
                                        <text v-else>{{ selectedExampleTitle }}</text>
                                    </view>
                                </picker>
                                <!-- #endif -->
                            </u-form-item>
                        </u-col>
                    </u-row>
                </view>
                <view
                    class="p-[20rpx] border border-solid border-[#DCDFE6] rounded"
                >
                    <textarea
                        class="w-full"
                        placeholder="请输入文档内容，你可以理解为提问的问题（必填）"
                        v-model="formData.question"
                    />
                </view>
                <view
                    class="p-[20rpx] border border-solid border-[#DCDFE6] rounded mt-[20rpx]"
                >
                    <textarea
                        class="w-full"
                        placeholder="请输入补充内容，你可以理解为问题的答案"
                        v-model="formData.answer"
                    />
                </view>
                <!-- 隐藏图片上传功能 -->
                <view class="mt-4" v-if="false">
                    <app-upload
                        :maxCount="9"
                        v-model="formData.images"
                        returnType="object-array"
                    ></app-upload>
                    <span class="text-info">最多支持上传9张图</span>
                </view>
                <!-- 隐藏视频上传功能 -->
                <view class="mt-4" v-if="false">
                    <app-upload
                        :maxCount="1"
                        v-model="formData.video"
                        returnType="object-array"
                        accept="video"
                    ></app-upload>
                    <span class="text-info">格式为MP4，大小不能超过20M </span>
                </view>
                <!-- 隐藏附件上传功能 -->
                <view class="mt-4" v-if="false">
                    <app-upload-file v-model="formData.files">
                        <view>
                            <u-button>上传附件</u-button>
                        </view>
                    </app-upload-file>
                    <span class="text-info">
                        支持上传PDF、docx、excle等文件格式
                    </span>
                </view>
            </scroll-view>
            <view class="mt-2">
                <button @click="submit" class="bg-primary text-white">
                    保存
                </button>
            </view>
        </view>
    </u-popup>
</template>

<script lang="ts" setup>
import { computed, ref, nextTick, toRaw, unref } from 'vue'
import { dataImport, dataUpdate, itemDataDetail, getAllExamples } from '@/api/kb'

const emit = defineEmits(['submit', 'close'])

const show = ref(false)

const formData = ref({
    kb_id: '',
    fd_id: '',
    question: '',
    answer: '',
    files: [],
    images: [],
    video: [],
    uuid: ''
})

// 示例库相关数据
const selectedCategoryId = ref('')
const selectedCategoryName = ref('')
const selectedExampleId = ref('')
const selectedExampleTitle = ref('')
const exampleCategories = ref<any[]>([])
const allExamples = ref<any[]>([])

// 创建纯净的普通数组，完全移除响应式代理
const createPlainArray = (data: any[]) => {
    const plainArray: any[] = []
    for (let i = 0; i < data.length; i++) {
        const item = data[i]
        plainArray.push({
            id: item.id,
            name: item.name
        })
    }
    return plainArray
}

const currentExamples = computed(() => {
    if (!selectedCategoryId.value) return []
    const category = allExamples.value.find((c: any) => c.id === selectedCategoryId.value)
    return category ? category.examples : []
})

// 为picker组件提供原生数组
const pickerCategories = computed(() => {
    const raw = toRaw(exampleCategories.value)
    return raw
})

// 获取示例库数据
const fetchExamples = async () => {
    try {
        const res = await getAllExamples()
        allExamples.value = res
        
        // 使用toRaw彻底移除响应式代理，然后创建普通数组
        const rawData = toRaw(res)
        const plainCategories = createPlainArray(rawData)
        
        // 强制清空现有数据
        exampleCategories.value = []
        await nextTick()
        
        // 重新赋值
        exampleCategories.value = plainCategories
        
        // 强制更新一次界面
        await nextTick()
        
    } catch (error) {
        console.error('获取示例库数据失败:', error)
    }
}

// 处理类别选择变化
const handleCategoryChange = (e: any) => {
    if (!e.detail || typeof e.detail.value === 'undefined') {
        return
    }
    
    const index = parseInt(e.detail.value)
    if (isNaN(index) || index < 0 || index >= pickerCategories.value.length) {
        return
    }
    
    const category = pickerCategories.value[index]
    
    if (category && category.id) {
        selectedCategoryId.value = category.id
        selectedCategoryName.value = category.name
        // 重置示例选择
        selectedExampleId.value = ''
        selectedExampleTitle.value = ''
    }
}

// 处理示例选择变化
const handleExampleChange = (e: any) => {
    const index = e.detail.value
    const examples = currentExamples.value
    if (examples && examples[index]) {
        const example = examples[index]
        selectedExampleId.value = example.id
        selectedExampleTitle.value = example.title
        // 填充问题和答案
        formData.value.question = example.question
        formData.value.answer = example.answer
    }
}

const submit = async () => {
    if (formData.value.uuid) {
        await dataUpdate({ ...formData.value })
    } else {
        await dataImport({ ...formData.value })
    }
    emit('submit')
}

//获取数据
const getData = async () => {
    const res = await itemDataDetail({ uuid: formData.value.uuid })
    Object.keys(formData.value).map((item) => {
        //@ts-ignore
        formData.value[item] = res[item]
    })
}

const open = (kb_id: any, fd_id: any, uuid: any) => {
    show.value = true
    
    // 重置选择状态
    selectedCategoryId.value = ''
    selectedCategoryName.value = ''
    selectedExampleId.value = ''
    selectedExampleTitle.value = ''
    
    formData.value.kb_id = kb_id
    formData.value.fd_id = fd_id
    formData.value.uuid = uuid
    if (uuid != '') {
        getData()
    }
    
    // 获取示例库数据
    fetchExamples()
}

// H5端专用的类别选择处理函数
const handleCategoryChangeH5 = (e: any) => {
    const index = parseInt(e.target.value)
    if (isNaN(index) || index < 0 || index >= pickerCategories.value.length) {
        // 如果选择了空值或无效索引，重置选择
        selectedCategoryId.value = ''
        selectedCategoryName.value = ''
        selectedExampleId.value = ''
        selectedExampleTitle.value = ''
        return
    }
    
    const category = pickerCategories.value[index]
    
    if (category && category.id) {
        selectedCategoryId.value = category.id
        selectedCategoryName.value = category.name
        // 重置示例选择
        selectedExampleId.value = ''
        selectedExampleTitle.value = ''
    }
}

// H5端专用的示例选择处理函数
const handleExampleChangeH5 = (e: any) => {
    const index = parseInt(e.target.value)
    if (isNaN(index) || index < 0 || index >= currentExamples.value.length) {
        // 如果选择了空值或无效索引，重置选择
        selectedExampleId.value = ''
        selectedExampleTitle.value = ''
        return
    }
    
    const example = currentExamples.value[index]
    
    if (example && example.id) {
        selectedExampleId.value = example.id
        selectedExampleTitle.value = example.title
        // 填充问题和答案
        formData.value.question = example.question
        formData.value.answer = example.answer
    }
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.important-notice {
    background: linear-gradient(135deg, #fff8e6 0%, #fff3d6 100%);
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
    
    .notice-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .notice-icon {
            font-size: 18px;
            margin-right: 8px;
        }
        
        .notice-title {
            font-weight: 600;
            color: #e6a23c;
            font-size: 16px;
        }
    }
    
    .notice-content {
        color: #856404;
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
        padding-left: 26px;
    }
}

.example-tip {
    background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%);
    border: 1px solid #409EFF;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
    
    .tip-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .tip-icon {
            font-size: 18px;
            margin-right: 8px;
        }
        
        .tip-title {
            font-weight: 600;
            color: #409EFF;
            font-size: 16px;
        }
    }
    
    .tip-content {
        color: #1f2937;
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
        padding-left: 26px;
    }
}

/* H5端select元素样式优化 */
select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23999' viewBox='0 0 20 20'%3E%3Cpath d='M7 7l3-3 3 3m0 6l-3 3-3-3'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 40px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
}

select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f5f5f5;
}

select option {
    padding: 8px;
    font-size: 14px;
}
</style>
