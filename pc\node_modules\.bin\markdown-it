#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/markdown-it@13.0.1/node_modules/markdown-it/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/markdown-it@13.0.1/node_modules/markdown-it/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/markdown-it@13.0.1/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/markdown-it@13.0.1/node_modules/markdown-it/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/markdown-it@13.0.1/node_modules/markdown-it/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/markdown-it@13.0.1/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../markdown-it/bin/markdown-it.js" "$@"
else
  exec node  "$basedir/../markdown-it/bin/markdown-it.js" "$@"
fi
