# 智能体角色示例库功能安装指南

## 功能说明
智能体角色示例库功能为智能体管理后台提供角色设定内容示例，帮助用户快速创建和配置智能体角色。

## 安装步骤

### 1. 数据库安装

#### 方法一：使用数据库管理工具（推荐）
1. 打开你的数据库管理工具（phpMyAdmin、Navicat、DBeaver等）
2. 连接到项目数据库
3. 选择正确的数据库（通常是项目配置中的数据库名）
4. 执行 `role_example_database.sql` 文件中的所有SQL语句

#### 方法二：使用命令行
```bash
# 进入项目根目录
cd /path/to/your/project

# 执行SQL文件（请根据实际情况修改数据库连接信息）
mysql -u root -p your_database_name < role_example_database.sql
```

### 2. 验证安装

#### 检查数据表
确认以下数据表已创建：
- `cm_role_example` - 角色示例表

#### 检查菜单权限
1. 登录后台管理系统
2. 进入"智能体管理"菜单
3. 确认可以看到"角色示例"子菜单
4. 点击"角色示例"菜单，确认页面正常加载

### 3. 功能测试

#### 基本功能测试
1. **列表查看**：确认可以看到角色示例列表
2. **添加示例**：尝试添加新的角色示例
3. **编辑示例**：编辑现有角色示例
4. **删除示例**：删除测试角色示例
5. **状态切换**：切换角色示例的启用/禁用状态

#### 搜索和筛选测试
1. **标题搜索**：输入关键词搜索角色示例
2. **类别筛选**：按示例类别筛选
3. **状态筛选**：按启用/禁用状态筛选

## 故障排除

### 常见问题

#### 1. 菜单不显示
**问题**：智能体管理下没有"角色示例"菜单
**解决方案**：
- 检查数据库中`cm_system_menu`表是否有ID为60030的记录
- 确认当前用户有相应的菜单权限
- 清除浏览器缓存并重新登录

#### 2. 页面显示"找不到组件"
**问题**：点击菜单后显示组件路径错误
**解决方案**：
- 确认前端组件文件存在：`admin/src/views/ai_role/role_example/index.vue`
- 检查菜单配置中的`component`字段是否为`ai_role/role_example/index`
- 重新构建前端项目

#### 3. API接口404错误
**问题**：前端调用后端接口返回404
**解决方案**：
- 确认后端控制器文件存在：`server/app/adminapi/controller/kb/RoleExampleController.php`
- 检查权限配置是否正确：`kb.robot/roleExample*`
- 清除后端缓存

#### 4. 数据库连接错误
**问题**：执行SQL时提示数据库连接失败
**解决方案**：
- 检查数据库连接信息是否正确
- 确认数据库服务正在运行
- 检查用户权限是否足够

### 日志查看
如果遇到问题，可以查看以下日志：
- 后端日志：`server/runtime/log/`
- 前端控制台：浏览器开发者工具Console
- 数据库日志：MySQL错误日志

## 技术支持

### 文件结构
```
智能体角色示例库相关文件：
├── role_example_database.sql                                    # 数据库安装文件
├── server/app/common/model/robot/RoleExample.php               # 数据模型
├── server/app/adminapi/logic/kb/RoleExampleLogic.php          # 业务逻辑
├── server/app/adminapi/controller/kb/RoleExampleController.php # 后台控制器
├── server/app/adminapi/validate/kb/RoleExampleValidate.php     # 数据验证
├── admin/src/api/ai_role/role_example.ts                      # 前端API接口
└── admin/src/views/ai_role/role_example/index.vue             # 后台管理页面
```

### 权限配置
- 主菜单权限：`kb.robot/roleExampleLists`
- 添加权限：`kb.robot/roleExampleAdd`
- 编辑权限：`kb.robot/roleExampleEdit`
- 删除权限：`kb.robot/roleExampleDel`
- 状态权限：`kb.robot/roleExampleStatus`

### API接口
- 列表接口：`GET /kb.robot/roleExampleLists`
- 详情接口：`GET /kb.robot/roleExampleDetail`
- 添加接口：`POST /kb.robot/roleExampleAdd`
- 编辑接口：`POST /kb.robot/roleExampleEdit`
- 删除接口：`POST /kb.robot/roleExampleDel`
- 状态接口：`POST /kb.robot/roleExampleStatus`

---

*安装指南版本：1.0*  
*最后更新：2025-05-24* 