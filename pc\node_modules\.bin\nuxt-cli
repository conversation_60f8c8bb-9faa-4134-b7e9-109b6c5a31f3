#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/nuxi@3.17.2/node_modules/nuxi/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxi@3.17.2/node_modules/nuxi/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxi@3.17.2/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/nuxi@3.17.2/node_modules/nuxi/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxi@3.17.2/node_modules/nuxi/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxi@3.17.2/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nuxi/bin/nuxi.mjs" "$@"
else
  exec node  "$basedir/../nuxi/bin/nuxi.mjs" "$@"
fi
