{"name": "posthtml-render", "version": "1.4.0", "description": "Renders PostHTML Tree to HTML/XML", "license": "MIT", "repository": "posthtml/posthtml-render", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "engines": {"node": ">=10"}, "scripts": {"version": "conventional-changelog -i changelog.md -s -r 0 && git add changelog.md", "test": "c8 mocha", "docs": "jsdoc2md lib/*.js > render.md", "pretest": "clinton"}, "files": ["lib"], "keywords": ["posthtml", "render", "html", "xml"], "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-angular": "^11.0.0", "c8": "^7.3.5", "chai": "^4.2.0", "clinton": "^0.14.0", "conventional-changelog-cli": "^2.1.1", "husky": "^4.3.0", "jsdoc-to-markdown": "^6.0.1", "lint-staged": "^10.5.1", "mocha": "^8.2.1", "xo": "^0.34.2"}, "types": "lib/index.d.ts"}