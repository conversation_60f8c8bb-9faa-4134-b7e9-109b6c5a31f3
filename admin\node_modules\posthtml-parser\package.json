{"name": "posthtml-parser", "version": "0.2.1", "description": "Parse HTML/XML to PostHTMLTree", "keywords": ["html", "xml", "parser", "posthtml", "posthtmltree"], "main": "index.js", "scripts": {"test": "npm run lint && npm run coverage", "lint": "jshint . && jscs -v .", "coverage": "istanbul cover --report text --report html --report lcov node_modules/mocha/bin/_mocha", "preversion": "npm test", "postversion": "git push && git push --tags && rm -rf coverage"}, "repository": "posthtml/posthtml-parser", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/posthtml/posthtml-parser/issues"}, "homepage": "https://github.com/posthtml/posthtml-parser#readme", "dependencies": {"htmlparser2": "^3.8.3", "isobject": "^2.1.0"}, "devDependencies": {"chai": "^3.3.0", "istanbul": "^0.4.0", "jscs": "^2.3.5", "jshint": "^2.8.0", "mocha": "^2.3.3", "rewire": "^2.5.2", "sinon": "^1.17.4", "sinon-chai": "^2.8.0"}}