@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=F:\erkai999\pc\node_modules\.pnpm\openapi-typescript@7.4.4_typescript@4.9.3\node_modules\openapi-typescript\bin\node_modules;F:\erkai999\pc\node_modules\.pnpm\openapi-typescript@7.4.4_typescript@4.9.3\node_modules\openapi-typescript\node_modules;F:\erkai999\pc\node_modules\.pnpm\openapi-typescript@7.4.4_typescript@4.9.3\node_modules;F:\erkai999\pc\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=F:\erkai999\pc\node_modules\.pnpm\openapi-typescript@7.4.4_typescript@4.9.3\node_modules\openapi-typescript\bin\node_modules;F:\erkai999\pc\node_modules\.pnpm\openapi-typescript@7.4.4_typescript@4.9.3\node_modules\openapi-typescript\node_modules;F:\erkai999\pc\node_modules\.pnpm\openapi-typescript@7.4.4_typescript@4.9.3\node_modules;F:\erkai999\pc\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\openapi-typescript\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\openapi-typescript\bin\cli.js" %*
)
