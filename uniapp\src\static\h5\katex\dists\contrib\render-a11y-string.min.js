!function(e,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r(require("katex"));else if("function"==typeof define&&define.amd)define(["katex"],r);else{var t="object"==typeof exports?r(require("katex")):r(e.katex);for(var a in t)("object"==typeof exports?exports:e)[a]=t[a]}}("undefined"!=typeof self?self:this,function(e){return function(e){var r={};function t(a){if(r[a])return r[a].exports;var o=r[a]={i:a,l:!1,exports:{}};return e[a].call(o.exports,o,o.exports,t),o.l=!0,o.exports}return t.m=e,t.c=r,t.d=function(e,r,a){t.o(e,r)||Object.defineProperty(e,r,{enumerable:!0,get:a})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,r){if(1&r&&(e=t(e)),8&r)return e;if(4&r&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(t.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&r&&"string"!=typeof e)for(var o in e)t.d(a,o,function(r){return e[r]}.bind(null,o));return a},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},t.p="",t(t.s=1)}([function(r,t){r.exports=e},function(e,r,t){"use strict";t.r(r);var a=t(0),o=t.n(a),n={"(":"left parenthesis",")":"right parenthesis","[":"open bracket","]":"close bracket","\\{":"left brace","\\}":"right brace","\\lvert":"open vertical bar","\\rvert":"close vertical bar","|":"vertical bar","\\uparrow":"up arrow","\\Uparrow":"up arrow","\\downarrow":"down arrow","\\Downarrow":"down arrow","\\updownarrow":"up down arrow","\\leftarrow":"left arrow","\\Leftarrow":"left arrow","\\rightarrow":"right arrow","\\Rightarrow":"right arrow","\\langle":"open angle","\\rangle":"close angle","\\lfloor":"open floor","\\rfloor":"close floor","\\int":"integral","\\intop":"integral","\\lim":"limit","\\ln":"natural log","\\log":"log","\\sin":"sine","\\cos":"cosine","\\tan":"tangent","\\cot":"cotangent","\\sum":"sum","/":"slash",",":"comma",".":"point","-":"negative","+":"plus","~":"tilde",":":"colon","?":"question mark","'":"apostrophe","\\%":"percent"," ":"space","\\ ":"space","\\$":"dollar sign","\\angle":"angle","\\degree":"degree","\\circ":"circle","\\vec":"vector","\\triangle":"triangle","\\pi":"pi","\\prime":"prime","\\infty":"infinity","\\alpha":"alpha","\\beta":"beta","\\gamma":"gamma","\\omega":"omega","\\theta":"theta","\\sigma":"sigma","\\lambda":"lambda","\\tau":"tau","\\Delta":"delta","\\delta":"delta","\\mu":"mu","\\rho":"rho","\\nabla":"del","\\ell":"ell","\\ldots":"dots","\\hat":"hat","\\acute":"acute"},s={prime:"prime",degree:"degrees",circle:"degrees",2:"squared",3:"cubed"},i={"|":"open vertical bar",".":""},l={"|":"close vertical bar",".":""},c={"+":"plus","-":"minus","\\pm":"plus minus","\\cdot":"dot","*":"times","/":"divided by","\\times":"times","\\div":"divided by","\\circ":"circle","\\bullet":"bullet"},u={"=":"equals","\\approx":"approximately equals","\u2260":"does not equal","\\geq":"is greater than or equal to","\\ge":"is greater than or equal to","\\leq":"is less than or equal to","\\le":"is less than or equal to",">":"is greater than","<":"is less than","\\leftarrow":"left arrow","\\Leftarrow":"left arrow","\\rightarrow":"right arrow","\\Rightarrow":"right arrow",":":"colon"},p={"\\underleftarrow":"left arrow","\\underrightarrow":"right arrow","\\underleftrightarrow":"left-right arrow","\\undergroup":"group","\\underlinesegment":"line segment","\\utilde":"tilde"},d=function(e,r,t){var a;e&&(/^\d+$/.test(a="open"===r?e in i?i[e]:n[e]||e:"close"===r?e in l?l[e]:n[e]||e:"bin"===r?c[e]||e:"rel"===r?u[e]||e:n[e]||e)&&t.length>0&&/^\d+$/.test(t[t.length-1])?t[t.length-1]+=a:a&&t.push(a))},b=function(e,r){var t=[];e.push(t),r(t)},h=function(e,r,t){switch(e.type){case"accent":b(r,function(r){f(e.base,r,t),r.push("with"),d(e.label,"normal",r),r.push("on top")});break;case"accentUnder":b(r,function(r){f(e.base,r,t),r.push("with"),d(p[e.label],"normal",r),r.push("underneath")});break;case"accent-token":break;case"atom":var a=e.text;switch(e.family){case"bin":d(a,"bin",r);break;case"close":d(a,"close",r);break;case"inner":d(e.text,"inner",r);break;case"open":d(a,"open",r);break;case"punct":d(a,"punct",r);break;case"rel":d(a,"rel",r);break;default:throw e.family,new Error('"'+e.family+'" is not a valid atom type')}break;case"color":var o=e.color.replace(/katex-/,"");b(r,function(r){r.push("start color "+o),f(e.body,r,t),r.push("end color "+o)});break;case"color-token":break;case"delimsizing":e.delim&&"."!==e.delim&&d(e.delim,"normal",r);break;case"genfrac":b(r,function(r){var a=e.leftDelim,o=e.rightDelim;e.hasBarLine?(r.push("start fraction"),a&&d(a,"open",r),f(e.numer,r,t),r.push("divided by"),f(e.denom,r,t),o&&d(o,"close",r),r.push("end fraction")):(r.push("start binomial"),a&&d(a,"open",r),f(e.numer,r,t),r.push("over"),f(e.denom,r,t),o&&d(o,"close",r),r.push("end binomial"))});break;case"kern":break;case"leftright":b(r,function(r){d(e.left,"open",r),f(e.body,r,t),d(e.right,"close",r)});break;case"leftright-right":break;case"lap":f(e.body,r,t);break;case"mathord":d(e.text,"normal",r);break;case"op":var n=e.body,i=e.name;n?f(n,r,t):i&&d(i,"normal",r);break;case"op-token":d(e.text,t,r);break;case"ordgroup":f(e.body,r,t);break;case"overline":b(r,function(r){r.push("start overline"),f(e.body,r,t),r.push("end overline")});break;case"phantom":r.push("empty space");break;case"raisebox":f(e.body,r,t);break;case"rule":r.push("rectangle");break;case"sizing":f(e.body,r,t);break;case"spacing":r.push("space");break;case"styling":f(e.body,r,t);break;case"sqrt":b(r,function(r){var a=e.body,o=e.index;if(o)return"3"===m(f(o,[],t)).join(",")?(r.push("cube root of"),f(a,r,t),void r.push("end cube root")):(r.push("root"),r.push("start index"),f(o,r,t),void r.push("end index"));r.push("square root of"),f(a,r,t),r.push("end square root")});break;case"supsub":var l=e.base,c=e.sub,u=e.sup,h=!1;if(l&&(f(l,r,t),h="op"===l.type&&"\\log"===l.name),c){var y=h?"base":"subscript";b(r,function(e){e.push("start "+y),f(c,e,t),e.push("end "+y)})}u&&b(r,function(e){var r=m(f(u,[],t)).join(",");r in s?e.push(s[r]):(e.push("start superscript"),f(u,e,t),e.push("end superscript"))});break;case"text":if("\\textbf"===e.font){b(r,function(r){r.push("start bold text"),f(e.body,r,t),r.push("end bold text")});break}b(r,function(r){r.push("start text"),f(e.body,r,t),r.push("end text")});break;case"textord":d(e.text,t,r);break;case"smash":f(e.body,r,t);break;case"enclose":if(/cancel/.test(e.label)){b(r,function(r){r.push("start cancel"),f(e.body,r,t),r.push("end cancel")});break}if(/box/.test(e.label)){b(r,function(r){r.push("start box"),f(e.body,r,t),r.push("end box")});break}if(/sout/.test(e.label)){b(r,function(r){r.push("start strikeout"),f(e.body,r,t),r.push("end strikeout")});break}throw new Error("KaTeX-a11y: enclose node with "+e.label+" not supported yet");case"vphantom":throw new Error("KaTeX-a11y: vphantom not implemented yet");case"hphantom":throw new Error("KaTeX-a11y: hphantom not implemented yet");case"operatorname":f(e.body,r,t);break;case"array":throw new Error("KaTeX-a11y: array not implemented yet");case"raw":throw new Error("KaTeX-a11y: raw not implemented yet");case"size":break;case"url":throw new Error("KaTeX-a11y: url not implemented yet");case"tag":throw new Error("KaTeX-a11y: tag not implemented yet");case"verb":d("start verbatim","normal",r),d(e.body,"normal",r),d("end verbatim","normal",r);break;case"environment":throw new Error("KaTeX-a11y: environment not implemented yet");case"horizBrace":d("start "+e.label.slice(1),"normal",r),f(e.base,r,t),d("end "+e.label.slice(1),"normal",r);break;case"infix":break;case"includegraphics":throw new Error("KaTeX-a11y: includegraphics not implemented yet");case"font":f(e.body,r,t);break;case"href":throw new Error("KaTeX-a11y: href not implemented yet");case"cr":throw new Error("KaTeX-a11y: cr not implemented yet");case"underline":b(r,function(r){r.push("start underline"),f(e.body,r,t),r.push("end underline")});break;case"xArrow":throw new Error("KaTeX-a11y: xArrow not implemented yet");case"mclass":var g=e.mclass.slice(1);f(e.body,r,g);break;case"mathchoice":f(e.text,r,t);break;case"htmlmathml":f(e.mathml,r,t);break;case"middle":d(e.delim,t,r);break;case"internal":break;case"html":f(e.body,r,t);break;default:throw e.type,new Error("KaTeX a11y un-recognized type: "+e.type)}},f=function e(r,t,a){if(void 0===t&&(t=[]),r instanceof Array)for(var o=0;o<r.length;o++)e(r[o],t,a);else h(r,t,a);return t},m=function e(r){var t=[];return r.forEach(function(r){r instanceof Array?t=t.concat(e(r)):t.push(r)}),t};r.default=function(e,r){var t=o.a.__parse(e,r),a=f(t,[],"normal");return m(a).join(", ")}}]).default});