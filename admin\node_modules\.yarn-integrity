{"systemParams": "win32-x64-127", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@element-plus/icons-vue@^2.0.6", "@highlightjs/vue-plugin@^2.1.0", "@iktakahiro/markdown-it-katex@^4.0.1", "@rushstack/eslint-patch@^1.1.0", "@tailwindcss/line-clamp@^0.4.2", "@types/lodash-es@^4.17.6", "@types/markdown-it@^12.2.3", "@types/node@^16.11.41", "@types/nprogress@^0.2.0", "@types/sortablejs@^1.15.1", "@vitejs/plugin-legacy@^2.3.1", "@vitejs/plugin-vue-jsx@^2.0.0", "@vitejs/plugin-vue@^3.0.0", "@vue/eslint-config-prettier@^7.0.0", "@vue/eslint-config-typescript@^11.0.0", "@vue/tsconfig@^0.1.3", "@wangeditor/editor-for-vue@^5.1.12", "@wangeditor/editor@^5.1.12", "autoprefixer@^10.4.7", "axios@^0.27.2", "consola@^2.15.3", "css-color-function@^1.3.3", "echarts@^5.3.3", "element-plus@2.2.9", "eslint-plugin-vue@^9.0.0", "eslint@^8.5.0", "execa@^6.1.0", "fs-extra@^10.1.0", "github-markdown-css@^5.2.0", "highlight.js@^11.6.0", "mammoth@^1.6.0", "markdown-it@^13.0.1", "nprogress@^0.2.0", "papa<PERSON><PERSON>@^5.4.1", "pdfjs-dist@^2.10.377", "pinia@^2.0.14", "postcss@^8.4.14", "prettier@^2.5.1", "sass@^1.53.0", "tailwindcss@^3.0.24", "terser@^5.15.1", "typescript@~4.7.4", "unplugin-auto-import@^0.9.2", "unplugin-vue-components@^0.19.9", "vite-plugin-style-import@^2.0.0", "vite-plugin-svg-icons@^2.0.1", "vite-plugin-vue-setup-extend@^0.4.0", "vite@^3.0.0", "vue-clipboard3@^2.0.0", "vue-drag-resize@^1.5.4", "vue-echarts@^6.2.3", "vue-router@^4.0.16", "vue-tsc@^0.38.1", "vue3-video-play@^1.3.1-beta.6", "vue@^3.2.37", "vuedraggable@^4.1.0", "xlsx@https://cdn.sheetjs.com/xlsx-0.20.0/xlsx-0.20.0.tgz", "yarn@^1.22.22"], "lockfileEntries": {"@aashutoshrathi/word-wrap@^1.2.3": "https://registry.npmmirror.com/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz", "@alloc/quick-lru@^5.2.0": "https://registry.npmmirror.com/@alloc/quick-lru/-/quick-lru-5.2.0.tgz", "@ampproject/remapping@^2.2.0": "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.3.0.tgz", "@antfu/utils@^0.5.2": "https://registry.npmmirror.com/@antfu/utils/-/utils-0.5.2.tgz", "@babel/code-frame@^7.23.5": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.23.5.tgz", "@babel/compat-data@^7.23.5": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.23.5.tgz", "@babel/core@^7.19.6": "https://registry.npmmirror.com/@babel/core/-/core-7.24.0.tgz", "@babel/generator@^7.23.6": "https://registry.npmmirror.com/@babel/generator/-/generator-7.23.6.tgz", "@babel/helper-annotate-as-pure@^7.22.5": "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.22.5.tgz", "@babel/helper-compilation-targets@^7.23.6": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.23.6.tgz", "@babel/helper-create-class-features-plugin@^7.23.6": "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.0.tgz", "@babel/helper-environment-visitor@^7.22.20": "https://registry.npmmirror.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.20.tgz", "@babel/helper-function-name@^7.23.0": "https://registry.npmmirror.com/@babel/helper-function-name/-/helper-function-name-7.23.0.tgz", "@babel/helper-hoist-variables@^7.22.5": "https://registry.npmmirror.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz", "@babel/helper-member-expression-to-functions@^7.22.15": "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.23.0.tgz", "@babel/helper-member-expression-to-functions@^7.23.0": "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.23.0.tgz", "@babel/helper-module-imports@^7.22.15": "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz", "@babel/helper-module-transforms@^7.23.3": "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.23.3.tgz", "@babel/helper-optimise-call-expression@^7.22.5": "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.22.5.tgz", "@babel/helper-plugin-utils@^7.22.5": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.0.tgz", "@babel/helper-replace-supers@^7.22.20": "https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.22.20.tgz", "@babel/helper-simple-access@^7.22.5": "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz", "@babel/helper-skip-transparent-expression-wrappers@^7.22.5": "https://registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.22.5.tgz", "@babel/helper-split-export-declaration@^7.22.6": "https://registry.npmmirror.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz", "@babel/helper-string-parser@^7.23.4": "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.23.4.tgz", "@babel/helper-validator-identifier@^7.22.20": "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz", "@babel/helper-validator-option@^7.23.5": "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.23.5.tgz", "@babel/helpers@^7.24.0": "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.24.0.tgz", "@babel/highlight@^7.23.4": "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.23.4.tgz", "@babel/parser@^7.23.6": "https://registry.npmmirror.com/@babel/parser/-/parser-7.24.0.tgz", "@babel/parser@^7.23.9": "https://registry.npmmirror.com/@babel/parser/-/parser-7.24.0.tgz", "@babel/parser@^7.24.0": "https://registry.npmmirror.com/@babel/parser/-/parser-7.24.0.tgz", "@babel/plugin-syntax-jsx@^7.23.3": "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.23.3.tgz", "@babel/plugin-syntax-typescript@^7.23.3": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.23.3.tgz", "@babel/plugin-transform-typescript@^7.20.0": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.23.6.tgz", "@babel/runtime@^7.12.0": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.24.0.tgz", "@babel/standalone@^7.20.0": "https://registry.npmmirror.com/@babel/standalone/-/standalone-7.24.0.tgz", "@babel/template@^7.22.15": "https://registry.npmmirror.com/@babel/template/-/template-7.24.0.tgz", "@babel/template@^7.24.0": "https://registry.npmmirror.com/@babel/template/-/template-7.24.0.tgz", "@babel/traverse@^7.23.7": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.24.0.tgz", "@babel/traverse@^7.24.0": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.24.0.tgz", "@babel/types@^7.22.15": "https://registry.npmmirror.com/@babel/types/-/types-7.24.0.tgz", "@babel/types@^7.22.5": "https://registry.npmmirror.com/@babel/types/-/types-7.24.0.tgz", "@babel/types@^7.23.0": "https://registry.npmmirror.com/@babel/types/-/types-7.24.0.tgz", "@babel/types@^7.23.6": "https://registry.npmmirror.com/@babel/types/-/types-7.24.0.tgz", "@babel/types@^7.24.0": "https://registry.npmmirror.com/@babel/types/-/types-7.24.0.tgz", "@ctrl/tinycolor@^3.4.1": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz", "@element-plus/icons-vue@^2.0.6": "https://registry.npmmirror.com/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz", "@esbuild/android-arm@0.15.18": "https://registry.npmmirror.com/@esbuild/android-arm/-/android-arm-0.15.18.tgz", "@esbuild/linux-loong64@0.15.18": "https://registry.npmmirror.com/@esbuild/linux-loong64/-/linux-loong64-0.15.18.tgz", "@eslint-community/eslint-utils@^4.2.0": "https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz", "@eslint-community/eslint-utils@^4.4.0": "https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz", "@eslint-community/regexpp@^4.4.0": "https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.10.0.tgz", "@eslint-community/regexpp@^4.6.1": "https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.10.0.tgz", "@eslint/eslintrc@^2.1.4": "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-2.1.4.tgz", "@eslint/js@8.57.0": "https://registry.npmmirror.com/@eslint/js/-/js-8.57.0.tgz", "@floating-ui/core@^0.7.3": "https://registry.npmjs.org/@floating-ui/core/-/core-0.7.3.tgz", "@floating-ui/dom@^0.5.4": "https://registry.npmjs.org/@floating-ui/dom/-/dom-0.5.4.tgz", "@highlightjs/vue-plugin@^2.1.0": "https://registry.npmmirror.com/@highlightjs/vue-plugin/-/vue-plugin-2.1.0.tgz", "@humanwhocodes/config-array@^0.11.14": "https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.11.14.tgz", "@humanwhocodes/module-importer@^1.0.1": "https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "@humanwhocodes/object-schema@^2.0.2": "https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-2.0.2.tgz", "@iktakahiro/markdown-it-katex@^4.0.1": "https://registry.npmjs.org/@iktakahiro/markdown-it-katex/-/markdown-it-katex-4.0.1.tgz", "@isaacs/cliui@^8.0.2": "https://registry.npmmirror.com/@isaacs/cliui/-/cliui-8.0.2.tgz", "@jridgewell/gen-mapping@^0.3.0": "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz", "@jridgewell/gen-mapping@^0.3.2": "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz", "@jridgewell/gen-mapping@^0.3.5": "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/set-array@^1.2.1": "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.2.1.tgz", "@jridgewell/source-map@^0.3.3": "https://registry.npmmirror.com/@jridgewell/source-map/-/source-map-0.3.5.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "@jridgewell/sourcemap-codec@^1.4.15": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "@jridgewell/trace-mapping@^0.3.17": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@jridgewell/trace-mapping@^0.3.24": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@jridgewell/trace-mapping@^0.3.9": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@nodelib/fs.walk@^1.2.8": "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@pkgjs/parseargs@^0.11.0": "https://registry.npmmirror.com/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7": "https://registry.npmmirror.com/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz", "@rollup/pluginutils@^4.1.2": "https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-4.2.1.tgz", "@rollup/pluginutils@^4.2.1": "https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-4.2.1.tgz", "@rushstack/eslint-patch@^1.1.0": "https://registry.npmmirror.com/@rushstack/eslint-patch/-/eslint-patch-1.7.2.tgz", "@tailwindcss/line-clamp@^0.4.2": "https://registry.npmmirror.com/@tailwindcss/line-clamp/-/line-clamp-0.4.4.tgz", "@transloadit/prettier-bytes@0.0.7": "https://registry.npmmirror.com/@transloadit/prettier-bytes/-/prettier-bytes-0.0.7.tgz", "@trysound/sax@0.2.0": "https://registry.npmmirror.com/@trysound/sax/-/sax-0.2.0.tgz", "@types/event-emitter@^0.3.3": "https://registry.npmmirror.com/@types/event-emitter/-/event-emitter-0.3.5.tgz", "@types/json-schema@^7.0.9": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/linkify-it@*": "https://registry.npmmirror.com/@types/linkify-it/-/linkify-it-3.0.5.tgz", "@types/lodash-es@^4.17.6": "https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.12.tgz", "@types/lodash@*": "https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.202.tgz", "@types/lodash@^4.14.182": "https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.202.tgz", "@types/markdown-it@^12.2.3": "https://registry.npmmirror.com/@types/markdown-it/-/markdown-it-12.2.3.tgz", "@types/mdurl@*": "https://registry.npmmirror.com/@types/mdurl/-/mdurl-1.0.5.tgz", "@types/node@*": "https://registry.npmmirror.com/@types/node/-/node-16.18.87.tgz", "@types/node@^16.11.41": "https://registry.npmmirror.com/@types/node/-/node-16.18.87.tgz", "@types/nprogress@^0.2.0": "https://registry.npmmirror.com/@types/nprogress/-/nprogress-0.2.3.tgz", "@types/semver@^7.3.12": "https://registry.npmmirror.com/@types/semver/-/semver-7.5.8.tgz", "@types/sortablejs@^1.15.1": "https://registry.npmmirror.com/@types/sortablejs/-/sortablejs-1.15.8.tgz", "@types/svgo@^2.6.1": "https://registry.npmmirror.com/@types/svgo/-/svgo-2.6.4.tgz", "@types/web-bluetooth@^0.0.14": "https://registry.npmjs.org/@types/web-bluetooth/-/web-bluetooth-0.0.14.tgz", "@typescript-eslint/eslint-plugin@^5.59.1": "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.62.0.tgz", "@typescript-eslint/parser@^5.59.1": "https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-5.62.0.tgz", "@typescript-eslint/scope-manager@5.62.0": "https://registry.npmmirror.com/@typescript-eslint/scope-manager/-/scope-manager-5.62.0.tgz", "@typescript-eslint/type-utils@5.62.0": "https://registry.npmmirror.com/@typescript-eslint/type-utils/-/type-utils-5.62.0.tgz", "@typescript-eslint/types@5.62.0": "https://registry.npmmirror.com/@typescript-eslint/types/-/types-5.62.0.tgz", "@typescript-eslint/typescript-estree@5.62.0": "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-5.62.0.tgz", "@typescript-eslint/utils@5.62.0": "https://registry.npmmirror.com/@typescript-eslint/utils/-/utils-5.62.0.tgz", "@typescript-eslint/visitor-keys@5.62.0": "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/-/visitor-keys-5.62.0.tgz", "@ungap/structured-clone@^1.2.0": "https://registry.npmmirror.com/@ungap/structured-clone/-/structured-clone-1.2.0.tgz", "@uppy/companion-client@^2.2.2": "https://registry.npmmirror.com/@uppy/companion-client/-/companion-client-2.2.2.tgz", "@uppy/core@^2.1.1": "https://registry.npmmirror.com/@uppy/core/-/core-2.3.4.tgz", "@uppy/store-default@^2.1.1": "https://registry.npmmirror.com/@uppy/store-default/-/store-default-2.1.1.tgz", "@uppy/utils@^4.1.2": "https://registry.npmmirror.com/@uppy/utils/-/utils-4.1.3.tgz", "@uppy/utils@^4.1.3": "https://registry.npmmirror.com/@uppy/utils/-/utils-4.1.3.tgz", "@uppy/xhr-upload@^2.0.3": "https://registry.npmmirror.com/@uppy/xhr-upload/-/xhr-upload-2.1.3.tgz", "@vitejs/plugin-legacy@^2.3.1": "https://registry.npmmirror.com/@vitejs/plugin-legacy/-/plugin-legacy-2.3.1.tgz", "@vitejs/plugin-vue-jsx@^2.0.0": "https://registry.npmmirror.com/@vitejs/plugin-vue-jsx/-/plugin-vue-jsx-2.1.1.tgz", "@vitejs/plugin-vue@^3.0.0": "https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-3.2.0.tgz", "@volar/code-gen@0.38.9": "https://registry.npmmirror.com/@volar/code-gen/-/code-gen-0.38.9.tgz", "@volar/source-map@0.38.9": "https://registry.npmmirror.com/@volar/source-map/-/source-map-0.38.9.tgz", "@volar/vue-code-gen@0.38.9": "https://registry.npmmirror.com/@volar/vue-code-gen/-/vue-code-gen-0.38.9.tgz", "@volar/vue-typescript@0.38.9": "https://registry.npmmirror.com/@volar/vue-typescript/-/vue-typescript-0.38.9.tgz", "@vue/babel-helper-vue-transform-on@1.2.1": "https://registry.npmmirror.com/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.2.1.tgz", "@vue/babel-plugin-jsx@^1.1.1": "https://registry.npmmirror.com/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.2.1.tgz", "@vue/babel-plugin-resolve-type@1.2.1": "https://registry.npmmirror.com/@vue/babel-plugin-resolve-type/-/babel-plugin-resolve-type-1.2.1.tgz", "@vue/compiler-core@3.4.21": "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.4.21.tgz", "@vue/compiler-core@^3.2.37": "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.4.21.tgz", "@vue/compiler-dom@3.4.21": "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.4.21.tgz", "@vue/compiler-dom@^3.2.37": "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.4.21.tgz", "@vue/compiler-sfc@3.4.21": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.4.21.tgz", "@vue/compiler-sfc@^3.2.29": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.4.21.tgz", "@vue/compiler-sfc@^3.2.37": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.4.21.tgz", "@vue/compiler-sfc@^3.4.15": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.4.21.tgz", "@vue/compiler-ssr@3.4.21": "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.4.21.tgz", "@vue/devtools-api@^6.5.0": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.6.1.tgz", "@vue/devtools-api@^6.5.1": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.6.1.tgz", "@vue/eslint-config-prettier@^7.0.0": "https://registry.npmmirror.com/@vue/eslint-config-prettier/-/eslint-config-prettier-7.1.0.tgz", "@vue/eslint-config-typescript@^11.0.0": "https://registry.npmmirror.com/@vue/eslint-config-typescript/-/eslint-config-typescript-11.0.3.tgz", "@vue/reactivity@3.4.21": "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.4.21.tgz", "@vue/reactivity@^3.2.37": "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.4.21.tgz", "@vue/runtime-core@3.4.21": "https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.4.21.tgz", "@vue/runtime-dom@3.4.21": "https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.4.21.tgz", "@vue/server-renderer@3.4.21": "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.4.21.tgz", "@vue/shared@3.4.21": "https://registry.npmmirror.com/@vue/shared/-/shared-3.4.21.tgz", "@vue/shared@^3.2.37": "https://registry.npmmirror.com/@vue/shared/-/shared-3.4.21.tgz", "@vue/tsconfig@^0.1.3": "https://registry.npmmirror.com/@vue/tsconfig/-/tsconfig-0.1.3.tgz", "@vueuse/core@^8.7.5": "https://registry.npmjs.org/@vueuse/core/-/core-8.9.4.tgz", "@vueuse/metadata@8.9.4": "https://registry.npmjs.org/@vueuse/metadata/-/metadata-8.9.4.tgz", "@vueuse/shared@8.9.4": "https://registry.npmjs.org/@vueuse/shared/-/shared-8.9.4.tgz", "@wangeditor/basic-modules@^1.1.7": "https://registry.npmmirror.com/@wangeditor/basic-modules/-/basic-modules-1.1.7.tgz", "@wangeditor/code-highlight@^1.0.3": "https://registry.npmmirror.com/@wangeditor/code-highlight/-/code-highlight-1.0.3.tgz", "@wangeditor/core@^1.1.19": "https://registry.npmmirror.com/@wangeditor/core/-/core-1.1.19.tgz", "@wangeditor/editor-for-vue@^5.1.12": "https://registry.npmmirror.com/@wangeditor/editor-for-vue/-/editor-for-vue-5.1.12.tgz", "@wangeditor/editor@^5.1.12": "https://registry.npmmirror.com/@wangeditor/editor/-/editor-5.1.23.tgz", "@wangeditor/list-module@^1.0.5": "https://registry.npmmirror.com/@wangeditor/list-module/-/list-module-1.0.5.tgz", "@wangeditor/table-module@^1.1.4": "https://registry.npmmirror.com/@wangeditor/table-module/-/table-module-1.1.4.tgz", "@wangeditor/upload-image-module@^1.0.2": "https://registry.npmmirror.com/@wangeditor/upload-image-module/-/upload-image-module-1.0.2.tgz", "@wangeditor/video-module@^1.1.4": "https://registry.npmmirror.com/@wangeditor/video-module/-/video-module-1.1.4.tgz", "@xmldom/xmldom@^0.8.6": "https://registry.npmmirror.com/@xmldom/xmldom/-/xmldom-0.8.10.tgz", "acorn-jsx@^5.3.2": "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn@^8.11.3": "https://registry.npmmirror.com/acorn/-/acorn-8.11.3.tgz", "acorn@^8.7.1": "https://registry.npmmirror.com/acorn/-/acorn-8.11.3.tgz", "acorn@^8.8.0": "https://registry.npmmirror.com/acorn/-/acorn-8.11.3.tgz", "acorn@^8.8.1": "https://registry.npmmirror.com/acorn/-/acorn-8.11.3.tgz", "acorn@^8.8.2": "https://registry.npmmirror.com/acorn/-/acorn-8.11.3.tgz", "acorn@^8.9.0": "https://registry.npmmirror.com/acorn/-/acorn-8.11.3.tgz", "ajv@^6.12.4": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ansi-regex@^2.0.0": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-2.1.1.tgz", "ansi-regex@^5.0.1": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^6.0.1": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.0.1.tgz", "ansi-styles@^2.2.1": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-2.2.1.tgz", "ansi-styles@^3.2.1": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^6.1.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-6.2.1.tgz", "any-promise@^1.0.0": "https://registry.npmmirror.com/any-promise/-/any-promise-1.3.0.tgz", "anymatch@~3.1.2": "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz", "arg@^5.0.2": "https://registry.npmmirror.com/arg/-/arg-5.0.2.tgz", "argparse@^2.0.1": "https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz", "argparse@~1.0.3": "https://registry.npmmirror.com/argparse/-/argparse-1.0.10.tgz", "arr-diff@^4.0.0": "https://registry.npmmirror.com/arr-diff/-/arr-diff-4.0.0.tgz", "arr-flatten@^1.1.0": "https://registry.npmmirror.com/arr-flatten/-/arr-flatten-1.1.0.tgz", "arr-union@^3.1.0": "https://registry.npmmirror.com/arr-union/-/arr-union-3.1.0.tgz", "array-union@^2.1.0": "https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz", "array-unique@^0.3.2": "https://registry.npmmirror.com/array-unique/-/array-unique-0.3.2.tgz", "assign-symbols@^1.0.0": "https://registry.npmmirror.com/assign-symbols/-/assign-symbols-1.0.0.tgz", "async-validator@^4.2.5": "https://registry.npmmirror.com/async-validator/-/async-validator-4.2.5.tgz", "asynckit@^0.4.0": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "atob@^2.1.2": "https://registry.npmmirror.com/atob/-/atob-2.1.2.tgz", "autoprefixer@^10.4.7": "https://registry.npmmirror.com/autoprefixer/-/autoprefixer-10.4.18.tgz", "axios@^0.27.2": "https://registry.npmmirror.com/axios/-/axios-0.27.2.tgz", "balanced-match@0.1.0": "https://registry.npmmirror.com/balanced-match/-/balanced-match-0.1.0.tgz", "balanced-match@^1.0.0": "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz", "base64-js@^1.5.1": "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz", "base@^0.11.1": "https://registry.npmmirror.com/base/-/base-0.11.2.tgz", "big.js@^5.2.2": "https://registry.npmmirror.com/big.js/-/big.js-5.2.2.tgz", "binary-extensions@^2.0.0": "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz", "bluebird@^3.5.0": "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz", "bluebird@~3.4.0": "https://registry.npmmirror.com/bluebird/-/bluebird-3.4.7.tgz", "boolbase@^1.0.0": "https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz", "brace-expansion@^2.0.1": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz", "braces@^2.2.2": "https://registry.npmmirror.com/braces/-/braces-2.3.2.tgz", "braces@^3.0.2": "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz", "braces@~3.0.2": "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz", "browserslist@^4.22.2": "https://registry.npmmirror.com/browserslist/-/browserslist-4.23.0.tgz", "browserslist@^4.23.0": "https://registry.npmmirror.com/browserslist/-/browserslist-4.23.0.tgz", "buffer-from@^1.0.0": "https://registry.npmmirror.com/buffer-from/-/buffer-from-1.1.2.tgz", "cache-base@^1.0.1": "https://registry.npmmirror.com/cache-base/-/cache-base-1.0.1.tgz", "callsites@^3.0.0": "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz", "camel-case@^4.1.2": "https://registry.npmmirror.com/camel-case/-/camel-case-4.1.2.tgz", "camelcase-css@^2.0.1": "https://registry.npmmirror.com/camelcase-css/-/camelcase-css-2.0.1.tgz", "camelcase@^6.3.0": "https://registry.npmmirror.com/camelcase/-/camelcase-6.3.0.tgz", "caniuse-lite@^1.0.30001587": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001596.tgz", "caniuse-lite@^1.0.30001591": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001596.tgz", "capital-case@^1.0.4": "https://registry.npmmirror.com/capital-case/-/capital-case-1.0.4.tgz", "chalk@^1.1.3": "https://registry.npmmirror.com/chalk/-/chalk-1.1.3.tgz", "chalk@^2.4.2": "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz", "chalk@^4.0.0": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "change-case@^4.1.2": "https://registry.npmmirror.com/change-case/-/change-case-4.1.2.tgz", "chokidar@>=3.0.0 <4.0.0": "https://registry.npmmirror.com/chokidar/-/chokidar-3.6.0.tgz", "chokidar@^3.5.3": "https://registry.npmmirror.com/chokidar/-/chokidar-3.6.0.tgz", "class-utils@^0.3.5": "https://registry.npmmirror.com/class-utils/-/class-utils-0.3.6.tgz", "clipboard@^2.0.6": "https://registry.npmmirror.com/clipboard/-/clipboard-2.0.11.tgz", "clone@^1.0.2": "https://registry.npmmirror.com/clone/-/clone-1.0.4.tgz", "clone@^2.1.1": "https://registry.npmmirror.com/clone/-/clone-2.1.2.tgz", "collection-visit@^1.0.0": "https://registry.npmmirror.com/collection-visit/-/collection-visit-1.0.0.tgz", "color-convert@^1.3.0": "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^1.9.0": "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^2.0.1": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "color-name@1.1.3": "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz", "color-name@^1.0.0": "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz", "color-name@~1.1.4": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "color-string@^0.3.0": "https://registry.npmmirror.com/color-string/-/color-string-0.3.0.tgz", "color@^0.11.0": "https://registry.npmmirror.com/color/-/color-0.11.4.tgz", "combined-stream@^1.0.8": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "commander@^2.19.0": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "commander@^2.20.0": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "commander@^4.0.0": "https://registry.npmmirror.com/commander/-/commander-4.1.1.tgz", "commander@^7.2.0": "https://registry.npmmirror.com/commander/-/commander-7.2.0.tgz", "component-emitter@^1.2.1": "https://registry.npmmirror.com/component-emitter/-/component-emitter-1.3.1.tgz", "compute-scroll-into-view@^1.0.20": "https://registry.npmmirror.com/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz", "concat-map@0.0.1": "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz", "consola@^2.15.3": "https://registry.npmmirror.com/consola/-/consola-2.15.3.tgz", "console@^0.7.2": "https://registry.npmmirror.com/console/-/console-0.7.2.tgz", "constant-case@^3.0.4": "https://registry.npmmirror.com/constant-case/-/constant-case-3.0.4.tgz", "convert-source-map@^2.0.0": "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz", "copy-descriptor@^0.1.0": "https://registry.npmmirror.com/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "core-js@^3.26.0": "https://registry.npmmirror.com/core-js/-/core-js-3.36.0.tgz", "core-util-is@~1.0.0": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.3.tgz", "cors@^2.8.5": "https://registry.npmmirror.com/cors/-/cors-2.8.5.tgz", "cross-spawn@^7.0.0": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz", "cross-spawn@^7.0.2": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz", "cross-spawn@^7.0.3": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz", "css-color-function@^1.3.3": "https://registry.npmmirror.com/css-color-function/-/css-color-function-1.3.3.tgz", "css-select@^4.1.3": "https://registry.npmmirror.com/css-select/-/css-select-4.3.0.tgz", "css-tree@^1.1.2": "https://registry.npmmirror.com/css-tree/-/css-tree-1.1.3.tgz", "css-tree@^1.1.3": "https://registry.npmmirror.com/css-tree/-/css-tree-1.1.3.tgz", "css-what@^6.0.1": "https://registry.npmmirror.com/css-what/-/css-what-6.1.0.tgz", "cssesc@^3.0.0": "https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz", "csso@^4.2.0": "https://registry.npmmirror.com/csso/-/csso-4.2.0.tgz", "csstype@^3.1.3": "https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz", "d@1": "https://registry.npmmirror.com/d/-/d-1.0.2.tgz", "d@^1.0.1": "https://registry.npmmirror.com/d/-/d-1.0.2.tgz", "d@^1.0.2": "https://registry.npmmirror.com/d/-/d-1.0.2.tgz", "dayjs@^1.11.3": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.10.tgz", "debug@^2.2.0": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "debug@^2.3.3": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "debug@^3.1.0": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "debug@^4.1.0": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "debug@^4.3.1": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "debug@^4.3.2": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "debug@^4.3.3": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "debug@^4.3.4": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "decode-uri-component@^0.2.0": "https://registry.npmmirror.com/decode-uri-component/-/decode-uri-component-0.2.2.tgz", "deep-is@^0.1.3": "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz", "define-property@^0.2.5": "https://registry.npmmirror.com/define-property/-/define-property-0.2.5.tgz", "define-property@^1.0.0": "https://registry.npmmirror.com/define-property/-/define-property-1.0.0.tgz", "define-property@^2.0.2": "https://registry.npmmirror.com/define-property/-/define-property-2.0.2.tgz", "delayed-stream@~1.0.0": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "delegate@^3.1.2": "https://registry.npmmirror.com/delegate/-/delegate-3.2.0.tgz", "didyoumean@^1.2.2": "https://registry.npmmirror.com/didyoumean/-/didyoumean-1.2.2.tgz", "dingbat-to-unicode@^1.0.1": "https://registry.npmmirror.com/dingbat-to-unicode/-/dingbat-to-unicode-1.0.1.tgz", "dir-glob@^3.0.1": "https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz", "dlv@^1.1.3": "https://registry.npmmirror.com/dlv/-/dlv-1.1.3.tgz", "doctrine@^3.0.0": "https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz", "dom-serializer@0": "https://registry.npmmirror.com/dom-serializer/-/dom-serializer-0.2.2.tgz", "dom-serializer@^1.0.1": "https://registry.npmmirror.com/dom-serializer/-/dom-serializer-1.4.1.tgz", "dom7@^3.0.0": "https://registry.npmmirror.com/dom7/-/dom7-3.0.0.tgz", "domelementtype@1": "https://registry.npmmirror.com/domelementtype/-/domelementtype-1.3.1.tgz", "domelementtype@^1.3.1": "https://registry.npmmirror.com/domelementtype/-/domelementtype-1.3.1.tgz", "domelementtype@^2.0.1": "https://registry.npmmirror.com/domelementtype/-/domelementtype-2.3.0.tgz", "domelementtype@^2.2.0": "https://registry.npmmirror.com/domelementtype/-/domelementtype-2.3.0.tgz", "domhandler@^2.3.0": "https://registry.npmmirror.com/domhandler/-/domhandler-2.4.2.tgz", "domhandler@^4.2.0": "https://registry.npmmirror.com/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^4.3.1": "https://registry.npmmirror.com/domhandler/-/domhandler-4.3.1.tgz", "dommatrix@^1.0.3": "https://registry.npmmirror.com/dommatrix/-/dommatrix-1.0.3.tgz", "domutils@^1.5.1": "https://registry.npmmirror.com/domutils/-/domutils-1.7.0.tgz", "domutils@^2.8.0": "https://registry.npmmirror.com/domutils/-/domutils-2.8.0.tgz", "dot-case@^3.0.4": "https://registry.npmmirror.com/dot-case/-/dot-case-3.0.4.tgz", "duck@^0.1.12": "https://registry.npmmirror.com/duck/-/duck-0.1.12.tgz", "eastasianwidth@^0.2.0": "https://registry.npmmirror.com/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "echarts@^5.3.3": "https://registry.npmmirror.com/echarts/-/echarts-5.5.0.tgz", "electron-to-chromium@^1.4.668": "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.699.tgz", "element-plus@2.2.9": "https://registry.npmjs.org/element-plus/-/element-plus-2.2.9.tgz", "emoji-regex@^8.0.0": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "emoji-regex@^9.2.2": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz", "emojis-list@^3.0.0": "https://registry.npmmirror.com/emojis-list/-/emojis-list-3.0.0.tgz", "entities@^1.1.1": "https://registry.npmmirror.com/entities/-/entities-1.1.2.tgz", "entities@^2.0.0": "https://registry.npmmirror.com/entities/-/entities-2.2.0.tgz", "entities@^4.5.0": "https://registry.npmmirror.com/entities/-/entities-4.5.0.tgz", "entities@~3.0.1": "https://registry.npmmirror.com/entities/-/entities-3.0.1.tgz", "es-module-lexer@^0.9.3": "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-0.9.3.tgz", "es5-ext@^0.10.35": "https://registry.npmmirror.com/es5-ext/-/es5-ext-0.10.64.tgz", "es5-ext@^0.10.62": "https://registry.npmmirror.com/es5-ext/-/es5-ext-0.10.64.tgz", "es5-ext@^0.10.64": "https://registry.npmmirror.com/es5-ext/-/es5-ext-0.10.64.tgz", "es5-ext@~0.10.14": "https://registry.npmmirror.com/es5-ext/-/es5-ext-0.10.64.tgz", "es6-iterator@^2.0.3": "https://registry.npmmirror.com/es6-iterator/-/es6-iterator-2.0.3.tgz", "es6-symbol@^3.1.1": "https://registry.npmmirror.com/es6-symbol/-/es6-symbol-3.1.4.tgz", "es6-symbol@^3.1.3": "https://registry.npmmirror.com/es6-symbol/-/es6-symbol-3.1.4.tgz", "esbuild-android-64@0.15.18": "https://registry.npmmirror.com/esbuild-android-64/-/esbuild-android-64-0.15.18.tgz", "esbuild-android-arm64@0.15.18": "https://registry.npmmirror.com/esbuild-android-arm64/-/esbuild-android-arm64-0.15.18.tgz", "esbuild-darwin-64@0.15.18": "https://registry.npmmirror.com/esbuild-darwin-64/-/esbuild-darwin-64-0.15.18.tgz", "esbuild-darwin-arm64@0.15.18": "https://registry.npmmirror.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.15.18.tgz", "esbuild-freebsd-64@0.15.18": "https://registry.npmmirror.com/esbuild-freebsd-64/-/esbuild-freebsd-64-0.15.18.tgz", "esbuild-freebsd-arm64@0.15.18": "https://registry.npmmirror.com/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.15.18.tgz", "esbuild-linux-32@0.15.18": "https://registry.npmmirror.com/esbuild-linux-32/-/esbuild-linux-32-0.15.18.tgz", "esbuild-linux-64@0.15.18": "https://registry.npmmirror.com/esbuild-linux-64/-/esbuild-linux-64-0.15.18.tgz", "esbuild-linux-arm64@0.15.18": "https://registry.npmmirror.com/esbuild-linux-arm64/-/esbuild-linux-arm64-0.15.18.tgz", "esbuild-linux-arm@0.15.18": "https://registry.npmmirror.com/esbuild-linux-arm/-/esbuild-linux-arm-0.15.18.tgz", "esbuild-linux-mips64le@0.15.18": "https://registry.npmmirror.com/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.15.18.tgz", "esbuild-linux-ppc64le@0.15.18": "https://registry.npmmirror.com/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.15.18.tgz", "esbuild-linux-riscv64@0.15.18": "https://registry.npmmirror.com/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.15.18.tgz", "esbuild-linux-s390x@0.15.18": "https://registry.npmmirror.com/esbuild-linux-s390x/-/esbuild-linux-s390x-0.15.18.tgz", "esbuild-netbsd-64@0.15.18": "https://registry.npmmirror.com/esbuild-netbsd-64/-/esbuild-netbsd-64-0.15.18.tgz", "esbuild-openbsd-64@0.15.18": "https://registry.npmmirror.com/esbuild-openbsd-64/-/esbuild-openbsd-64-0.15.18.tgz", "esbuild-sunos-64@0.15.18": "https://registry.npmmirror.com/esbuild-sunos-64/-/esbuild-sunos-64-0.15.18.tgz", "esbuild-windows-32@0.15.18": "https://registry.npmmirror.com/esbuild-windows-32/-/esbuild-windows-32-0.15.18.tgz", "esbuild-windows-64@0.15.18": "https://registry.npmmirror.com/esbuild-windows-64/-/esbuild-windows-64-0.15.18.tgz", "esbuild-windows-arm64@0.15.18": "https://registry.npmmirror.com/esbuild-windows-arm64/-/esbuild-windows-arm64-0.15.18.tgz", "esbuild@^0.15.9": "https://registry.npmmirror.com/esbuild/-/esbuild-0.15.18.tgz", "escalade@^3.1.1": "https://registry.npmmirror.com/escalade/-/escalade-3.1.2.tgz", "escape-html@^1.0.3": "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz", "escape-string-regexp@1.0.5": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^1.0.2": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "escape-string-regexp@^5.0.0": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz", "eslint-config-prettier@^8.3.0": "https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.10.0.tgz", "eslint-plugin-prettier@^4.0.0": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz", "eslint-plugin-vue@^9.0.0": "https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-9.22.0.tgz", "eslint-scope@^5.1.1": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^7.1.1": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.2.tgz", "eslint-scope@^7.2.2": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.2.tgz", "eslint-visitor-keys@^3.3.0": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-visitor-keys@^3.4.1": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-visitor-keys@^3.4.3": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint@^8.5.0": "https://registry.npmmirror.com/eslint/-/eslint-8.57.0.tgz", "esniff@^2.0.1": "https://registry.npmmirror.com/esniff/-/esniff-2.0.1.tgz", "espree@^9.3.1": "https://registry.npmmirror.com/espree/-/espree-9.6.1.tgz", "espree@^9.6.0": "https://registry.npmmirror.com/espree/-/espree-9.6.1.tgz", "espree@^9.6.1": "https://registry.npmmirror.com/espree/-/espree-9.6.1.tgz", "esquery@^1.4.0": "https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz", "esquery@^1.4.2": "https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz", "esrecurse@^4.3.0": "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz", "estraverse@^4.1.1": "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^5.1.0": "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.2.0": "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz", "estree-walker@^2.0.1": "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz", "estree-walker@^2.0.2": "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz", "esutils@^2.0.2": "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz", "etag@^1.8.1": "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz", "event-emitter@^0.3.5": "https://registry.npmmirror.com/event-emitter/-/event-emitter-0.3.5.tgz", "execa@^6.1.0": "https://registry.npmmirror.com/execa/-/execa-6.1.0.tgz", "expand-brackets@^2.1.4": "https://registry.npmmirror.com/expand-brackets/-/expand-brackets-2.1.4.tgz", "ext@^1.7.0": "https://registry.npmmirror.com/ext/-/ext-1.7.0.tgz", "extend-shallow@^2.0.1": "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-2.0.1.tgz", "extend-shallow@^3.0.0": "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-3.0.2.tgz", "extend-shallow@^3.0.2": "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-3.0.2.tgz", "extglob@^2.0.2": "https://registry.npmmirror.com/extglob/-/extglob-2.0.4.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-diff@^1.1.2": "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz", "fast-glob@^3.2.11": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.2.tgz", "fast-glob@^3.2.9": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.2.tgz", "fast-glob@^3.3.0": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.2.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@^2.0.6": "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fastq@^1.6.0": "https://registry.npmmirror.com/fastq/-/fastq-1.17.1.tgz", "file-entry-cache@^6.0.1": "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "fill-range@^4.0.0": "https://registry.npmmirror.com/fill-range/-/fill-range-4.0.0.tgz", "fill-range@^7.0.1": "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz", "find-up@^5.0.0": "https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz", "flat-cache@^3.0.4": "https://registry.npmmirror.com/flat-cache/-/flat-cache-3.2.0.tgz", "flatted@^3.2.9": "https://registry.npmmirror.com/flatted/-/flatted-3.3.1.tgz", "follow-redirects@^1.14.9": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.5.tgz", "for-in@^1.0.2": "https://registry.npmmirror.com/for-in/-/for-in-1.0.2.tgz", "foreground-child@^3.1.0": "https://registry.npmmirror.com/foreground-child/-/foreground-child-3.1.1.tgz", "form-data@^4.0.0": "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz", "fraction.js@^4.3.7": "https://registry.npmmirror.com/fraction.js/-/fraction.js-4.3.7.tgz", "fragment-cache@^0.2.1": "https://registry.npmmirror.com/fragment-cache/-/fragment-cache-0.2.1.tgz", "fs-extra@^10.0.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz", "fs-extra@^10.1.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz", "fs.realpath@^1.0.0": "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@~2.3.2": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz", "function-bind@^1.1.2": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz", "get-stream@^6.0.1": "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz", "get-value@^2.0.3": "https://registry.npmmirror.com/get-value/-/get-value-2.0.6.tgz", "get-value@^2.0.6": "https://registry.npmmirror.com/get-value/-/get-value-2.0.6.tgz", "github-markdown-css@^5.2.0": "https://registry.npmmirror.com/github-markdown-css/-/github-markdown-css-5.5.1.tgz", "glob-parent@^5.1.2": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@^6.0.2": "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz", "glob-parent@~5.1.2": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "glob@^10.3.10": "https://registry.npmmirror.com/glob/-/glob-10.3.10.tgz", "glob@^7.1.3": "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz", "globals@^11.1.0": "https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz", "globals@^13.19.0": "https://registry.npmmirror.com/globals/-/globals-13.24.0.tgz", "globby@^11.1.0": "https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz", "good-listener@^1.2.2": "https://registry.npmmirror.com/good-listener/-/good-listener-1.2.2.tgz", "graceful-fs@^4.1.6": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.0": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "graphemer@^1.4.0": "https://registry.npmmirror.com/graphemer/-/graphemer-1.4.0.tgz", "has-ansi@^2.0.0": "https://registry.npmmirror.com/has-ansi/-/has-ansi-2.0.0.tgz", "has-flag@^1.0.0": "https://registry.npmmirror.com/has-flag/-/has-flag-1.0.0.tgz", "has-flag@^3.0.0": "https://registry.npmmirror.com/has-flag/-/has-flag-3.0.0.tgz", "has-flag@^4.0.0": "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz", "has-value@^0.3.1": "https://registry.npmmirror.com/has-value/-/has-value-0.3.1.tgz", "has-value@^1.0.0": "https://registry.npmmirror.com/has-value/-/has-value-1.0.0.tgz", "has-values@^0.1.4": "https://registry.npmmirror.com/has-values/-/has-values-0.1.4.tgz", "has-values@^1.0.0": "https://registry.npmmirror.com/has-values/-/has-values-1.0.0.tgz", "hasown@^2.0.0": "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz", "he@^1.1.1": "https://registry.npmmirror.com/he/-/he-1.2.0.tgz", "header-case@^2.0.4": "https://registry.npmmirror.com/header-case/-/header-case-2.0.4.tgz", "highlight.js@^11.6.0": "https://registry.npmmirror.com/highlight.js/-/highlight.js-11.9.0.tgz", "hls.js@^1.0.10": "https://registry.npmmirror.com/hls.js/-/hls.js-1.5.7.tgz", "html-tags@^3.3.1": "https://registry.npmmirror.com/html-tags/-/html-tags-3.3.1.tgz", "html-void-elements@^2.0.0": "https://registry.npmmirror.com/html-void-elements/-/html-void-elements-2.0.1.tgz", "htmlparser2@^3.8.3": "https://registry.npmmirror.com/htmlparser2/-/htmlparser2-3.10.1.tgz", "human-signals@^3.0.1": "https://registry.npmmirror.com/human-signals/-/human-signals-3.0.1.tgz", "i18next@^20.4.0": "https://registry.npmmirror.com/i18next/-/i18next-20.6.1.tgz", "ignore@^5.2.0": "https://registry.npmmirror.com/ignore/-/ignore-5.3.1.tgz", "image-size@^0.5.1": "https://registry.npmmirror.com/image-size/-/image-size-0.5.5.tgz", "immediate@~3.0.5": "https://registry.npmmirror.com/immediate/-/immediate-3.0.6.tgz", "immer@^9.0.6": "https://registry.npmmirror.com/immer/-/immer-9.0.21.tgz", "immutable@^4.0.0": "https://registry.npmmirror.com/immutable/-/immutable-4.3.5.tgz", "import-fresh@^3.2.1": "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz", "imurmurhash@^0.1.4": "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz", "inflight@^1.0.4": "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.1": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.3": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.3": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "is-accessor-descriptor@^1.0.1": "https://registry.npmmirror.com/is-accessor-descriptor/-/is-accessor-descriptor-1.0.1.tgz", "is-binary-path@~2.1.0": "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-buffer@^1.1.5": "https://registry.npmmirror.com/is-buffer/-/is-buffer-1.1.6.tgz", "is-core-module@^2.13.0": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.13.1.tgz", "is-data-descriptor@^1.0.1": "https://registry.npmmirror.com/is-data-descriptor/-/is-data-descriptor-1.0.1.tgz", "is-descriptor@^0.1.0": "https://registry.npmmirror.com/is-descriptor/-/is-descriptor-0.1.7.tgz", "is-descriptor@^1.0.0": "https://registry.npmmirror.com/is-descriptor/-/is-descriptor-1.0.3.tgz", "is-descriptor@^1.0.2": "https://registry.npmmirror.com/is-descriptor/-/is-descriptor-1.0.3.tgz", "is-extendable@^0.1.0": "https://registry.npmmirror.com/is-extendable/-/is-extendable-0.1.1.tgz", "is-extendable@^0.1.1": "https://registry.npmmirror.com/is-extendable/-/is-extendable-0.1.1.tgz", "is-extendable@^1.0.1": "https://registry.npmmirror.com/is-extendable/-/is-extendable-1.0.1.tgz", "is-extglob@^2.1.1": "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-glob@^4.0.0": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.1": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.3": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-hotkey@^0.2.0": "https://registry.npmmirror.com/is-hotkey/-/is-hotkey-0.2.0.tgz", "is-number@^3.0.0": "https://registry.npmmirror.com/is-number/-/is-number-3.0.0.tgz", "is-number@^7.0.0": "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz", "is-path-inside@^3.0.3": "https://registry.npmmirror.com/is-path-inside/-/is-path-inside-3.0.3.tgz", "is-plain-obj@^1.1": "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "is-plain-object@^2.0.3": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.4.tgz", "is-plain-object@^2.0.4": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.4.tgz", "is-plain-object@^5.0.0": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-5.0.0.tgz", "is-stream@^3.0.0": "https://registry.npmmirror.com/is-stream/-/is-stream-3.0.0.tgz", "is-url@^1.2.4": "https://registry.npmmirror.com/is-url/-/is-url-1.2.4.tgz", "is-windows@^1.0.2": "https://registry.npmmirror.com/is-windows/-/is-windows-1.0.2.tgz", "isarray@1.0.0": "https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz", "isarray@~1.0.0": "https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz", "isexe@^2.0.0": "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz", "isobject@^2.0.0": "https://registry.npmmirror.com/isobject/-/isobject-2.1.0.tgz", "isobject@^2.1.0": "https://registry.npmmirror.com/isobject/-/isobject-2.1.0.tgz", "isobject@^3.0.0": "https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz", "isobject@^3.0.1": "https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz", "jackspeak@^2.3.5": "https://registry.npmmirror.com/jackspeak/-/jackspeak-2.3.6.tgz", "jiti@^1.19.1": "https://registry.npmmirror.com/jiti/-/jiti-1.21.0.tgz", "js-base64@^2.1.9": "https://registry.npmmirror.com/js-base64/-/js-base64-2.6.4.tgz", "js-tokens@^4.0.0": "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz", "js-yaml@^4.1.0": "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz", "jsesc@^2.5.1": "https://registry.npmmirror.com/jsesc/-/jsesc-2.5.2.tgz", "json-buffer@3.0.1": "https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.1.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "json-stable-stringify-without-jsonify@^1.0.1": "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "json5@^1.0.1": "https://registry.npmmirror.com/json5/-/json5-1.0.2.tgz", "json5@^2.2.3": "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz", "jsonc-parser@^3.2.0": "https://registry.npmmirror.com/jsonc-parser/-/jsonc-parser-3.2.1.tgz", "jsonfile@^6.0.1": "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz", "jszip@^3.7.1": "https://registry.npmmirror.com/jszip/-/jszip-3.10.1.tgz", "katex@^0.12.0": "https://registry.npmjs.org/katex/-/katex-0.12.0.tgz", "keyv@^4.5.3": "https://registry.npmmirror.com/keyv/-/keyv-4.5.4.tgz", "kind-of@^3.0.2": "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz", "kind-of@^3.0.3": "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz", "kind-of@^3.2.0": "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz", "kind-of@^4.0.0": "https://registry.npmmirror.com/kind-of/-/kind-of-4.0.0.tgz", "kind-of@^5.0.2": "https://registry.npmmirror.com/kind-of/-/kind-of-5.1.0.tgz", "kind-of@^6.0.2": "https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz", "levn@^0.4.1": "https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz", "lie@~3.3.0": "https://registry.npmmirror.com/lie/-/lie-3.3.0.tgz", "lilconfig@^2.1.0": "https://registry.npmmirror.com/lilconfig/-/lilconfig-2.1.0.tgz", "lilconfig@^3.0.0": "https://registry.npmmirror.com/lilconfig/-/lilconfig-3.1.1.tgz", "lines-and-columns@^1.1.6": "https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "linkify-it@^4.0.1": "https://registry.npmmirror.com/linkify-it/-/linkify-it-4.0.1.tgz", "loader-utils@^1.1.0": "https://registry.npmmirror.com/loader-utils/-/loader-utils-1.4.2.tgz", "local-pkg@^0.4.1": "https://registry.npmmirror.com/local-pkg/-/local-pkg-0.4.3.tgz", "local-pkg@^0.4.2": "https://registry.npmmirror.com/local-pkg/-/local-pkg-0.4.3.tgz", "locate-path@^6.0.0": "https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz", "lodash-es@^4.17.21": "https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz", "lodash-unified@^1.0.2": "https://registry.npmmirror.com/lodash-unified/-/lodash-unified-1.0.3.tgz", "lodash.camelcase@^4.3.0": "https://registry.npmmirror.com/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "lodash.clonedeep@^4.5.0": "https://registry.npmmirror.com/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz", "lodash.debounce@^4.0.8": "https://registry.npmmirror.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "lodash.foreach@^4.5.0": "https://registry.npmmirror.com/lodash.foreach/-/lodash.foreach-4.5.0.tgz", "lodash.isequal@^4.5.0": "https://registry.npmmirror.com/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "lodash.merge@^4.6.2": "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz", "lodash.throttle@^4.1.1": "https://registry.npmmirror.com/lodash.throttle/-/lodash.throttle-4.1.1.tgz", "lodash.toarray@^4.4.0": "https://registry.npmmirror.com/lodash.toarray/-/lodash.toarray-4.4.0.tgz", "lodash@^4.17.21": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lop@^0.4.1": "https://registry.npmmirror.com/lop/-/lop-0.4.1.tgz", "lower-case@^2.0.2": "https://registry.npmmirror.com/lower-case/-/lower-case-2.0.2.tgz", "lru-cache@^5.1.1": "https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz", "lru-cache@^6.0.0": "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz", "lru-cache@^9.1.1 || ^10.0.0": "https://registry.npmmirror.com/lru-cache/-/lru-cache-10.2.0.tgz", "magic-string@^0.25.7": "https://registry.npmmirror.com/magic-string/-/magic-string-0.25.9.tgz", "magic-string@^0.26.2": "https://registry.npmmirror.com/magic-string/-/magic-string-0.26.7.tgz", "magic-string@^0.26.7": "https://registry.npmmirror.com/magic-string/-/magic-string-0.26.7.tgz", "magic-string@^0.30.7": "https://registry.npmmirror.com/magic-string/-/magic-string-0.30.8.tgz", "mammoth@^1.6.0": "https://registry.npmmirror.com/mammoth/-/mammoth-1.7.0.tgz", "map-cache@^0.2.2": "https://registry.npmmirror.com/map-cache/-/map-cache-0.2.2.tgz", "map-visit@^1.0.0": "https://registry.npmmirror.com/map-visit/-/map-visit-1.0.0.tgz", "markdown-it@^13.0.1": "https://registry.npmmirror.com/markdown-it/-/markdown-it-13.0.2.tgz", "mdn-data@2.0.14": "https://registry.npmmirror.com/mdn-data/-/mdn-data-2.0.14.tgz", "mdurl@^1.0.1": "https://registry.npmmirror.com/mdurl/-/mdurl-1.0.1.tgz", "memoize-one@^6.0.0": "https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz", "merge-options@1.0.1": "https://registry.npmmirror.com/merge-options/-/merge-options-1.0.1.tgz", "merge-stream@^2.0.0": "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz", "merge2@^1.3.0": "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz", "merge2@^1.4.1": "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz", "micromatch@3.1.0": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.0.tgz", "micromatch@^4.0.4": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz", "micromatch@^4.0.5": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz", "mime-db@1.52.0": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "mime-match@^1.0.2": "https://registry.npmmirror.com/mime-match/-/mime-match-1.0.2.tgz", "mime-types@^2.1.12": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mimic-fn@^4.0.0": "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-4.0.0.tgz", "minimatch@^3.0.5": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.1": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.2": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^5.1.0": "https://registry.npmmirror.com/minimatch/-/minimatch-5.1.6.tgz", "minimatch@^9.0.1": "https://registry.npmmirror.com/minimatch/-/minimatch-9.0.3.tgz", "minimist@^1.2.0": "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz", "minipass@^5.0.0 || ^6.0.2 || ^7.0.0": "https://registry.npmmirror.com/minipass/-/minipass-7.0.4.tgz", "mixin-deep@^1.2.0": "https://registry.npmmirror.com/mixin-deep/-/mixin-deep-1.3.2.tgz", "mlly@^0.5.5": "https://registry.npmmirror.com/mlly/-/mlly-0.5.17.tgz", "mlly@^1.2.0": "https://registry.npmmirror.com/mlly/-/mlly-1.6.1.tgz", "ms@2.0.0": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "ms@2.1.2": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "ms@^2.1.1": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "mz@^2.7.0": "https://registry.npmmirror.com/mz/-/mz-2.7.0.tgz", "namespace-emitter@^2.0.1": "https://registry.npmmirror.com/namespace-emitter/-/namespace-emitter-2.0.1.tgz", "nanoid@^3.1.25": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.7.tgz", "nanoid@^3.2.0": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.7.tgz", "nanoid@^3.3.7": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.7.tgz", "nanomatch@^1.2.1": "https://registry.npmmirror.com/nanomatch/-/nanomatch-1.2.13.tgz", "natural-compare-lite@^1.4.0": "https://registry.npmmirror.com/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz", "natural-compare@^1.4.0": "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz", "next-tick@^1.1.0": "https://registry.npmmirror.com/next-tick/-/next-tick-1.1.0.tgz", "no-case@^3.0.4": "https://registry.npmmirror.com/no-case/-/no-case-3.0.4.tgz", "node-releases@^2.0.14": "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.14.tgz", "normalize-path@^3.0.0": "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-range@^0.1.2": "https://registry.npmmirror.com/normalize-range/-/normalize-range-0.1.2.tgz", "normalize-wheel-es@^1.1.2": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz", "npm-run-path@^5.1.0": "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-5.3.0.tgz", "nprogress@^0.2.0": "https://registry.npmmirror.com/nprogress/-/nprogress-0.2.0.tgz", "nth-check@^2.0.1": "https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz", "nth-check@^2.1.1": "https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz", "object-assign@^4": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "object-assign@^4.0.1": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "object-assign@^4.1.0": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "object-copy@^0.1.0": "https://registry.npmmirror.com/object-copy/-/object-copy-0.1.0.tgz", "object-hash@^3.0.0": "https://registry.npmmirror.com/object-hash/-/object-hash-3.0.0.tgz", "object-visit@^1.0.0": "https://registry.npmmirror.com/object-visit/-/object-visit-1.0.1.tgz", "object.pick@^1.3.0": "https://registry.npmmirror.com/object.pick/-/object.pick-1.3.0.tgz", "once@^1.3.0": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "onetime@^6.0.0": "https://registry.npmmirror.com/onetime/-/onetime-6.0.0.tgz", "option@~0.2.1": "https://registry.npmmirror.com/option/-/option-0.2.4.tgz", "optionator@^0.9.3": "https://registry.npmmirror.com/optionator/-/optionator-0.9.3.tgz", "p-limit@^3.0.2": "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz", "pako@~1.0.2": "https://registry.npmmirror.com/pako/-/pako-1.0.11.tgz", "papaparse@^5.4.1": "https://registry.npmmirror.com/papaparse/-/papaparse-5.4.1.tgz", "param-case@^3.0.4": "https://registry.npmmirror.com/param-case/-/param-case-3.0.4.tgz", "parent-module@^1.0.0": "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz", "pascal-case@^3.1.2": "https://registry.npmmirror.com/pascal-case/-/pascal-case-3.1.2.tgz", "pascalcase@^0.1.1": "https://registry.npmmirror.com/pascalcase/-/pascalcase-0.1.1.tgz", "path-case@^3.0.4": "https://registry.npmmirror.com/path-case/-/path-case-3.0.4.tgz", "path-exists@^4.0.0": "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-key@^3.1.0": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "path-key@^4.0.0": "https://registry.npmmirror.com/path-key/-/path-key-4.0.0.tgz", "path-parse@^1.0.7": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "path-scurry@^1.10.1": "https://registry.npmmirror.com/path-scurry/-/path-scurry-1.10.1.tgz", "path-type@^4.0.0": "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz", "pathe@^0.2.0": "https://registry.npmmirror.com/pathe/-/pathe-0.2.0.tgz", "pathe@^0.3.2": "https://registry.npmmirror.com/pathe/-/pathe-0.3.9.tgz", "pathe@^1.0.0": "https://registry.npmmirror.com/pathe/-/pathe-1.1.2.tgz", "pathe@^1.1.0": "https://registry.npmmirror.com/pathe/-/pathe-1.1.2.tgz", "pathe@^1.1.2": "https://registry.npmmirror.com/pathe/-/pathe-1.1.2.tgz", "pdfjs-dist@^2.10.377": "https://registry.npmmirror.com/pdfjs-dist/-/pdfjs-dist-2.16.105.tgz", "picocolors@^1.0.0": "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz", "picomatch@^2.0.4": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.1": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.2": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.3.1": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "pify@^2.3.0": "https://registry.npmmirror.com/pify/-/pify-2.3.0.tgz", "pinia@^2.0.14": "https://registry.npmmirror.com/pinia/-/pinia-2.1.7.tgz", "pirates@^4.0.1": "https://registry.npmmirror.com/pirates/-/pirates-4.0.6.tgz", "pkg-types@^1.0.0": "https://registry.npmmirror.com/pkg-types/-/pkg-types-1.0.3.tgz", "pkg-types@^1.0.3": "https://registry.npmmirror.com/pkg-types/-/pkg-types-1.0.3.tgz", "posix-character-classes@^0.1.0": "https://registry.npmmirror.com/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "postcss-import@^15.1.0": "https://registry.npmmirror.com/postcss-import/-/postcss-import-15.1.0.tgz", "postcss-js@^4.0.1": "https://registry.npmmirror.com/postcss-js/-/postcss-js-4.0.1.tgz", "postcss-load-config@^4.0.1": "https://registry.npmmirror.com/postcss-load-config/-/postcss-load-config-4.0.2.tgz", "postcss-nested@^6.0.1": "https://registry.npmmirror.com/postcss-nested/-/postcss-nested-6.0.1.tgz", "postcss-prefix-selector@^1.6.0": "https://registry.npmmirror.com/postcss-prefix-selector/-/postcss-prefix-selector-1.16.0.tgz", "postcss-selector-parser@^6.0.11": "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.15.tgz", "postcss-selector-parser@^6.0.15": "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.15.tgz", "postcss-value-parser@^4.0.0": "https://registry.npmmirror.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-value-parser@^4.2.0": "https://registry.npmmirror.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss@^5.2.17": "https://registry.npmmirror.com/postcss/-/postcss-5.2.18.tgz", "postcss@^8.4.14": "https://registry.npmmirror.com/postcss/-/postcss-8.4.35.tgz", "postcss@^8.4.18": "https://registry.npmmirror.com/postcss/-/postcss-8.4.35.tgz", "postcss@^8.4.23": "https://registry.npmmirror.com/postcss/-/postcss-8.4.35.tgz", "postcss@^8.4.35": "https://registry.npmmirror.com/postcss/-/postcss-8.4.35.tgz", "posthtml-parser@^0.2.0": "https://registry.npmmirror.com/posthtml-parser/-/posthtml-parser-0.2.1.tgz", "posthtml-parser@^0.2.1": "https://registry.npmmirror.com/posthtml-parser/-/posthtml-parser-0.2.1.tgz", "posthtml-rename-id@^1.0": "https://registry.npmmirror.com/posthtml-rename-id/-/posthtml-rename-id-1.0.12.tgz", "posthtml-render@^1.0.5": "https://registry.npmmirror.com/posthtml-render/-/posthtml-render-1.4.0.tgz", "posthtml-render@^1.0.6": "https://registry.npmmirror.com/posthtml-render/-/posthtml-render-1.4.0.tgz", "posthtml-svg-mode@^1.0.3": "https://registry.npmmirror.com/posthtml-svg-mode/-/posthtml-svg-mode-1.0.3.tgz", "posthtml@^0.9.2": "https://registry.npmmirror.com/posthtml/-/posthtml-0.9.2.tgz", "preact@^10.5.13": "https://registry.npmmirror.com/preact/-/preact-10.19.6.tgz", "prelude-ls@^1.2.1": "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz", "prettier-linter-helpers@^1.0.0": "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "prettier@^2.5.1": "https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz", "prismjs@^1.23.0": "https://registry.npmmirror.com/prismjs/-/prismjs-1.29.0.tgz", "process-nextick-args@~2.0.0": "https://registry.npmmirror.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "punycode@^2.1.0": "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz", "query-string@^4.3.2": "https://registry.npmmirror.com/query-string/-/query-string-4.3.4.tgz", "queue-microtask@^1.2.2": "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz", "read-cache@^1.0.0": "https://registry.npmmirror.com/read-cache/-/read-cache-1.0.0.tgz", "readable-stream@^3.1.1": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@~2.3.6": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.8.tgz", "readdirp@~3.6.0": "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz", "regenerator-runtime@^0.13.10": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "regenerator-runtime@^0.14.0": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "regex-not@^1.0.0": "https://registry.npmmirror.com/regex-not/-/regex-not-1.0.2.tgz", "regex-not@^1.0.2": "https://registry.npmmirror.com/regex-not/-/regex-not-1.0.2.tgz", "repeat-element@^1.1.2": "https://registry.npmmirror.com/repeat-element/-/repeat-element-1.1.4.tgz", "repeat-string@^1.6.1": "https://registry.npmmirror.com/repeat-string/-/repeat-string-1.6.1.tgz", "resize-detector@^0.3.0": "https://registry.npmmirror.com/resize-detector/-/resize-detector-0.3.0.tgz", "resolve-from@^4.0.0": "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz", "resolve-url@^0.2.1": "https://registry.npmmirror.com/resolve-url/-/resolve-url-0.2.1.tgz", "resolve@^1.1.7": "https://registry.npmmirror.com/resolve/-/resolve-1.22.8.tgz", "resolve@^1.22.0": "https://registry.npmmirror.com/resolve/-/resolve-1.22.8.tgz", "resolve@^1.22.1": "https://registry.npmmirror.com/resolve/-/resolve-1.22.8.tgz", "resolve@^1.22.2": "https://registry.npmmirror.com/resolve/-/resolve-1.22.8.tgz", "ret@~0.1.10": "https://registry.npmmirror.com/ret/-/ret-0.1.15.tgz", "reusify@^1.0.4": "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz", "rgb@~0.1.0": "https://registry.npmmirror.com/rgb/-/rgb-0.1.0.tgz", "rimraf@^3.0.2": "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz", "rollup@^2.79.1": "https://registry.npmmirror.com/rollup/-/rollup-2.79.1.tgz", "run-parallel@^1.1.9": "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz", "safe-buffer@~5.1.0": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.1.1": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-regex@^1.1.0": "https://registry.npmmirror.com/safe-regex/-/safe-regex-1.1.0.tgz", "sass@^1.53.0": "https://registry.npmmirror.com/sass/-/sass-1.71.1.tgz", "scroll-into-view-if-needed@^2.2.28": "https://registry.npmmirror.com/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.31.tgz", "scule@^0.2.1": "https://registry.npmmirror.com/scule/-/scule-0.2.1.tgz", "select@^1.1.2": "https://registry.npmmirror.com/select/-/select-1.1.2.tgz", "semver@^6.3.1": "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz", "semver@^7.3.6": "https://registry.npmmirror.com/semver/-/semver-7.6.0.tgz", "semver@^7.3.7": "https://registry.npmmirror.com/semver/-/semver-7.6.0.tgz", "semver@^7.6.0": "https://registry.npmmirror.com/semver/-/semver-7.6.0.tgz", "sentence-case@^3.0.4": "https://registry.npmmirror.com/sentence-case/-/sentence-case-3.0.4.tgz", "set-value@^2.0.0": "https://registry.npmmirror.com/set-value/-/set-value-2.0.1.tgz", "set-value@^2.0.1": "https://registry.npmmirror.com/set-value/-/set-value-2.0.1.tgz", "setimmediate@^1.0.5": "https://registry.npmmirror.com/setimmediate/-/setimmediate-1.0.5.tgz", "shebang-command@^2.0.0": "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz", "signal-exit@^3.0.7": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^4.0.1": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.1.0.tgz", "slash@^3.0.0": "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz", "slate-history@^0.66.0": "https://registry.npmmirror.com/slate-history/-/slate-history-0.66.0.tgz", "slate@^0.72.0": "https://registry.npmmirror.com/slate/-/slate-0.72.8.tgz", "snabbdom@^3.1.0": "https://registry.npmmirror.com/snabbdom/-/snabbdom-3.6.2.tgz", "snake-case@^3.0.4": "https://registry.npmmirror.com/snake-case/-/snake-case-3.0.4.tgz", "snapdragon-node@^2.0.1": "https://registry.npmmirror.com/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "snapdragon-util@^3.0.1": "https://registry.npmmirror.com/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "snapdragon@^0.8.1": "https://registry.npmmirror.com/snapdragon/-/snapdragon-0.8.2.tgz", "sortablejs@1.14.0": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.14.0.tgz", "source-map-js@>=0.6.2 <2.0.0": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz", "source-map-js@^1.0.2": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz", "source-map-resolve@^0.5.0": "https://registry.npmmirror.com/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "source-map-support@~0.5.20": "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-url@^0.4.0": "https://registry.npmmirror.com/source-map-url/-/source-map-url-0.4.1.tgz", "source-map@^0.5.6": "https://registry.npmmirror.com/source-map/-/source-map-0.5.7.tgz", "source-map@^0.6.0": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "sourcemap-codec@^1.4.8": "https://registry.npmmirror.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz", "split-string@^3.0.1": "https://registry.npmmirror.com/split-string/-/split-string-3.1.0.tgz", "split-string@^3.0.2": "https://registry.npmmirror.com/split-string/-/split-string-3.1.0.tgz", "sprintf-js@~1.0.2": "https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.0.3.tgz", "ssr-window@^3.0.0-alpha.1": "https://registry.npmmirror.com/ssr-window/-/ssr-window-3.0.0.tgz", "stable@^0.1.8": "https://registry.npmmirror.com/stable/-/stable-0.1.8.tgz", "static-extend@^0.1.1": "https://registry.npmmirror.com/static-extend/-/static-extend-0.1.2.tgz", "strict-uri-encode@^1.0.0": "https://registry.npmmirror.com/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz", "string-width-cjs@npm:string-width@^4.2.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^4.1.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^5.0.1": "https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz", "string-width@^5.1.2": "https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz", "string_decoder@^1.1.1": "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.1.1.tgz", "string_decoder@~1.1.1": "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.1.1.tgz", "strip-ansi-cjs@npm:strip-ansi@^6.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^3.0.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-3.0.1.tgz", "strip-ansi@^6.0.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^7.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-7.1.0.tgz", "strip-final-newline@^3.0.0": "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-3.0.0.tgz", "strip-json-comments@^3.1.1": "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strip-literal@^0.4.0": "https://registry.npmmirror.com/strip-literal/-/strip-literal-0.4.2.tgz", "sucrase@^3.32.0": "https://registry.npmmirror.com/sucrase/-/sucrase-3.35.0.tgz", "supports-color@^2.0.0": "https://registry.npmmirror.com/supports-color/-/supports-color-2.0.0.tgz", "supports-color@^3.2.3": "https://registry.npmmirror.com/supports-color/-/supports-color-3.2.3.tgz", "supports-color@^5.3.0": "https://registry.npmmirror.com/supports-color/-/supports-color-5.5.0.tgz", "supports-color@^7.1.0": "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "svg-baker@1.7.0": "https://registry.npmmirror.com/svg-baker/-/svg-baker-1.7.0.tgz", "svg-tags@^1.0.0": "https://registry.npmmirror.com/svg-tags/-/svg-tags-1.0.0.tgz", "svgo@^2.8.0": "https://registry.npmmirror.com/svgo/-/svgo-2.8.0.tgz", "systemjs@^6.13.0": "https://registry.npmmirror.com/systemjs/-/systemjs-6.14.3.tgz", "tailwindcss@^3.0.24": "https://registry.npmmirror.com/tailwindcss/-/tailwindcss-3.4.1.tgz", "terser@^5.15.1": "https://registry.npmmirror.com/terser/-/terser-5.29.1.tgz", "text-table@^0.2.0": "https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz", "thenify-all@^1.0.0": "https://registry.npmmirror.com/thenify-all/-/thenify-all-1.6.0.tgz", "thenify@>= 3.1.0 < 4": "https://registry.npmmirror.com/thenify/-/thenify-3.3.1.tgz", "throttle-debounce@^3.0.1": "https://registry.npmmirror.com/throttle-debounce/-/throttle-debounce-3.0.1.tgz", "tiny-emitter@^2.0.0": "https://registry.npmmirror.com/tiny-emitter/-/tiny-emitter-2.1.0.tgz", "tiny-warning@^1.0.3": "https://registry.npmmirror.com/tiny-warning/-/tiny-warning-1.0.3.tgz", "to-fast-properties@^2.0.0": "https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "to-object-path@^0.3.0": "https://registry.npmmirror.com/to-object-path/-/to-object-path-0.3.0.tgz", "to-regex-range@^2.1.0": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-2.1.1.tgz", "to-regex-range@^5.0.1": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "to-regex@^3.0.1": "https://registry.npmmirror.com/to-regex/-/to-regex-3.0.2.tgz", "traverse@^0.6.6": "https://registry.npmmirror.com/traverse/-/traverse-0.6.8.tgz", "ts-interface-checker@^0.1.9": "https://registry.npmmirror.com/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz", "tslib@2.3.0": "https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz", "tslib@^1.8.1": "https://registry.npmmirror.com/tslib/-/tslib-1.14.1.tgz", "tslib@^2.0.3": "https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz", "tsutils@^3.21.0": "https://registry.npmmirror.com/tsutils/-/tsutils-3.21.0.tgz", "type-check@^0.4.0": "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz", "type-check@~0.4.0": "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz", "type-fest@^0.20.2": "https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz", "type@^2.7.2": "https://registry.npmmirror.com/type/-/type-2.7.2.tgz", "typescript@~4.7.4": "https://registry.npmmirror.com/typescript/-/typescript-4.7.4.tgz", "uc.micro@^1.0.1": "https://registry.npmmirror.com/uc.micro/-/uc.micro-1.0.6.tgz", "uc.micro@^1.0.5": "https://registry.npmmirror.com/uc.micro/-/uc.micro-1.0.6.tgz", "ufo@^1.0.0": "https://registry.npmmirror.com/ufo/-/ufo-1.4.0.tgz", "ufo@^1.3.2": "https://registry.npmmirror.com/ufo/-/ufo-1.4.0.tgz", "underscore@^1.13.1": "https://registry.npmmirror.com/underscore/-/underscore-1.13.6.tgz", "unimport@^0.4.5": "https://registry.npmmirror.com/unimport/-/unimport-0.4.7.tgz", "union-value@^1.0.0": "https://registry.npmmirror.com/union-value/-/union-value-1.0.1.tgz", "universalify@^2.0.0": "https://registry.npmmirror.com/universalify/-/universalify-2.0.1.tgz", "unplugin-auto-import@^0.9.2": "https://registry.npmmirror.com/unplugin-auto-import/-/unplugin-auto-import-0.9.5.tgz", "unplugin-vue-components@^0.19.9": "https://registry.npmmirror.com/unplugin-vue-components/-/unplugin-vue-components-0.19.9.tgz", "unplugin@^0.7.0": "https://registry.npmmirror.com/unplugin/-/unplugin-0.7.2.tgz", "unplugin@^0.7.2": "https://registry.npmmirror.com/unplugin/-/unplugin-0.7.2.tgz", "unset-value@^1.0.0": "https://registry.npmmirror.com/unset-value/-/unset-value-1.0.0.tgz", "update-browserslist-db@^1.0.13": "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.0.13.tgz", "upper-case-first@^2.0.2": "https://registry.npmmirror.com/upper-case-first/-/upper-case-first-2.0.2.tgz", "upper-case@^2.0.2": "https://registry.npmmirror.com/upper-case/-/upper-case-2.0.2.tgz", "uri-js@^4.2.2": "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz", "urix@^0.1.0": "https://registry.npmmirror.com/urix/-/urix-0.1.0.tgz", "use@^3.1.0": "https://registry.npmmirror.com/use/-/use-3.1.1.tgz", "util-deprecate@^1.0.1": "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@^1.0.2": "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@~1.0.1": "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz", "vary@^1": "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz", "vite-plugin-style-import@^2.0.0": "https://registry.npmmirror.com/vite-plugin-style-import/-/vite-plugin-style-import-2.0.0.tgz", "vite-plugin-svg-icons@^2.0.1": "https://registry.npmmirror.com/vite-plugin-svg-icons/-/vite-plugin-svg-icons-2.0.1.tgz", "vite-plugin-vue-setup-extend@^0.4.0": "https://registry.npmmirror.com/vite-plugin-vue-setup-extend/-/vite-plugin-vue-setup-extend-0.4.0.tgz", "vite@^3.0.0": "https://registry.npmmirror.com/vite/-/vite-3.2.8.tgz", "vue-clipboard3@^2.0.0": "https://registry.npmmirror.com/vue-clipboard3/-/vue-clipboard3-2.0.0.tgz", "vue-demi@*": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.7.tgz", "vue-demi@>=0.14.5": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.7.tgz", "vue-demi@^0.13.11": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.11.tgz", "vue-drag-resize@^1.5.0-rc3": "https://registry.npmjs.org/vue-drag-resize/-/vue-drag-resize-1.5.4.tgz", "vue-drag-resize@^1.5.4": "https://registry.npmjs.org/vue-drag-resize/-/vue-drag-resize-1.5.4.tgz", "vue-echarts@^6.2.3": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.9.tgz", "vue-eslint-parser@^9.1.1": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.4.2.tgz", "vue-eslint-parser@^9.4.2": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.4.2.tgz", "vue-router@^4.0.16": "https://registry.npmmirror.com/vue-router/-/vue-router-4.3.0.tgz", "vue-tsc@^0.38.1": "https://registry.npmmirror.com/vue-tsc/-/vue-tsc-0.38.9.tgz", "vue3-video-play@^1.3.1-beta.6": "https://registry.npmmirror.com/vue3-video-play/-/vue3-video-play-1.3.2.tgz", "vue@^3.2.2": "https://registry.npmmirror.com/vue/-/vue-3.4.21.tgz", "vue@^3.2.37": "https://registry.npmmirror.com/vue/-/vue-3.4.21.tgz", "vuedraggable@^4.1.0": "https://registry.npmmirror.com/vuedraggable/-/vuedraggable-4.1.0.tgz", "web-streams-polyfill@^3.2.1": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "webpack-sources@^3.2.3": "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz", "webpack-virtual-modules@^0.4.4": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.6.tgz", "which@^2.0.1": "https://registry.npmmirror.com/which/-/which-2.0.2.tgz", "wildcard@^1.1.0": "https://registry.npmmirror.com/wildcard/-/wildcard-1.1.2.tgz", "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^8.1.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "wrappy@1": "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz", "xlsx@https://cdn.sheetjs.com/xlsx-0.20.0/xlsx-0.20.0.tgz": "https://cdn.sheetjs.com/xlsx-0.20.0/xlsx-0.20.0.tgz", "xml-name-validator@^4.0.0": "https://registry.npmmirror.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz", "xmlbuilder@^10.0.0": "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-10.1.1.tgz", "yallist@^3.0.2": "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz", "yallist@^4.0.0": "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz", "yaml@^2.3.4": "https://registry.npmmirror.com/yaml/-/yaml-2.4.1.tgz", "yarn@^1.22.22": "https://registry.npmmirror.com/yarn/-/yarn-1.22.22.tgz", "yocto-queue@^0.1.0": "https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz", "zrender@5.5.0": "https://registry.npmmirror.com/zrender/-/zrender-5.5.0.tgz"}, "files": [], "artifacts": {"vue-demi@0.14.7": ["lib", "lib\\index.cjs", "lib\\index.d.ts", "lib\\index.mjs"], "vue-demi@0.13.11": ["lib", "lib\\index.cjs", "lib\\index.d.ts", "lib\\index.mjs"]}}