<template>
    <view class="flex items-center">
        <view
            v-if="chatModel.modelList.length"
            class="flex-1"
            @click="chatModel.show = true"
        >
            <slot :item="currentModel">
                <view
                    class="flex items-center p-[20rpx] text-sm"
                    v-if="currentModel.name"
                >
                    <view
                        class="leading-6 text-[#101010]"
                        :class="{
                            'line-clamp-1 max-w-[300rpx]': hasShowUnit
                        }"
                    >
                        {{ currentModel.alias }}/
                    </view>
                    <view
                        class="text-[#23B571] font-medium"
                        v-if="currentModel.alias && (currentModel.price == '0' || currentModel.is_free)"
                    >
                        (会员免费)
                    </view>
                    <view
                        v-else-if="currentModel.alias"
                        class="text-muted leading-5 font-normal"
                        :class="{
                            'line-clamp-1 w-[90rpx]': hasShowUnit
                        }"
                    >
                        ({{
                            `消耗${currentModel.price}${appStore.getTokenUnit}/1000字符`
                        }})
                    </view>
                    <!--                <u-icon name="arrow-down" size="24rpx"></u-icon>-->
                    <image
                        class="w-[24rpx] h-[24rpx] ml-2"
                        src="@/static/images/icon/icon_toggle.png"
                    ></image>
                </view>
            </slot>
        </view>
        <u-popup
            v-model="chatModel.show"
            mode="bottom"
            :safe-area-inset-bottom="true"
            closeable
            border-radius="16"
        >
            <scroll-view
                scroll-y
                class="model-container h-[80vh] p-[20rpx] pb-[40rpx]"
            >
                <view class="text-lg font-medium mb-[20rpx]">选择模型</view>
                <view>
                    <u-collapse v-model="activeName" @change="collapseChange">
                        <u-collapse-item
                            v-for="(item, fIndex) in chatModel.modelList"
                            :key="item.id"
                            :name="fIndex"
                            class="bg-[#f8f8f8] dark:bg-[#0d0e10] border border-solid border-[transparent]"
                            :class="{
                                'collapse-item--active': isActiveItem(fIndex)
                            }"
                        >
                            <template #title>
                                <div>
                                    <view class="flex items-center py-2">
                                        <image
                                            v-if="item.logo"
                                            :src="item.logo"
                                            class="w-[60rpx] h-[60rpx]"
                                        />
                                        <text
                                            class="mx-2 leading-[24px] font-medium"
                                            >{{ item.name }}</text
                                        >
                                        <text
                                            v-if="item.is_free"
                                            class="bg-[#E3FFF2] text-[#23B571] text-xs px-[10rpx] py-[4rpx] leading-[40rpx] rounded-[6rpx]"
                                        >
                                            会员免费
                                        </text>
                                    </view>
                                    <view
                                        v-if="item.remarks"
                                        class="flex items-center text-xs text-muted font-normal mt-2 leading-[40rpx]"
                                    >
                                        {{ item.remarks }}
                                    </view>
                                </div>
                            </template>
                            <view class="flex-1 h-full">
                                <!-- 对话模型：显示子模型列表 -->
                                <template v-if="type === 'chatModels' && item.models">
                                    <view
                                        class="mb-[20rpx]"
                                        v-for="citem in item.models"
                                        :key="citem.id"
                                    >
                                        <!-- 模型信息行 -->
                                        <view 
                                            class="flex items-center justify-between"
                                            @click="setModelAndClose(item.id, citem.id)"
                                        >
                                        <view
                                            class="flex-1 min-w-0 flex items-center"
                                        >
                                            <view
                                                class="leading-6 text-main mr-2"
                                                >{{ citem.alias }}</view
                                            >
                                            <view
                                                v-if="
                                                    citem.alias &&
                                                    (citem.price == '0' || citem.is_free)
                                                "
                                                class="text-[#23B571] font-medium bg-[#E3FFF2] px-[12rpx] py-[4rpx] rounded-[6rpx] text-[24rpx]"
                                            >
                                                会员免费
                                            </view>
                                            <view
                                                v-else
                                                class="text-tx-secondary text-xs leading-5 font-normal"
                                            >
                                                {{
                                                    `消耗${citem.price}${appStore.getTokenUnit}/1000字符`
                                                }}
                                            </view>
                                        </view>

                                        <view
                                            class="text-muted ml-1 mr-[2px]"
                                            v-if="subModel !== citem.id"
                                        >
                                            <u-image
                                                :src="IconUnSelect"
                                                width="32rpx"
                                                height="32rpx"
                                            ></u-image>
                                        </view>
                                        <view class="text-primary mt-[3px]" v-else>
                                            <u-icon
                                                name="checkmark-circle-fill"
                                                size="40rpx"
                                            ></u-icon>
                                        </view>
                                                                        </view>
                                    
                                    <!-- VIP限制超出提示 - 告知用户会扣费 -->
                                    <view
                                        v-if="citem.vip_limit_info && citem.vip_limit_info.is_exceeded"
                                        class="mt-[16rpx] p-[20rpx] bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-300 rounded-[12rpx] text-blue-700"
                                        style="box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.1);"
                                    >
                                        <view class="font-semibold mb-[12rpx] flex items-center text-[24rpx]">
                                            <text class="text-blue-500 mr-[8rpx] text-[28rpx]">💡</text>
                                            会员免费额度提醒
                                        </view>
                                        <view class="text-[22rpx] leading-[32rpx] text-blue-600">
                                            该模型短时间内使用频次过高，已明显超过正常使用需求，平台将对该模型免费使用进行限制，继续使用将正常扣费，建议您使用其他会员免费模型。此限制将于今日24:00解除，届时您可继续免费使用该模型。
                                        </view>
                                    </view>
 
                                    </view>
                                </template>
                                
                                <!-- 向量模型和重排模型：直接显示主模型 -->
                                <template v-else>
                                    <view 
                                        class="flex items-center justify-between p-[20rpx]"
                                        @click="setModelAndClose(item.id, '')"
                                    >
                                        <view class="flex-1 min-w-0 flex items-center">
                                            <view class="leading-6 text-main mr-2">{{ item.alias }}</view>
                                            <view
                                                v-if="item.price == '0' || item.is_free"
                                                class="text-[#23B571] font-medium bg-[#E3FFF2] px-[12rpx] py-[4rpx] rounded-[6rpx] text-[24rpx]"
                                            >
                                                会员免费
                                            </view>
                                            <view
                                                v-else
                                                class="text-tx-secondary text-xs leading-5 font-normal"
                                            >
                                                {{ `消耗${item.price}${appStore.getTokenUnit}/1000字符` }}
                                            </view>
                                        </view>

                                        <view
                                            class="text-muted ml-1 mr-[2px]"
                                            v-if="model !== item.id"
                                        >
                                            <u-image
                                                :src="IconUnSelect"
                                                width="32rpx"
                                                height="32rpx"
                                            ></u-image>
                                        </view>
                                        <view class="text-primary mt-[3px]" v-else>
                                            <u-icon
                                                name="checkmark-circle-fill"
                                                size="40rpx"
                                            ></u-icon>
                                        </view>
                                    </view>
                                </template>
                            </view>
                        </u-collapse-item>
                    </u-collapse>
                </view>
            </scroll-view>
        </u-popup>
    </view>
</template>
<script lang="ts">
export default {
    options: {
        virtualHost: true,
        styleIsolation: 'shared'
    },
    externalClasses: ['class']
}
</script>
<script setup lang="ts">
import { getAiModel } from '@/api/app'
import { useAppStore } from '@/stores/app'
import IconUnSelect from '@/static/images/icon/icon_unselect.png'
import { computed, reactive, type PropType, ref, watch } from 'vue'

const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    sub_id: {
        type: String,
        default: ''
    },
    setDefault: {
        type: Boolean,
        default: true
    },
    type: {
        type: String as PropType<'chatModels' | 'vectorModels' | 'rankingModels'>,
        default: 'chatModels'
    },
    disabled: {
        type: Boolean,
        default: false
    },
    hasHiddenUnit: {
        type: Boolean,
        default: false
    }
})
const emit = defineEmits(['update:id', 'update:sub_id', 'update:modelConfig'])
const model = computed({
    get() {
        return props.id
    },
    set(value) {
        emit('update:id', value)
    }
})
const subModel = computed({
    get() {
        return props.sub_id
    },
    set(value) {
        emit('update:sub_id', value)
    }
})
const appStore = useAppStore()

const activeName = ref<number | string | number[]>(-1)
// 聊天模型数据
const chatModel = reactive({
    modelList: [] as any[],
    show: false
})

const currentModel = computed(() => {
    // 防止modelList为undefined或空时出错
    if (!chatModel.modelList || !Array.isArray(chatModel.modelList)) {
        return {}
    }

    if (props.type === 'chatModels') {
        // 对话模型有子模型结构
        return (
            chatModel.modelList
                .flatMap((item: any) => item.models || [])
                .find((item: any) => item.id === subModel.value) || {}
        )
    } else {
        // 向量模型和重排模型直接查找主模型
        return (
            chatModel.modelList
                .find((item: any) => item.id === model.value) || {}
        )
    }
})

const hasShowUnit = computed(() => {
    const flag = currentModel.value?.alias?.length > 10
    return props.hasHiddenUnit ? flag : false
})

const collapseChange = (name: number) => {
    activeName.value = name
}

// 获取聊天模型数据
const getChatModelFunc = async () => {
    try {
        const data = await getAiModel()
        // 确保从API返回的数据中正确获取对应类型的模型列表
        chatModel.modelList = data[props.type] || []
        console.log(`H5端获取${props.type}模型数据:`, chatModel.modelList)
        
        // 初始化已保存的值
        initSavedValues()
        
        if (props.setDefault && chatModel.modelList.length > 0) {
            setDefaultModel()
        }
    } catch (error) {
        console.log('获取聊天模型数据错误=>', error)
        chatModel.modelList = []
    }
}

// 初始化已保存的值
const initSavedValues = () => {
    if (!chatModel.modelList || chatModel.modelList.length === 0) {
        return
    }
    
    console.log(`H5端initSavedValues - type: ${props.type}, id: ${props.id}, sub_id: ${props.sub_id}`)
    console.log('modelList:', chatModel.modelList)
    
    if (props.type === 'chatModels') {
        // 对话模型：如果有保存的sub_id，需要找到对应的主模型
        if (props.sub_id) {
            for (const item of chatModel.modelList) {
                if (item.models && item.models.some((subItem: any) => subItem.id === props.sub_id)) {
                    model.value = item.id
                    subModel.value = props.sub_id
                    // 设置对应的activeName用于展开面板
                    const index = chatModel.modelList.findIndex(modelItem => modelItem.id === item.id)
                    if (index !== -1) {
                        activeName.value = index
                    }
                    console.log(`H5端对话模型初始化: model=${item.id}, subModel=${props.sub_id}`)
                    break
                }
            }
        }
    } else {
        // 向量模型和重排模型：直接使用主模型ID
        if (props.id) {
            // 将props.id转换为字符串进行比较
            const propId = String(props.id)
            const savedModel = chatModel.modelList.find((item: any) => String(item.id) === propId)
            if (savedModel) {
                model.value = savedModel.id
                console.log(`H5端${props.type}初始化成功:`, propId, '->', savedModel.alias || savedModel.name)
            } else {
                console.log(`H5端${props.type}初始化失败: 未找到ID为 ${propId} 的模型`)
                console.log('可用模型列表:', chatModel.modelList.map(item => ({ id: item.id, name: item.name, alias: item.alias })))
            }
        } else {
            console.log(`H5端${props.type}初始化跳过: props.id为空`)
        }
    }
}

const setDefaultModel = () => {
    if (!chatModel.modelList || chatModel.modelList.length === 0) {
        return
    }

    const defaultGroupIndex =
        chatModel.modelList.findIndex((item) => item.is_default) || 0
    
    if (props.type === 'chatModels') {
        // 对话模型有子模型结构
        const defaultModel = chatModel.modelList[defaultGroupIndex]?.models?.[0]
        if (defaultModel) {
            model.value = chatModel.modelList[defaultGroupIndex].id
            subModel.value = defaultModel.id
            activeName.value = defaultGroupIndex
        }
    } else {
        // 向量模型和重排模型直接设置主模型
        if (chatModel.modelList[defaultGroupIndex]) {
            model.value = chatModel.modelList[defaultGroupIndex].id
            activeName.value = defaultGroupIndex
        }
    }
}

const setModelAndClose = (id: string, sub_id: string) => {
    model.value = id
    if (props.type === 'chatModels') {
        subModel.value = sub_id
    } else {
        // 向量模型和重排模型不使用subModel
        subModel.value = ''
    }
    chatModel.show = false
}

watch(
    () => currentModel.value,
    (value) => {
        emit('update:modelConfig', value)
    }
)

// 监听props变化，重新初始化值
watch(
    () => [props.id, props.sub_id],
    () => {
        if (chatModel.modelList && chatModel.modelList.length > 0) {
            initSavedValues()
        }
    }
)

const isActiveItem = (index: number) => {
    if (!chatModel.modelList || !chatModel.modelList[index]) {
        return false
    }
    
    const targetModel = chatModel.modelList[index]
    
    if (props.type === 'chatModels') {
        // 对话模型检查子模型
        if (!targetModel.models) return false
        return targetModel.models.some(
            (item: any) => item.id === subModel.value
        )
    } else {
        // 向量模型和重排模型直接检查主模型
        return targetModel.id === model.value
    }
}

// 监听modelList变化，当数据加载完成后初始化已保存的值
watch(
    () => chatModel.modelList,
    (newList) => {
        if (newList && newList.length > 0) {
            console.log('H5端modelList更新，重新初始化已保存值')
            initSavedValues()
        }
    },
    { immediate: true }
)

getChatModelFunc()
</script>

<style lang="scss">
.model-container {
    :deep() {
        .u-collapse-item {
            margin-top: 20rpx;
            padding: 0 20rpx;
            border-radius: 12rpx;
            border-width: 1px;
            border-style: solid;
            border-color: transparent;
            box-shadow: 0 1px 6px 0 rgba(230, 233, 237, 1);
        }
    }
    :deep() {
        .collapse-item--active {
            // #ifdef H5
            border-width: 1px;
            border-style: solid;
            @apply border-primary bg-primary-light-9;
            // #endif
            .u-collapse-item {
                border-width: 1px;
                border-style: solid;
                @apply border-primary bg-primary-light-9;
            }
        }
    }
}
</style>
