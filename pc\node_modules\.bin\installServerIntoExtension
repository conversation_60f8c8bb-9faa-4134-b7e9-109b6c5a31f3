#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/vscode-languageserver@7.0.0/node_modules/vscode-languageserver/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vscode-languageserver@7.0.0/node_modules/vscode-languageserver/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vscode-languageserver@7.0.0/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/vscode-languageserver@7.0.0/node_modules/vscode-languageserver/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vscode-languageserver@7.0.0/node_modules/vscode-languageserver/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vscode-languageserver@7.0.0/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
"$basedir/../vscode-languageserver/bin/installServerIntoExtension"   "$@"
exit $?
