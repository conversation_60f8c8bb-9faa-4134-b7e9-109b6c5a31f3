#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.13_typescript@4.9.3_/node_modules/vue-demi/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.13_typescript@4.9.3_/node_modules/vue-demi/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.13_typescript@4.9.3_/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.13_typescript@4.9.3_/node_modules/vue-demi/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.13_typescript@4.9.3_/node_modules/vue-demi/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.13_typescript@4.9.3_/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../vue-demi/bin/vue-demi-fix.js" "$@"
else
  exec node  "$basedir/../../../vue-demi/bin/vue-demi-fix.js" "$@"
fi
