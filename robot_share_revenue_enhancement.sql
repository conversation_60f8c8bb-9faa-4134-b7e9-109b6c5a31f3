-- 智能体分享分成收益功能数据库变更文件
-- 执行日期: 2024-12-19
-- 功能说明: 为智能体分享添加分成收益机制

-- 1. 添加智能体分成收益配置表
CREATE TABLE `cm_kb_robot_revenue_config` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `is_enable` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否启用分成功能：0-关闭 1-开启',
  `share_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '分享者分成比例(百分比): 0.00-100.00',
  `platform_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT 100.00 COMMENT '平台保留比例(百分比): 0.00-100.00', 
  `min_revenue` decimal(10,2) UNSIGNED NOT NULL DEFAULT 0.01 COMMENT '最小分成金额(电力值)',
  `settle_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '结算方式：1-实时结算 2-每日结算',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能体分成收益配置表';

-- 插入默认配置数据
INSERT INTO `cm_kb_robot_revenue_config` (`id`, `is_enable`, `share_ratio`, `platform_ratio`, `min_revenue`, `settle_type`, `create_time`, `update_time`) VALUES 
(1, 0, 30.00, 70.00, 0.01, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 2. 创建智能体分成收益记录表
CREATE TABLE `cm_kb_robot_revenue_log` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '使用者用户ID',
  `sharer_id` int(10) UNSIGNED NOT NULL COMMENT '分享者用户ID', 
  `robot_id` int(10) UNSIGNED NOT NULL COMMENT '智能体ID',
  `square_id` int(10) UNSIGNED NOT NULL COMMENT '广场记录ID',
  `record_id` int(10) UNSIGNED NOT NULL COMMENT '对话记录ID',
  `total_cost` decimal(15,7) UNSIGNED NOT NULL DEFAULT 0.0000000 COMMENT '总消耗电力值',
  `share_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT 0.0000000 COMMENT '分享者获得电力值',
  `platform_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT 0.0000000 COMMENT '平台保留电力值',
  `share_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '分成比例(百分比)',
  `settle_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '结算状态：0-未结算 1-已结算',
  `settle_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '结算时间',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id` (`user_id`) USING BTREE,
  INDEX `idx_sharer_id` (`sharer_id`) USING BTREE,
  INDEX `idx_robot_id` (`robot_id`) USING BTREE,
  INDEX `idx_square_id` (`square_id`) USING BTREE,
  INDEX `idx_settle_status` (`settle_status`) USING BTREE,
  INDEX `idx_create_time` (`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能体分成收益记录表';

-- 3. 添加用户账户新的变动类型 (在 user_account_log 表中使用)
-- 注意: 这些常量已在代码中定义，无需数据库变更
-- UM_INC_ROBOT_REVENUE = 217  // 智能体分成收益
-- UM_DEC_ROBOT_REVENUE = 113  // 智能体分成扣减(平台保留部分)

-- 4. 在 cm_kb_robot_record 表中添加分成相关字段
-- 4.1 添加分成状态字段
ALTER TABLE `cm_kb_robot_record` 
ADD COLUMN `is_revenue_shared` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已分成：0-未分成 1-已分成' AFTER `tokens`;

-- 4.2 添加分成记录ID字段
ALTER TABLE `cm_kb_robot_record` 
ADD COLUMN `revenue_log_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分成记录ID' AFTER `is_revenue_shared`;

-- 5. 在 cm_kb_robot_square 表中添加分成统计字段
-- 5.1 添加累计分成收益字段
ALTER TABLE `cm_kb_robot_square` 
ADD COLUMN `total_revenue` decimal(15,7) UNSIGNED NOT NULL DEFAULT 0.0000000 COMMENT '累计分成收益' AFTER `verify_result`;

-- 5.2 添加使用次数字段
ALTER TABLE `cm_kb_robot_square` 
ADD COLUMN `use_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用次数' AFTER `total_revenue`;

-- 6. 在系统配置中添加分成功能配置项 (通过配置服务管理)
-- robot_revenue.is_enable = 0            // 是否启用分成功能
-- robot_revenue.share_ratio = 30.00      // 分享者分成比例  
-- robot_revenue.platform_ratio = 70.00   // 平台保留比例
-- robot_revenue.min_revenue = 0.01       // 最小分成金额
-- robot_revenue.settle_type = 1          // 结算方式

-- 变更记录说明:
-- 1. cm_kb_robot_revenue_config: 存储全局分成配置，支持动态调整分成比例
-- 2. cm_kb_robot_revenue_log: 记录每次分成收益的详细信息，便于统计和对账
-- 3. cm_kb_robot_record: 添加分成标记，避免重复分成
-- 4. cm_kb_robot_square: 添加收益统计，便于展示分享者的收益情况
-- 5. 配置项: 通过ConfigService管理，支持后台动态配置 

-- 执行说明:
-- 1. 请按顺序执行以上SQL语句
-- 2. 如果某个字段已存在，可跳过对应的ALTER语句
-- 3. 建议在执行前备份数据库 