#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/atob@2.1.2/node_modules/atob/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/atob@2.1.2/node_modules/atob/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/atob@2.1.2/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/atob@2.1.2/node_modules/atob/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/atob@2.1.2/node_modules/atob/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/atob@2.1.2/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../atob/bin/atob.js" "$@"
else
  exec node  "$basedir/../atob/bin/atob.js" "$@"
fi
