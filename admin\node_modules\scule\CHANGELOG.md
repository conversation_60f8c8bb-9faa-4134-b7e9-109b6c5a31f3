# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.2.1](https://github.com/nuxt-contrib/scule/compare/v0.2.0...v0.2.1) (2021-04-28)

## [0.2.0](https://github.com/nuxt-contrib/scule/compare/v0.1.1...v0.2.0) (2021-04-22)


### ⚠ BREAKING CHANGES

* add `exports` field

### Features

* add `exports` field ([6241d0f](https://github.com/nuxt-contrib/scule/commit/6241d0f2b4892c5edc820fb2271b6666ef564af0)), closes [#2](https://github.com/nuxt-contrib/scule/issues/2)

### [0.1.1](https://github.com/nuxt-contrib/scule/compare/v0.1.0...v0.1.1) (2021-02-16)


### Features

* add dot to default splitters ([db5120f](https://github.com/nuxt-contrib/scule/commit/db5120fddf22850255f7c0d1283aad7d8c53cf5b))

### 0.0.1 (2021-02-16)
