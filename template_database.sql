-- 模板库功能数据库设计
-- 创建时间：2025-05-24
-- 说明：模板库功能，复用示例库类别，新增模板表

-- 1. 创建模板库表
CREATE TABLE `cm_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属类别ID（关联cm_example_category表）',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '模板名称',
  `description` text COMMENT '模板描述',
  `download_url` varchar(500) NOT NULL DEFAULT '' COMMENT '下载链接地址',
  `file_size` varchar(50) DEFAULT '' COMMENT '文件大小',
  `file_type` varchar(50) DEFAULT '' COMMENT '文件类型',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id` (`category_id`) USING BTREE,
  INDEX `idx_status` (`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板库表';

-- 2. 添加模板链接菜单（使用实际的知识库管理菜单ID：50125）
INSERT INTO `cm_system_menu` (`id`, `pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`)
VALUES (60020, 50125, 'C', '模板链接', '', 8, 'kb.template/lists', 'knowledge_base/template', 'knowledge_base/template/index', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 3. 添加模板链接的按钮权限
INSERT INTO `cm_system_menu` (`id`, `pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`)
VALUES 
(60021, 60020, 'A', '添加', '', 0, 'kb.template/add', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60022, 60020, 'A', '编辑', '', 0, 'kb.template/edit', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60023, 60020, 'A', '删除', '', 0, 'kb.template/del', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60024, 60020, 'A', '状态', '', 0, 'kb.template/status', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60025, 60020, 'A', '详情', '', 0, 'kb.template/detail', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60026, 60020, 'A', '获取类别列表', '', 0, 'kb.template/getCategoryList', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60027, 60020, 'A', '根据类别获取模板', '', 0, 'kb.template/getListByCategoryId', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60028, 60020, 'A', '获取所有模板', '', 0, 'kb.template/getAllTemplates', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60029, 60020, 'A', '下载模板', '', 0, 'kb.template/download', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 4. 插入示例数据（可选）
INSERT INTO `cm_template` (`category_id`, `name`, `description`, `download_url`, `file_size`, `file_type`, `sort`, `status`, `create_time`) 
VALUES 
(1, 'Word文档模板', '通用Word文档导入模板，包含标准格式和字段定义', 'https://example.com/templates/word_template.docx', '2.5MB', 'docx', 100, 1, UNIX_TIMESTAMP()),
(1, 'Excel表格模板', '标准Excel数据导入模板，支持批量数据导入', 'https://example.com/templates/excel_template.xlsx', '1.8MB', 'xlsx', 90, 1, UNIX_TIMESTAMP()),
(2, 'PDF文档模板', 'PDF格式文档导入参考模板', 'https://example.com/templates/pdf_template.pdf', '3.2MB', 'pdf', 80, 1, UNIX_TIMESTAMP()); 