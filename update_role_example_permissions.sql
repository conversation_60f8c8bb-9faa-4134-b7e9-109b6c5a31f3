-- 更新角色示例菜单权限配置
-- 创建时间：2025-05-28
-- 说明：将角色示例菜单的权限从kb.robot/roleExampleXXX改为kb.role_example/roleExampleXXX
-- 目的：修复前端API调用404错误，确保前端路径与后端控制器一致

-- 1. 更新主菜单权限 - 角色示例列表
UPDATE `cm_system_menu` 
SET `perms` = 'kb.role_example/roleExampleLists' 
WHERE `id` = 60030;

-- 2. 更新按钮权限 - 添加角色示例
UPDATE `cm_system_menu` 
SET `perms` = 'kb.role_example/roleExampleAdd' 
WHERE `id` = 60031;

-- 3. 更新按钮权限 - 编辑角色示例
UPDATE `cm_system_menu` 
SET `perms` = 'kb.role_example/roleExampleEdit' 
WHERE `id` = 60032;

-- 4. 更新按钮权限 - 删除角色示例
UPDATE `cm_system_menu` 
SET `perms` = 'kb.role_example/roleExampleDel' 
WHERE `id` = 60033;

-- 5. 更新按钮权限 - 修改角色示例状态
UPDATE `cm_system_menu` 
SET `perms` = 'kb.role_example/roleExampleStatus' 
WHERE `id` = 60034;

-- 6. 更新按钮权限 - 获取角色示例详情
UPDATE `cm_system_menu` 
SET `perms` = 'kb.role_example/roleExampleDetail' 
WHERE `id` = 60035;

-- 7. 更新按钮权限 - 获取示例类别列表
UPDATE `cm_system_menu` 
SET `perms` = 'kb.role_example/roleExampleCategoryList' 
WHERE `id` = 60036;

-- 8. 更新按钮权限 - 根据类别获取角色示例列表
UPDATE `cm_system_menu` 
SET `perms` = 'kb.role_example/roleExampleListByCategory' 
WHERE `id` = 60037;

-- 9. 更新按钮权限 - 获取所有角色示例（按类别分组）
UPDATE `cm_system_menu` 
SET `perms` = 'kb.role_example/roleExampleAll' 
WHERE `id` = 60038;

-- 验证更新结果
SELECT id, name, perms 
FROM `cm_system_menu` 
WHERE `id` BETWEEN 60030 AND 60038 
ORDER BY `id`; 