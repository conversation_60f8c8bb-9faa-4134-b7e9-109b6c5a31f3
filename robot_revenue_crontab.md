# 智能体分成收益每日结算定时任务配置

## 配置定时任务

### 1. Linux/Unix 系统配置

#### 使用 crontab 配置
```bash
# 编辑定时任务
crontab -e

# 添加以下配置（每日凌晨2点执行）
0 2 * * * cd /path/to/your/project/server && php think robot:revenue:settle >> /var/log/robot_revenue_settle.log 2>&1

# 或者每日凌晨1点执行
0 1 * * * cd /path/to/your/project/server && php think robot:revenue:settle

# 查看定时任务列表
crontab -l
```

#### 使用系统服务管理
```bash
# 创建服务配置文件
sudo nano /etc/systemd/system/robot-revenue-settle.service

# 配置内容：
[Unit]
Description=Robot Revenue Settlement Service
After=network.target

[Service]
Type=oneshot
User=www-data
WorkingDirectory=/path/to/your/project/server
ExecStart=/usr/bin/php think robot:revenue:settle
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target

# 创建定时器配置
sudo nano /etc/systemd/system/robot-revenue-settle.timer

# 定时器内容：
[Unit]
Description=Robot Revenue Settlement Timer
Requires=robot-revenue-settle.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target

# 启用并启动定时器
sudo systemctl enable robot-revenue-settle.timer
sudo systemctl start robot-revenue-settle.timer

# 查看定时器状态
sudo systemctl status robot-revenue-settle.timer
```

### 2. Windows 系统配置

#### 使用任务计划程序
```batch
# 通过命令行创建任务
schtasks /create /tn "RobotRevenueSettle" /tr "cd /d D:\your\project\server && php think robot:revenue:settle" /sc daily /st 02:00

# 或者通过GUI：
# 1. 打开"任务计划程序"
# 2. 创建基本任务
# 3. 设置名称：智能体分成收益结算
# 4. 触发器：每天
# 5. 时间：凌晨02:00
# 6. 操作：启动程序
# 7. 程序：php
# 8. 参数：think robot:revenue:settle
# 9. 起始位置：D:\your\project\server
```

### 3. Docker 环境配置

#### 在容器中配置 cron
```dockerfile
# 在 Dockerfile 中添加
RUN apt-get update && apt-get install -y cron

# 复制 crontab 文件
COPY crontab /etc/cron.d/robot-revenue-settle
RUN chmod 0644 /etc/cron.d/robot-revenue-settle
RUN crontab /etc/cron.d/robot-revenue-settle

# 启动 cron 服务
CMD ["cron", "-f"]
```

#### crontab 文件内容
```bash
# /etc/cron.d/robot-revenue-settle
0 2 * * * root cd /var/www/html/server && php think robot:revenue:settle >> /var/log/robot_revenue_settle.log 2>&1
```

## 手动执行测试

### 1. 直接执行命令
```bash
# 进入项目目录
cd /path/to/your/project/server

# 执行结算命令
php think robot:revenue:settle

# 或者使用完整路径
/usr/bin/php /path/to/your/project/server/think robot:revenue:settle
```

### 2. 查看命令帮助
```bash
# 查看所有可用命令
php think

# 查看特定命令帮助
php think robot:revenue:settle --help
```

## 监控和日志

### 1. 日志配置
- 系统日志：记录在 ThinkPHP 的日志系统中
- 定时任务日志：可配置输出到指定文件
- 错误日志：记录执行过程中的错误信息

### 2. 监控建议
```bash
# 监控日志文件
tail -f /var/log/robot_revenue_settle.log

# 检查最近的执行记录
grep "智能体分成收益批量结算" /path/to/project/runtime/log/*.log

# 监控系统资源使用
top -p $(pgrep -f "robot:revenue:settle")
```

### 3. 邮件通知（可选）
```bash
# 在 crontab 中添加邮件通知
MAILTO=<EMAIL>
0 2 * * * cd /path/to/your/project/server && php think robot:revenue:settle
```

## 故障排查

### 1. 常见问题
- **权限问题**：确保 crontab 用户有执行 PHP 的权限
- **路径问题**：使用绝对路径避免路径错误
- **环境变量**：crontab 环境变量可能与shell不同
- **数据库连接**：确保定时任务能正常连接数据库

### 2. 调试方法
```bash
# 测试命令是否正常
/usr/bin/php /path/to/project/server/think robot:revenue:settle

# 检查 crontab 用户权限
sudo -u www-data /usr/bin/php /path/to/project/server/think robot:revenue:settle

# 查看 crontab 日志
grep CRON /var/log/syslog
```

## 配置验证

### 1. 设置结算方式为每日结算
1. 登录后台管理系统
2. 进入：AI知识库 → 智能体广场设置 → 智能体分成收益设置
3. 将"结算方式"改为"每日结算"
4. 保存配置

### 2. 验证定时任务
1. 手动执行命令确认正常运行
2. 检查数据库中待结算记录状态
3. 观察日志记录是否正常
4. 验证下次自动执行时间

## 性能优化建议

### 1. 批量大小调整
- 默认每批处理100条记录
- 可根据服务器性能调整批量大小
- 避免一次处理过多记录导致内存问题

### 2. 执行时间选择
- 建议选择业务低峰期执行（如凌晨1-3点）
- 避免与其他重要定时任务冲突
- 考虑数据库备份时间

### 3. 监控指标
- 执行时间
- 处理记录数量
- 成功/失败比率
- 内存和CPU使用情况 