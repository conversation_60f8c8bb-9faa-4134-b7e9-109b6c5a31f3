-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- 主机： 172.20.0.2
-- 生成日期： 2025-06-03 15:53:10
-- 服务器版本： 5.7.29
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `chatmoney`
--

-- --------------------------------------------------------

--
-- 表的结构 `cm_admin`
--

CREATE TABLE `cm_admin` (
  `id` int(10) UNSIGNED NOT NULL,
  `root` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '超管: [0=否, 1=是]',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '管理名称',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '用户头像',
  `account` varchar(32) NOT NULL DEFAULT '' COMMENT '账号',
  `password` varchar(32) NOT NULL COMMENT '密码',
  `login_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '最后登录时间',
  `login_ip` varchar(15) NOT NULL DEFAULT '' COMMENT '最后登录的IP',
  `multipoint_login` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '多处登录: [1=是, 0=否]',
  `disable` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否禁用: [0=否, 1=是]',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_admin_dept`
--

CREATE TABLE `cm_admin_dept` (
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '管理ID',
  `dept_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '部门ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门关联表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_admin_jobs`
--

CREATE TABLE `cm_admin_jobs` (
  `admin_id` int(10) UNSIGNED NOT NULL COMMENT '管理ID',
  `jobs_id` int(10) UNSIGNED NOT NULL COMMENT '岗位ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位关联表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_admin_role`
--

CREATE TABLE `cm_admin_role` (
  `admin_id` int(10) UNSIGNED NOT NULL COMMENT '管理ID',
  `role_id` int(10) UNSIGNED NOT NULL COMMENT '角色ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色关联表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_admin_session`
--

CREATE TABLE `cm_admin_session` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `terminal` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '平台类型: 1=PC管理后台, 2=Mobile手机管理后台',
  `token` varchar(32) NOT NULL DEFAULT '' COMMENT '令牌的值',
  `expire_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '到期时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理会话表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_ai_search_record`
--

CREATE TABLE `cm_ai_search_record` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `channel` varchar(100) NOT NULL DEFAULT '' COMMENT '通道: tiangong',
  `model` varchar(60) NOT NULL DEFAULT '' COMMENT '模型: [search=简易,copilot=增强,research=研究]',
  `type` varchar(60) NOT NULL DEFAULT '' COMMENT '类型: [all=全网,doc=文档,scholar=学术]',
  `ask` varchar(800) NOT NULL DEFAULT '' COMMENT '提问问题',
  `context` text COMMENT '上下文',
  `markdown` text COMMENT 'markdown',
  `results` longtext COMMENT '结果数据',
  `price` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '消耗费用',
  `ip` varchar(32) NOT NULL DEFAULT '' COMMENT '来源IP',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI搜索记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_article`
--

CREATE TABLE `cm_article` (
  `id` int(10) NOT NULL COMMENT '文章id',
  `cid` int(10) NOT NULL COMMENT '文章分类',
  `title` varchar(255) NOT NULL COMMENT '文章标题',
  `desc` varchar(255) NOT NULL DEFAULT '' COMMENT '文章简介',
  `abstract` text COMMENT '文章摘要',
  `image` varchar(128) NOT NULL DEFAULT '' COMMENT '文章图片',
  `author` varchar(255) NOT NULL DEFAULT '' COMMENT '文章作者',
  `content` text COMMENT '文章内容',
  `click_virtual` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '虚拟浏览量',
  `click_actual` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '实际浏览量',
  `is_show` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示: [1=是, 0=否]',
  `sort` int(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序编号',
  `create_time` int(11) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章内容表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_article_cate`
--

CREATE TABLE `cm_article_cate` (
  `id` int(10) NOT NULL COMMENT '文章分类id',
  `name` varchar(90) DEFAULT NULL COMMENT '分类名称',
  `sort` int(10) DEFAULT '0' COMMENT '排序',
  `is_show` tinyint(1) DEFAULT '1' COMMENT '是否显示:1-是;0-否',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章分类表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_article_collect`
--

CREATE TABLE `cm_article_collect` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `article_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '文章ID',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '收藏状态 0-未收藏 1-已收藏',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章收藏表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_card_code`
--

CREATE TABLE `cm_card_code` (
  `id` int(11) NOT NULL,
  `sn` varchar(32) NOT NULL COMMENT '卡密编号',
  `type` tinyint(1) NOT NULL COMMENT '类型：1-会员套餐；2-充值套餐；3-对话次数；4-绘画次数',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联套餐（充值、会员套餐）',
  `balance` int(11) NOT NULL DEFAULT '0' COMMENT '电力值',
  `card_num` int(11) NOT NULL COMMENT '卡密数量',
  `valid_start_time` int(11) NOT NULL COMMENT '有效开始时间',
  `valid_end_time` int(11) NOT NULL COMMENT '有效结束时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `rule_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '生成规则：1-批次编号+随机字母；2-批次编号+随机数字；',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡密' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_card_code_record`
--

CREATE TABLE `cm_card_code_record` (
  `id` int(11) NOT NULL,
  `sn` varchar(32) NOT NULL COMMENT '卡密编号',
  `card_id` int(11) NOT NULL COMMENT '卡密id',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-未使用，1-已使用',
  `user_id` int(11) DEFAULT NULL COMMENT '使用的用户id',
  `use_time` int(11) DEFAULT NULL COMMENT '使用时间',
  `package_snapshot` text COMMENT '套餐快照',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡密兑换记录' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_chat_category`
--

CREATE TABLE `cm_chat_category` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '类目名称',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `status` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '状态: [1=开启, 0=关闭]',
  `image` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '图标',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例分类表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_chat_record`
--

CREATE TABLE `cm_chat_record` (
  `id` int(11) NOT NULL,
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `category_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '记录分类',
  `other_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创作的ID',
  `chat_model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '对话模型ID',
  `ask` text NOT NULL COMMENT '提问',
  `reply` text NOT NULL COMMENT '回复',
  `reasoning` text COMMENT '思考过程',
  `channel` varchar(64) NOT NULL DEFAULT '' COMMENT '模型渠道',
  `model` varchar(128) NOT NULL DEFAULT '' COMMENT '对话模型',
  `tokens` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '消耗的tokens',
  `price` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '消耗的费用',
  `correlation` text COMMENT '相关问题',
  `files_plugin` text COMMENT '图片理解',
  `type` int(1) NOT NULL COMMENT '记录类型: [1=对话, 2=创作,3-角色]',
  `creation_type` tinyint(11) NOT NULL DEFAULT '1' COMMENT '创作类型:[1-常规,2-扩写,3-简写,4-续写,5-改写-正式得体,6-改写-严肃庄重,7-改写-轻松,8-改写-热情]',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否显示: [1=是的, 0=否的]',
  `censor_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '审核状态: [0=未审核, 1=合规, 2=不合规, 3=疑似, 4=审核失败]',
  `censor_result` text COMMENT '审核结果',
  `censor_num` int(2) UNSIGNED NOT NULL DEFAULT '0' COMMENT '审核次数',
  `extra` text COMMENT '预留字段',
  `flows` text COMMENT 'tokens信息',
  `reply_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '回复类型：1-模型回复；2-默认回复',
  `ip` varchar(255) DEFAULT NULL COMMENT '访客ip地址',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_chat_record_category`
--

CREATE TABLE `cm_chat_record_category` (
  `id` int(10) NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户id',
  `name` varchar(64) NOT NULL DEFAULT '' COMMENT '对话分类名称',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话分类表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_chat_record_collect`
--

CREATE TABLE `cm_chat_record_collect` (
  `id` int(10) NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `records_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '对话记录ID',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话收藏表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_chat_sample`
--

CREATE TABLE `cm_chat_sample` (
  `id` int(10) UNSIGNED NOT NULL,
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '类目id',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `content` varchar(255) CHARACTER SET utf8 NOT NULL COMMENT '内容',
  `status` tinyint(1) NOT NULL COMMENT '状态: [1=是, 0=否]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话示例表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_config`
--

CREATE TABLE `cm_config` (
  `id` int(10) NOT NULL,
  `type` varchar(30) NOT NULL DEFAULT '' COMMENT '类型',
  `name` varchar(60) NOT NULL DEFAULT '' COMMENT '名称',
  `value` text COMMENT '值',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局配置表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_creation_category`
--

CREATE TABLE `cm_creation_category` (
  `id` int(10) NOT NULL,
  `image` varchar(128) NOT NULL DEFAULT '' COMMENT '分类图标',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '类目名称',
  `sort` int(10) NOT NULL COMMENT '排序编号',
  `status` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '启用状态: [1=开启, 0=关闭]',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='创作类别表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_creation_model`
--

CREATE TABLE `cm_creation_model` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `name` varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '名称',
  `image` varchar(64) NOT NULL COMMENT '图标',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '类别',
  `status` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '状态：1-开启，0-关闭',
  `content` text COMMENT '主题内容',
  `tips` text COMMENT '提示文字',
  `context_num` int(5) UNSIGNED NOT NULL DEFAULT '2' COMMENT '上下文总数',
  `n` int(5) UNSIGNED NOT NULL DEFAULT '1' COMMENT '最大回复',
  `top_p` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.9' COMMENT '随机属性',
  `presence_penalty` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.5' COMMENT '话题属性',
  `frequency_penalty` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.5' COMMENT '重复属性',
  `temperature` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.6' COMMENT '词汇属性',
  `max_tokens` int(5) UNSIGNED NOT NULL DEFAULT '150' COMMENT '最大回复',
  `form` text NOT NULL COMMENT '表单数据',
  `virtual_use_num` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '虚拟使用',
  `system` text COMMENT '全局指令',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='创作模型表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_creation_model_collect`
--

CREATE TABLE `cm_creation_model_collect` (
  `id` int(10) NOT NULL,
  `creation_id` int(10) UNSIGNED NOT NULL COMMENT '创作ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='创作收藏表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_decorate_page`
--

CREATE TABLE `cm_decorate_page` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `type` tinyint(2) UNSIGNED NOT NULL DEFAULT '10' COMMENT '页面类型: [1=首页装修, 2=悬浮菜单]',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '页面名称',
  `data` longtext COMMENT '页面数据',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='装修页面表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_dept`
--

CREATE TABLE `cm_dept` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'id',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '部门名称',
  `pid` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '上级部门',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `leader` varchar(64) NOT NULL DEFAULT '' COMMENT '负责人',
  `mobile` varchar(16) NOT NULL DEFAULT '' COMMENT '联系电话',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '部门状态: [0=停用, 1=正常]',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门管理表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_dev_crontab`
--

CREATE TABLE `cm_dev_crontab` (
  `id` int(10) NOT NULL,
  `name` varchar(32) NOT NULL COMMENT '定时任务名称',
  `type` tinyint(1) NOT NULL COMMENT '类型 1-定时任务',
  `system` tinyint(4) DEFAULT '0' COMMENT '是否系统任务 0-否 1-是',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `command` varchar(64) NOT NULL COMMENT '命令内容',
  `params` varchar(64) DEFAULT '' COMMENT '参数',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 1-运行 2-停止 3-错误',
  `expression` varchar(64) NOT NULL COMMENT '运行规则',
  `error` varchar(256) DEFAULT NULL COMMENT '运行失败原因',
  `last_time` int(10) DEFAULT NULL COMMENT '最后执行时间',
  `time` varchar(64) DEFAULT '0' COMMENT '实时执行时长',
  `max_time` varchar(64) DEFAULT '0' COMMENT '最大执行时长',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划任务表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_dev_pay_config`
--

CREATE TABLE `cm_dev_pay_config` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '模版名称',
  `pay_way` tinyint(1) NOT NULL COMMENT '支付方式: [1=余额支付, 2=微信支付, 3=支付宝支付]',
  `config` text COMMENT '支付配置',
  `icon` varchar(250) NOT NULL COMMENT '图标',
  `remark` varchar(250) NOT NULL COMMENT '备注',
  `sort` int(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付方式表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_dev_pay_way`
--

CREATE TABLE `cm_dev_pay_way` (
  `id` int(10) UNSIGNED NOT NULL,
  `pay_config_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '支付配置ID',
  `scene` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '支付场景: [1=微信小程序, 2=微信公众号, 3=H5, 4=PC, 5=APP]',
  `is_default` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '默认支付: [0=否, 1=是]',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '配置状态: [0=关闭, 1=开启]'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付配置表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_dict_data`
--

CREATE TABLE `cm_dict_data` (
  `id` int(10) NOT NULL COMMENT 'id',
  `name` varchar(255) NOT NULL COMMENT '数据名称',
  `value` varchar(255) NOT NULL COMMENT '数据值',
  `type_id` int(10) NOT NULL COMMENT '字典类型id',
  `type_value` varchar(255) NOT NULL COMMENT '字典类型',
  `sort` int(10) DEFAULT '0' COMMENT '排序值',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0-停用 1-正常',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典数据表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_dict_type`
--

CREATE TABLE `cm_dict_type` (
  `id` int(11) NOT NULL COMMENT 'id',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '字典名称',
  `type` varchar(255) NOT NULL DEFAULT '' COMMENT '字典类型名称',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0-停用 1-正常',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典类型表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_distribution_apply`
--

CREATE TABLE `cm_distribution_apply` (
  `id` int(11) UNSIGNED NOT NULL,
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户id',
  `name` varchar(12) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `mobile` varchar(32) NOT NULL COMMENT '手机号码',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `audit_time` int(10) DEFAULT NULL COMMENT '审核时间',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '状态：1-待审核；2-审核通过；3-审核不通过',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销申请表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_distribution_order`
--

CREATE TABLE `cm_distribution_order` (
  `id` int(11) NOT NULL,
  `order_type` tinyint(1) NOT NULL COMMENT '订单类型：1-充值订单；2-会员订单；',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `order_sn` varchar(255) NOT NULL COMMENT '订单编号',
  `order_amount` decimal(10,2) UNSIGNED NOT NULL COMMENT '实付金额',
  `pay_time` int(10) DEFAULT NULL COMMENT '支付时间',
  `user_id` int(11) NOT NULL COMMENT '下单用户ID',
  `first_user_id` int(11) NOT NULL COMMENT '一级分销商用户ID',
  `first_ratio` decimal(10,2) UNSIGNED NOT NULL COMMENT '一级分销佣金比例',
  `first_reward` decimal(10,2) UNSIGNED NOT NULL COMMENT '一级分销奖励',
  `second_user_id` int(11) DEFAULT NULL COMMENT '二级分销商用户ID',
  `second_ratio` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT '二级分销佣金比例',
  `second_reward` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT '二级分销奖励',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销订单表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_lora`
--

CREATE TABLE `cm_draw_lora` (
  `id` int(11) NOT NULL COMMENT '主键',
  `cover` varchar(100) NOT NULL COMMENT '模型封面',
  `title` varchar(50) NOT NULL COMMENT '模型名称',
  `model_name` varchar(100) NOT NULL COMMENT '模型标识',
  `sort` int(11) NOT NULL COMMENT '排序',
  `status` int(11) NOT NULL COMMENT '状态',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SD微调模型表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_model`
--

CREATE TABLE `cm_draw_model` (
  `id` int(11) NOT NULL COMMENT '主键',
  `title` varchar(50) CHARACTER SET utf8 NOT NULL COMMENT '模型标题',
  `model_name` varchar(255) CHARACTER SET utf8 NOT NULL COMMENT '模型名称',
  `cover` varchar(255) CHARACTER SET utf8 NOT NULL COMMENT '模型封面',
  `category_id` int(10) NOT NULL COMMENT '模型分类',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `status` int(1) NOT NULL COMMENT '状态:[0=开启,1=关闭]',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SD绘画模型表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_model_category`
--

CREATE TABLE `cm_draw_model_category` (
  `id` int(11) NOT NULL COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8 NOT NULL COMMENT '分类名称',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `status` int(1) NOT NULL COMMENT '状态:[0=正常,1=禁用]',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SD绘画模型分类表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_model_lora_relation`
--

CREATE TABLE `cm_draw_model_lora_relation` (
  `id` int(10) NOT NULL COMMENT '主键',
  `lora_id` int(10) NOT NULL COMMENT '微调模型ID',
  `model_id` int(10) NOT NULL COMMENT '主要模型ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SD绘画模型分类关联表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_prompt`
--

CREATE TABLE `cm_draw_prompt` (
  `id` int(11) NOT NULL,
  `category_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分类id',
  `model` varchar(100) NOT NULL DEFAULT 'sd' COMMENT '绘画模型',
  `prompt` varchar(255) NOT NULL DEFAULT '' COMMENT '中文关键词',
  `prompt_en` varchar(255) NOT NULL DEFAULT '' COMMENT '英文关键词',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `status` int(11) NOT NULL COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SD绘画提示词库表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_prompt_category`
--

CREATE TABLE `cm_draw_prompt_category` (
  `id` int(11) NOT NULL,
  `model` varchar(100) NOT NULL DEFAULT 'sd' COMMENT '绘画模型',
  `pid` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '上级id',
  `level` tinyint(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '层级',
  `name` varchar(32) NOT NULL COMMENT '类目名称',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `status` int(11) NOT NULL COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SD绘画提示词分类' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_prompt_example`
--

CREATE TABLE `cm_draw_prompt_example` (
  `id` int(11) UNSIGNED NOT NULL,
  `model` varchar(100) NOT NULL DEFAULT 'sd' COMMENT '绘画模型',
  `prompt` text NOT NULL COMMENT '中文关键词',
  `prompt_en` text NOT NULL COMMENT '英文关键词',
  `sort` int(11) UNSIGNED DEFAULT '0' COMMENT '排序',
  `status` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SD绘画提示词示例表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_records`
--

CREATE TABLE `cm_draw_records` (
  `id` int(11) NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户id',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '绘图类型 1-文生图  2-图生图 3-选中放大 4-选中变换',
  `action` varchar(255) NOT NULL DEFAULT '' COMMENT '动作 绘图动作',
  `prompt` text NOT NULL COMMENT '提示词',
  `prompt_en` text COMMENT '提示词译文',
  `prompt_desc` text NOT NULL COMMENT '提示词详情',
  `prompt_other` varchar(300) DEFAULT NULL COMMENT '提示词其他参数',
  `image` varchar(255) DEFAULT '' COMMENT '本地图片地址',
  `image_base` varchar(255) DEFAULT '' COMMENT '垫图地址',
  `image_mask` text COMMENT '重绘区域base64',
  `image_url` varchar(500) DEFAULT NULL COMMENT '绘图服务返回地址',
  `image_id` varchar(255) DEFAULT NULL COMMENT '绘图服务图片id',
  `scale` varchar(20) DEFAULT NULL COMMENT '图片比例',
  `task_id` varchar(200) DEFAULT '0' COMMENT '绘图服务任务id',
  `origin_task_id` varchar(100) DEFAULT '' COMMENT '父级任务ID',
  `thumbnail` varchar(500) DEFAULT '' COMMENT '缩略图',
  `notify_snap` text COMMENT '回调快照信息',
  `able_actions` varchar(255) DEFAULT NULL COMMENT '可操作动作',
  `use_tokens` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '消耗tokens',
  `fail_reason` varchar(600) DEFAULT NULL COMMENT '失败原因',
  `model` varchar(100) DEFAULT '' COMMENT '绘画模型',
  `version` varchar(255) DEFAULT '' COMMENT '绘画版本',
  `negative_prompt` varchar(300) DEFAULT NULL COMMENT '绘画中不想出现的参数',
  `style` varchar(255) DEFAULT '' COMMENT '绘画风格',
  `quality` varchar(30) DEFAULT '' COMMENT '图片质量',
  `engine` varchar(255) DEFAULT 'midjourney' COMMENT '绘画引擎',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '状态：0-未启动 1-执行中 2-失败 3-成功',
  `censor_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态：0-未审核；1-合规；2-不合规；3-疑似；4-审核失败；',
  `ip` varchar(255) NOT NULL COMMENT '访客ip地址',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间',
  `complex_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '高级参数',
  `loras` text COMMENT '微调模型'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SD绘画记录表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_records_collect`
--

CREATE TABLE `cm_draw_records_collect` (
  `id` int(11) NOT NULL,
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `square_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '广场ID',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='绘画记录收藏';

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_square`
--

CREATE TABLE `cm_draw_square` (
  `id` int(11) NOT NULL,
  `operate_id` int(11) NOT NULL COMMENT '操作人ID',
  `source` tinyint(1) NOT NULL COMMENT '来源：1-官方；2-用户；',
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '分类id',
  `records_id` int(11) NOT NULL DEFAULT '0' COMMENT '绘画分享来源ID',
  `prompts` text CHARACTER SET utf8mb4 NOT NULL COMMENT '英文提示词',
  `prompts_cn` text CHARACTER SET utf8mb4 COMMENT '中文提示词',
  `image` varchar(255) CHARACTER SET utf8mb4 NOT NULL COMMENT '图片',
  `thumbnail` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '缩略图',
  `is_show` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否显示：1-是；0-否；',
  `is_slice` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否为切图 [0-否 1-是]',
  `verify_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态：0-待审核；1-审核通过；2-审核不通过；',
  `verify_result` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '审核结果',
  `avatar` varchar(200) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '头像',
  `nickname` varchar(32) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '用户昵称',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='绘画广场表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_draw_task`
--

CREATE TABLE `cm_draw_task` (
  `id` int(11) NOT NULL COMMENT '主键id',
  `action` varchar(255) NOT NULL DEFAULT '' COMMENT '动作  generate upsample variation',
  `task_id` varchar(30) NOT NULL DEFAULT '' COMMENT '任务id',
  `notify_hook` varchar(255) DEFAULT '' COMMENT '回调地址',
  `notify_msg` text COMMENT '回调错误信息',
  `image` varchar(255) DEFAULT NULL COMMENT '图片地址',
  `prompt` text COMMENT '提示词-中文',
  `prompt_desc` text COMMENT '完整描述',
  `index` int(1) UNSIGNED DEFAULT '0' COMMENT '图片索引',
  `image_base` varchar(255) DEFAULT NULL COMMENT '垫图图片地址',
  `image_msg_id` varchar(255) DEFAULT '' COMMENT '消息id，基于消息id进行图片变换操作',
  `image_msg_hash` text COMMENT '消息hash,基于消息hash进行图片变换操作',
  `image_url` varchar(600) DEFAULT NULL COMMENT '图片地址',
  `msg_id` varchar(255) DEFAULT NULL COMMENT '图片消息id',
  `msg_hash` text COMMENT '图片消息hash',
  `request_snap` text COMMENT '请求快照',
  `response_snap` text COMMENT 'mj响应快照',
  `token` varchar(255) DEFAULT '' COMMENT '绘画token',
  `fail_reason` text COMMENT '失败原因',
  `progress` int(3) DEFAULT '0' COMMENT '任务进度',
  `status` int(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '任务状态：0-未处理 1-已提交 2-执行中 3-成功 4-失败',
  `submit_time` int(11) UNSIGNED DEFAULT NULL COMMENT '提交时间',
  `start_time` int(11) UNSIGNED DEFAULT NULL COMMENT '开始执行时间',
  `finish_time` int(11) UNSIGNED DEFAULT NULL COMMENT '完成时间',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SD绘画任务表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_email_log`
--

CREATE TABLE `cm_email_log` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `scene_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '场景ID',
  `email` varchar(255) NOT NULL COMMENT '邮箱',
  `content` varchar(255) NOT NULL COMMENT '发送内容',
  `code` varchar(32) DEFAULT NULL COMMENT '验证码',
  `is_verify` tinyint(1) UNSIGNED DEFAULT '0' COMMENT '是否已验证: [0=否, 1=是]',
  `check_num` int(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '验证次数',
  `send_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '发送状态: [0=发送中, 1=发送成功, 2=发送失败]',
  `send_time` int(10) NOT NULL COMMENT '发送时间',
  `results` text COMMENT '发送结果',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_example_category`
--

CREATE TABLE `cm_example_category` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '类别名称',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例库类别表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_example_content`
--

CREATE TABLE `cm_example_content` (
  `id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属类别ID',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '示例标题',
  `question` text NOT NULL COMMENT '问题内容',
  `answer` text NOT NULL COMMENT '答案内容',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例库内容表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_file`
--

CREATE TABLE `cm_file` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `cid` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '类目ID',
  `source_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '上传者ID',
  `source` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '来源类型: [0=后台, 1=用户]',
  `type` tinyint(2) UNSIGNED NOT NULL DEFAULT '10' COMMENT '文件类型: [10=图片, 20=视频]',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '文件名称',
  `uri` varchar(200) NOT NULL COMMENT '文件路径',
  `ip` varchar(255) DEFAULT NULL COMMENT '访客ip地址',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件管理表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_file_cate`
--

CREATE TABLE `cm_file_cate` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '父级ID',
  `type` tinyint(2) UNSIGNED NOT NULL DEFAULT '10' COMMENT '类型: [10=图片，20=视频，30=文件]',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '分类名称',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件分类表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_generate_column`
--

CREATE TABLE `cm_generate_column` (
  `id` int(10) NOT NULL COMMENT 'id',
  `table_id` int(11) NOT NULL DEFAULT '0' COMMENT '表id',
  `column_name` varchar(100) NOT NULL DEFAULT '' COMMENT '字段名称',
  `column_comment` varchar(300) NOT NULL DEFAULT '' COMMENT '字段描述',
  `column_type` varchar(100) NOT NULL DEFAULT '' COMMENT '字段类型',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填 0-非必填 1-必填',
  `is_pk` tinyint(1) DEFAULT '0' COMMENT '是否为主键 0-不是 1-是',
  `is_insert` tinyint(1) DEFAULT '0' COMMENT '是否为插入字段 0-不是 1-是',
  `is_update` tinyint(1) DEFAULT '0' COMMENT '是否为更新字段 0-不是 1-是',
  `is_lists` tinyint(1) DEFAULT '0' COMMENT '是否为列表字段 0-不是 1-是',
  `is_query` tinyint(1) DEFAULT '0' COMMENT '是否为查询字段 0-不是 1-是',
  `query_type` varchar(100) DEFAULT '=' COMMENT '查询类型',
  `view_type` varchar(100) DEFAULT 'input' COMMENT '显示类型',
  `dict_type` varchar(255) DEFAULT '' COMMENT '字典类型',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生成字段表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_generate_table`
--

CREATE TABLE `cm_generate_table` (
  `id` int(10) NOT NULL COMMENT 'id',
  `table_name` varchar(200) NOT NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(300) NOT NULL DEFAULT '' COMMENT '表描述',
  `template_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '模板类型 0-单表(curd) 1-树表(curd)',
  `author` varchar(100) DEFAULT '' COMMENT '作者',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `generate_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '生成方式  0-压缩包下载 1-生成到模块',
  `module_name` varchar(100) DEFAULT '' COMMENT '模块名',
  `class_dir` varchar(100) DEFAULT '' COMMENT '类目录名',
  `class_comment` varchar(100) DEFAULT '' COMMENT '类描述',
  `admin_id` int(11) DEFAULT '0' COMMENT '管理员id',
  `menu` text COMMENT '菜单配置',
  `delete` text COMMENT '删除配置',
  `tree` text COMMENT '树表配置',
  `relations` text COMMENT '关联配置',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生成信息表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_jobs`
--

CREATE TABLE `cm_jobs` (
  `id` int(10) NOT NULL COMMENT 'id',
  `name` varchar(50) NOT NULL COMMENT '岗位名称',
  `code` varchar(64) NOT NULL COMMENT '岗位编码',
  `sort` int(10) DEFAULT '0' COMMENT '显示顺序',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态（0停用 1正常）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位管理表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_digital`
--

CREATE TABLE `cm_kb_digital` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '数字人名称',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '数字人头像',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '数字人封面',
  `wide_stay_video` varchar(255) NOT NULL DEFAULT '' COMMENT '宽屏人物待机视频',
  `wide_talk_video` varchar(255) NOT NULL DEFAULT '' COMMENT '宽屏人物说话视频',
  `vertical_stay_video` varchar(255) NOT NULL DEFAULT '' COMMENT '竖屏人物待机视频',
  `vertical_talk_video` varchar(255) NOT NULL DEFAULT '' COMMENT '竖屏人物说话视频',
  `channel` varchar(100) NOT NULL DEFAULT '' COMMENT '配音渠道',
  `dubbing` varchar(100) NOT NULL DEFAULT '' COMMENT '配音角色',
  `idle_time` int(10) UNSIGNED NOT NULL DEFAULT '10' COMMENT '闲时时间',
  `idle_reply` text COMMENT '闲时回复',
  `is_disable` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否禁用: [0=否, 1=是]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数字人管理表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_know`
--

CREATE TABLE `cm_kb_know` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `create_uid` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `image` varchar(250) NOT NULL DEFAULT '' COMMENT '知识库封面',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '知识库名称',
  `intro` varchar(500) NOT NULL DEFAULT '' COMMENT '知识库简介',
  `documents_model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '处理模型ID',
  `embedding_model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '向量模型ID',
  `documents_model_sub_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '处理模型子ID',
  `embedding_model_sub_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '向量模型子ID',
  `documents_model` varchar(100) NOT NULL DEFAULT '' COMMENT '处理模型 (废弃)',
  `embedding_model` varchar(100) NOT NULL DEFAULT '' COMMENT '训练模型 (废弃)',
  `is_enable` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否启用: [0=否, 1=是]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库管理表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_know_files`
--

CREATE TABLE `cm_kb_know_files` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '所属用户ID',
  `know_id` int(10) UNSIGNED NOT NULL COMMENT '知识库ID',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '文件名称',
  `file` varchar(300) NOT NULL DEFAULT '' COMMENT '文件路径',
  `is_qa` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'QA拆分: [0=否, 1=待拆分, 2=拆分完成]',
  `is_default` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '默认固定: [0=否, 1=是]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库文件表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_know_qa`
--

CREATE TABLE `cm_kb_know_qa` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '模型的ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户的ID',
  `kb_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '知识库ID',
  `fd_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '文件的ID',
  `name` varchar(300) NOT NULL DEFAULT '' COMMENT '文件名称',
  `content` text COMMENT '文本内容',
  `results` text COMMENT '拆分结果',
  `usage` text COMMENT 'tokens信息',
  `tokens` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '消耗的tokens',
  `model` varchar(100) NOT NULL DEFAULT '' COMMENT '拆分的模型',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '拆分状态: [0=等待拆分, 1=拆分中, 2=拆分成功, 3=拆分失败]',
  `error` text COMMENT '错误信息',
  `task_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '任务耗时',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库QA表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_know_team`
--

CREATE TABLE `cm_kb_know_team` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `kb_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '知识库ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户的ID',
  `power` tinyint(3) UNSIGNED NOT NULL DEFAULT '3' COMMENT '拥有权限: [1=可管理, 2=可编辑, 3=可查看]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库团队表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot`
--

CREATE TABLE `cm_kb_robot` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `cate_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '类目ID',
  `code` varchar(100) NOT NULL DEFAULT '' COMMENT '机器人编号',
  `kb_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '关联知识库',
  `icons` varchar(250) NOT NULL DEFAULT '' COMMENT '对话的图标',
  `image` varchar(250) NOT NULL DEFAULT '' COMMENT '机器人封面',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '机器人名称',
  `intro` varchar(500) NOT NULL DEFAULT '' COMMENT '机器人简介',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序的编号',
  `model` varchar(100) NOT NULL DEFAULT '' COMMENT 'AI模型(废弃字段)',
  `model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'AI主模型ID',
  `model_sub_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'AI子模型ID',
  `roles_prompt` text COMMENT '角色设定词',
  `limit_prompt` text COMMENT '系统限定词',
  `temperature` float(2,1) UNSIGNED NOT NULL DEFAULT '0.8' COMMENT '属性温度',
  `search_similarity` float(5,3) UNSIGNED NOT NULL DEFAULT '0.800' COMMENT '搜索相似度',
  `search_limits` smallint(5) UNSIGNED NOT NULL DEFAULT '1' COMMENT '搜索单词数',
  `search_empty_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '搜索空类型: [1=GPT回复, 2=固定回复]',
  `search_empty_text` text COMMENT '搜索空文本',
  `welcome_introducer` text COMMENT '欢迎引导词',
  `related_issues_num` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '推荐的问题',
  `copyright` text COMMENT '底部的版权',
  `share_bg` varchar(300) DEFAULT '' COMMENT '分享背景图',
  `digital_bg` varchar(30) NOT NULL COMMENT '数字人背景',
  `flow_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '工作流启用状态',
  `flow_config` text COMMENT '工作流配置',
  `context_num` int(5) DEFAULT '0' COMMENT '上下文数量',
  `digital_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '数字人绑定',
  `is_digital` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '数字人启用: [0=否, 1=是]',
  `is_show_feedback` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '显示反馈: [0=否, 1=是]',
  `is_show_context` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '显示上下文: [0=否, 1=是]',
  `is_show_quote` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '显示引用词: [0=否, 1=是]',
  `is_public` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否公开它: [0=否, 1=是]',
  `is_enable` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否可使用: [0=否, 1=是]',
  `support_file` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否支持文件: [0=否, 1=是]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人管理表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_category`
--

CREATE TABLE `cm_kb_robot_category` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '名称',
  `image` varchar(250) NOT NULL DEFAULT '' COMMENT '图标',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `is_enable` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否启用: [0=否, 1=是]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人分类表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_instruct`
--

CREATE TABLE `cm_kb_robot_instruct` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户的ID',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `keyword` varchar(200) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '关键词',
  `content` text CHARACTER SET utf8 COMMENT '回复内容',
  `images` text CHARACTER SET utf8 COMMENT '上传图片',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人指令表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_publish`
--

CREATE TABLE `cm_kb_robot_publish` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `type` tinyint(2) NOT NULL COMMENT '类型: [1=网页, 2=公众号, 3=JS嵌入, 4=API调用]',
  `chat_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '对话方式: [1=文本, 2=数字人]',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户的ID',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '分享名称',
  `apikey` varchar(200) NOT NULL DEFAULT '' COMMENT '渠道编号',
  `secret` varchar(200) NOT NULL COMMENT '访问密钥',
  `context_num` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '上下文数',
  `limit_total_chat` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户总的限制对话',
  `limit_today_chat` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户每天限制对话',
  `limit_exceed` varchar(500) NOT NULL DEFAULT '' COMMENT '超出限制默认回复',
  `use_count` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '调用次数',
  `use_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '使用时间',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人发布表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_record`
--

CREATE TABLE `cm_kb_robot_record` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户的ID',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分类的ID',
  `square_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '广场的ID',
  `chat_model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '对话模型ID',
  `emb_model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '向量模型ID',
  `ask` text COMMENT '提问',
  `reply` text COMMENT '答复',
  `reasoning` text COMMENT '思考过程',
  `files_plugin` longtext COMMENT '文件理解',
  `images` text COMMENT '附带图片',
  `video` text COMMENT '附带视频',
  `files` text COMMENT '附带文件',
  `quotes` text COMMENT '引用内容',
  `context` text COMMENT '上下文组',
  `correlation` text COMMENT '相关问题',
  `flows` text NOT NULL COMMENT 'tokens信息',
  `model` varchar(100) NOT NULL DEFAULT '' COMMENT '对话模型',
  `tokens` decimal(15,7) NOT NULL COMMENT '消耗金额',
  `is_revenue_shared` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否已分成：0-未分成 1-已分成',
  `revenue_log_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分成记录ID',
  `feedback` text COMMENT '用户反馈',
  `share_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分享的ID',
  `share_apikey` varchar(80) NOT NULL DEFAULT '' COMMENT '分享的密钥',
  `share_identity` varchar(60) NOT NULL DEFAULT '' COMMENT '分享的身份',
  `censor_status` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '审核状态: [0=未审核, 1=合规, 2=不合规, 3=疑似, 4=审核失败]',
  `censor_result` text COMMENT '审核结果',
  `censor_num` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '审核次数',
  `is_feedback` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否反馈: [0=否, 1=是]',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否显示: [0=否, 1=是]',
  `is_flow` tinyint(1) NOT NULL DEFAULT '0' COMMENT '使用工作流 0-未使用 1-已使用',
  `task_time` int(60) UNSIGNED NOT NULL DEFAULT '0' COMMENT '对话耗时',
  `unique_id` varchar(100) NOT NULL DEFAULT '' COMMENT '分享唯一ID',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人对话表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_revenue_config`
--

CREATE TABLE `cm_kb_robot_revenue_config` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `is_enable` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否启用分成功能：0-关闭 1-开启',
  `share_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '分享者分成比例(百分比): 0.00-100.00',
  `platform_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT '100.00' COMMENT '平台保留比例(百分比): 0.00-100.00',
  `min_revenue` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.01' COMMENT '最小分成金额(电力值)',
  `settle_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '结算方式：1-实时结算 2-每日结算',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能体分成收益配置表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_revenue_log`
--

CREATE TABLE `cm_kb_robot_revenue_log` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '使用者用户ID',
  `sharer_id` int(10) UNSIGNED NOT NULL COMMENT '分享者用户ID',
  `robot_id` int(10) UNSIGNED NOT NULL COMMENT '智能体ID',
  `square_id` int(10) UNSIGNED NOT NULL COMMENT '广场记录ID',
  `record_id` int(10) UNSIGNED NOT NULL COMMENT '对话记录ID',
  `total_cost` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '总消耗电力值',
  `share_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '分享者获得电力值',
  `platform_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '平台保留电力值',
  `share_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '分成比例(百分比)',
  `settle_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '结算状态：0-未结算 1-已结算',
  `settle_time` int(10) UNSIGNED DEFAULT NULL COMMENT '结算时间',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能体分成收益记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_session`
--

CREATE TABLE `cm_kb_robot_session` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `square_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '广场ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `name` varchar(200) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '分类名称',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人会话表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_share_log`
--

CREATE TABLE `cm_kb_robot_share_log` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `robot_id` int(10) UNSIGNED NOT NULL COMMENT '机器人ID',
  `balance` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '赠送电力值',
  `channel` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分享渠道: [1-微信小程序 2-微信公众号 3-手机H5 4-电脑PC 5-苹果APP 6-安卓APP]',
  `square_id` int(10) NOT NULL COMMENT '广场id',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人分享记录' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_square`
--

CREATE TABLE `cm_kb_robot_square` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `robot_id` int(10) UNSIGNED NOT NULL COMMENT '机器人ID',
  `cate_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分类ID',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序编号',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否显示: [0=否， 1=是]',
  `verify_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态：0-待审核；1-审核通过；2-审核不通过；',
  `verify_result` varchar(255) DEFAULT NULL COMMENT '审核结果',
  `total_revenue` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '累计分成收益',
  `use_count` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '使用次数',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人广场表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_visitor`
--

CREATE TABLE `cm_kb_robot_visitor` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `ip` varchar(100) NOT NULL COMMENT '访客IP',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `terminal` tinyint(1) NOT NULL COMMENT '访问终端',
  `visit` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '浏览量',
  `create_time` int(10) DEFAULT NULL COMMENT '访问时间',
  `update_time` int(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人访问表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_key_pool`
--

CREATE TABLE `cm_key_pool` (
  `id` int(10) NOT NULL COMMENT 'ID',
  `model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '模型ID',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '类型: [1=AI对话, 2=AI向量, 3=语音播报, 4=语音输入]',
  `channel` varchar(32) NOT NULL DEFAULT '' COMMENT '渠道: [gpt3.5,gpt4.0,zhipu,baidu]',
  `key` varchar(800) NOT NULL DEFAULT '' COMMENT '密钥',
  `appid` varchar(20) NOT NULL DEFAULT '' COMMENT 'appId',
  `secret` varchar(128) NOT NULL DEFAULT '' COMMENT 'secret',
  `api` varchar(300) DEFAULT '' COMMENT '错误接口',
  `notice` text COMMENT '错误通知',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '密钥状态',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='密钥池子表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_key_rule`
--

CREATE TABLE `cm_key_rule` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '模型ID',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '规则类型: [1=对话]',
  `channel` varchar(32) NOT NULL DEFAULT '' COMMENT '接口类型',
  `rule` varchar(500) NOT NULL DEFAULT '' COMMENT '停用规则',
  `prompt` varchar(255) NOT NULL DEFAULT '' COMMENT '停用提示',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '规则状态: [1=开启, 0=关闭]',
  `create_time` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='密钥规则表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_member_adjust_log`
--

CREATE TABLE `cm_member_adjust_log` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `operate_id` int(11) NOT NULL COMMENT '操作员ID',
  `package_id` int(11) NOT NULL DEFAULT '0' COMMENT '套餐',
  `member_end_time` int(11) NOT NULL DEFAULT '0' COMMENT '套餐时长',
  `is_perpetual` tinyint(1) NOT NULL COMMENT '是否永久:1-是,0-否',
  `package_snap` text COMMENT '套餐快照(type=1是记录的是原套餐快照，type=2是套餐快照)',
  `type` int(11) DEFAULT '1' COMMENT '类型：1-后台调整，2-卡密兑换',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员等级调整记录' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_member_benefits`
--

CREATE TABLE `cm_member_benefits` (
  `id` int(11) NOT NULL,
  `package_id` int(11) NOT NULL COMMENT '套餐id',
  `name` varchar(255) NOT NULL COMMENT '权益名称',
  `image` varchar(255) NOT NULL COMMENT '权益图标',
  `describe` varchar(255) DEFAULT NULL COMMENT '权益描述',
  `status` tinyint(1) NOT NULL COMMENT '状态：1-开启,0-关闭',
  `sort` int(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员权益' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_member_order`
--

CREATE TABLE `cm_member_order` (
  `id` int(11) NOT NULL COMMENT 'id',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `order_sn` varchar(64) NOT NULL COMMENT '订单编号',
  `terminal` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '订单来源：1-微信小程序；2-微信公众号；3-手机H5；4-PC；5-苹果app；6-安卓app；',
  `pay_sn` varchar(255) DEFAULT '' COMMENT '支付编号-冗余字段，针对微信同一主体不同客户端支付需用不同订单号预留。',
  `pay_way` tinyint(2) NOT NULL DEFAULT '2' COMMENT '支付方式 2-微信支付 3-支付宝支付',
  `pay_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付状态：0-待支付；1-已支付',
  `pay_time` int(10) DEFAULT NULL COMMENT '支付时间',
  `order_amount` decimal(10,2) UNSIGNED NOT NULL COMMENT '实付金额',
  `discount_amount` decimal(10,2) UNSIGNED DEFAULT '0.00' COMMENT '优惠金额',
  `total_amount` decimal(10,2) UNSIGNED NOT NULL COMMENT '订单总价',
  `transaction_id` varchar(128) DEFAULT NULL COMMENT '第三方平台交易流水号',
  `member_package_id` int(11) NOT NULL COMMENT '套餐ID',
  `member_price_id` int(11) NOT NULL COMMENT '套餐价格id',
  `member_package_info` text COMMENT '套餐信息',
  `refund_status` tinyint(1) DEFAULT '0' COMMENT '退款状态 0-未退款 1-已退款',
  `refund_transaction_id` varchar(255) DEFAULT NULL COMMENT '退款交易流水号',
  `package_end_time` int(11) DEFAULT NULL COMMENT '套餐到期时间',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员订单表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_member_package`
--

CREATE TABLE `cm_member_package` (
  `id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL COMMENT '套餐名称',
  `describe` varchar(255) DEFAULT NULL COMMENT '套餐描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否上架:1-上架,0-下架',
  `sort` int(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `is_recommend` int(11) NOT NULL DEFAULT '0' COMMENT '是否推荐:1-是,0-否',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NOT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员套餐主表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_member_package_apply`
--

CREATE TABLE `cm_member_package_apply` (
  `id` int(11) NOT NULL,
  `package_id` int(11) NOT NULL COMMENT '套餐',
  `type` tinyint(1) NOT NULL COMMENT '类型:1-对话,2-向量,3-绘画,4-音乐',
  `channel` varchar(255) CHARACTER SET utf8 NOT NULL COMMENT '渠道',
  `status` tinyint(1) NOT NULL COMMENT '会员免费状态:1-开启,0-关闭',
  `day_limit` int(11) NOT NULL COMMENT '每天使用上限',
  `create_time` int(11) NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员套餐关联应用表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_member_package_comment`
--

CREATE TABLE `cm_member_package_comment` (
  `id` int(11) NOT NULL,
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '评价类型：1-虚拟评价；2-用户自评；',
  `member_package_id` int(11) NOT NULL COMMENT '会员套餐ID',
  `image` varchar(255) NOT NULL COMMENT '头像',
  `name` varchar(255) NOT NULL COMMENT '用户昵称',
  `comment_content` text NOT NULL COMMENT '评价内容',
  `comment_level` tinyint(1) NOT NULL COMMENT '评价等级：1-5星',
  `status` tinyint(1) NOT NULL COMMENT '状态：1-显示；0-隐藏；',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员套餐评价' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_member_package_price`
--

CREATE TABLE `cm_member_package_price` (
  `id` int(11) NOT NULL,
  `package_id` int(11) NOT NULL COMMENT '套餐id',
  `duration` int(5) UNSIGNED NOT NULL COMMENT '套餐时长',
  `duration_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '套餐时长类型:1-月,2-日,3-永久',
  `sell_price` decimal(10,2) UNSIGNED NOT NULL COMMENT '销售价格',
  `lineation_price` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT '划线价',
  `give_balance` decimal(15,7) DEFAULT NULL COMMENT '赠送电力值',
  `give_robot` int(11) DEFAULT NULL COMMENT '赠送机器人',
  `is_give` tinyint(1) NOT NULL DEFAULT '1' COMMENT '额外赠送：1-是,0-否',
  `is_recommend` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐:1-是,0-否',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:1-开启,0-关闭',
  `tags` varchar(255) DEFAULT NULL COMMENT '标签',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` int(11) NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员套餐价格表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_models`
--

CREATE TABLE `cm_models` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '模型类型: [1=对话模型, 2=向量模型]',
  `channel` varchar(30) NOT NULL DEFAULT '' COMMENT '模型渠道',
  `logo` varchar(300) NOT NULL DEFAULT '' COMMENT '模型图标',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '模型名称',
  `remarks` varchar(800) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '模型描述',
  `configs` text COMMENT '模型配置',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序编号',
  `is_system` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '系统内置: [0=否, 1=是]',
  `is_enable` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否启用: [0=否, 1=是]',
  `is_default` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否默认: [0=否, 1=是]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型管理表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_models_cost`
--

CREATE TABLE `cm_models_cost` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `model_id` int(10) NOT NULL COMMENT '模型ID',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '模型类型: [1=对话模型, 2=向量模型]',
  `channel` varchar(100) NOT NULL DEFAULT '' COMMENT '模型渠道',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '模型名称',
  `alias` varchar(100) NOT NULL DEFAULT '' COMMENT '模型别名',
  `price` decimal(10,4) UNSIGNED NOT NULL DEFAULT '0.0000' COMMENT '消费价格',
  `sort` int(10) UNSIGNED NOT NULL COMMENT '排序编号',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否启用: [0=否, 1=是]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型计费表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_music_record`
--

CREATE TABLE `cm_music_record` (
  `id` int(11) UNSIGNED NOT NULL COMMENT 'ID',
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `task_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '任务id',
  `clips_id` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '片段id',
  `style_id` varchar(100) NOT NULL DEFAULT '0' COMMENT '风格id',
  `tags` varchar(200) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '风格',
  `channel` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '模型渠道',
  `model` varchar(128) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '音乐模型',
  `title` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '标题',
  `prompt` text COMMENT '提示词',
  `custom_mode` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '自定义模式 0-不是 1-是',
  `make_instrumental` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否纯音乐 0-不是 1-是',
  `video_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '视频地址',
  `audio_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '音频地址',
  `image_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '音乐封面',
  `image_large_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '音乐封面大图',
  `lyric` text COMMENT '歌词',
  `duration` int(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '时长(秒)',
  `mv` varchar(100) NOT NULL DEFAULT 'chirp-v3-0' COMMENT '版本',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '0-待生成 1-生成中 2-生成成功 3-生成失败',
  `tokens` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '消耗金额',
  `fail_reason` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '失败原因',
  `ip` varchar(255) NOT NULL COMMENT '访客ip地址',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音乐记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_music_records_collect`
--

CREATE TABLE `cm_music_records_collect` (
  `id` int(11) NOT NULL,
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `square_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '广场ID',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='音乐记录收藏';

-- --------------------------------------------------------

--
-- 表的结构 `cm_music_square`
--

CREATE TABLE `cm_music_square` (
  `id` int(11) UNSIGNED NOT NULL COMMENT 'ID',
  `operate_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `source` tinyint(1) NOT NULL COMMENT '来源：1-官方；2-用户；',
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '分类id',
  `title` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '标题',
  `audio_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '音频地址',
  `image_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '音乐封面',
  `image_large_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '音乐封面大图',
  `lyric` text COMMENT '歌词',
  `duration` int(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '时长(秒)',
  `records_id` int(11) NOT NULL DEFAULT '0' COMMENT '分享来源ID',
  `verify_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态：0-待审核；1-审核通过；2-审核不通过；',
  `verify_result` varchar(255) DEFAULT NULL COMMENT '审核结果',
  `is_show` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否显示：1-是；0-否；',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音乐广场';

-- --------------------------------------------------------

--
-- 表的结构 `cm_music_style`
--

CREATE TABLE `cm_music_style` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `name` varchar(64) NOT NULL DEFAULT '' COMMENT '名称',
  `value` varchar(64) NOT NULL DEFAULT '' COMMENT '英文名称',
  `image` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '图标',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `status` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音乐风格表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_music_task`
--

CREATE TABLE `cm_music_task` (
  `id` int(11) NOT NULL COMMENT 'ID',
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `task_id` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '任务id',
  `style_id` varchar(100) NOT NULL DEFAULT '' COMMENT '风格id',
  `tags` varchar(200) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '风格',
  `channel` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '模型渠道',
  `model` varchar(128) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '音乐模型',
  `title` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '标题',
  `prompt` text COMMENT '提示词',
  `custom_mode` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '自定义模式 0-不是 1-是',
  `make_instrumental` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否纯音乐 0-不是 1-是',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '0-待生成 1-生成中 2-生成成功 3-生成失败',
  `tokens` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '消耗金额',
  `fail_reason` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '失败原因',
  `response` text CHARACTER SET utf8 COMMENT '响应数据',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音乐任务表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_notice_record`
--

CREATE TABLE `cm_notice_record` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '接收用户ID',
  `send_uid` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '发送者的ID',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `scene_id` int(10) UNSIGNED DEFAULT '0' COMMENT '场景',
  `read` tinyint(1) DEFAULT '0' COMMENT '已读状态;0-未读,1-已读',
  `recipient` tinyint(1) DEFAULT '0' COMMENT '通知接收对象类型;1-会员;2-商家;3-平台;4-游客(未注册用户)',
  `send_type` tinyint(1) DEFAULT '0' COMMENT '通知发送类型 1-系统通知 2-短信通知 3-微信模板 4-微信小程序',
  `notice_type` tinyint(1) DEFAULT NULL COMMENT '通知类型 1-业务通知 2-验证码 3-知识库反馈 4-绘画审核 5-音乐审核 6-视频审核',
  `notice_sub_type` tinyint(1) DEFAULT NULL COMMENT '通知子类型 1-审核成功 2-审核失败',
  `extra` varchar(255) DEFAULT '' COMMENT '其他',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_notice_setting`
--

CREATE TABLE `cm_notice_setting` (
  `id` int(10) NOT NULL,
  `scene_id` int(10) NOT NULL COMMENT '场景id',
  `scene_name` varchar(255) NOT NULL DEFAULT '' COMMENT '场景名称',
  `scene_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '场景描述',
  `recipient` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '接收者: [1=用户, 2=平台]',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '通知类型: 1-业务通知 2-验证码',
  `system_notice` text COMMENT '系统通知设置',
  `sms_notice` text COMMENT '短信通知设置',
  `oa_notice` text COMMENT '公众号通知设置',
  `mnp_notice` text COMMENT '小程序通知设置',
  `email_notice` text COMMENT '邮件通通知设置',
  `support` char(10) NOT NULL DEFAULT '' COMMENT '支持的发送类型 1-系统通知 2-短信通知 3-微信模板消息 4-小程序提醒',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知设置表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_official_account_reply`
--

CREATE TABLE `cm_official_account_reply` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(64) NOT NULL DEFAULT '' COMMENT '规则名称',
  `keyword` varchar(64) NOT NULL DEFAULT '' COMMENT '关键词',
  `reply_type` tinyint(1) NOT NULL COMMENT '回复类型 1-关注回复 2-关键字回复 3-默认回复',
  `matching_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '匹配方式：1-全匹配；2-模糊匹配',
  `content_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '内容类型：1-文本；2-图文；3-超链接；',
  `content` text NOT NULL COMMENT '回复内容',
  `content_image` varchar(255) NOT NULL DEFAULT '' COMMENT '回复图片',
  `content_image_media_id` varchar(255) NOT NULL DEFAULT '' COMMENT '微信素材media_id',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '启动状态：1-启动；0-关闭',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '50' COMMENT '排序',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公众号回复';

-- --------------------------------------------------------

--
-- 表的结构 `cm_operation_log`
--

CREATE TABLE `cm_operation_log` (
  `id` int(10) NOT NULL,
  `admin_id` int(10) NOT NULL COMMENT '管理员ID',
  `admin_name` varchar(16) NOT NULL DEFAULT '' COMMENT '管理员名称',
  `account` varchar(16) NOT NULL DEFAULT '' COMMENT '管理员账号',
  `action` varchar(64) DEFAULT '' COMMENT '操作名称',
  `type` varchar(8) NOT NULL COMMENT '请求方式',
  `url` varchar(255) NOT NULL COMMENT '访问链接',
  `params` text COMMENT '请求数据',
  `result` text COMMENT '请求结果',
  `ip` varchar(15) NOT NULL DEFAULT '' COMMENT 'ip地址',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_ppt_record`
--

CREATE TABLE `cm_ppt_record` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '类型: [1=基础,2=增强,3=深入]',
  `prompt` varchar(800) NOT NULL DEFAULT '' COMMENT '生成描述',
  `title` varchar(800) NOT NULL DEFAULT '' COMMENT 'ppt标题',
  `page_count` int(4) UNSIGNED NOT NULL DEFAULT '0' COMMENT '页面数量',
  `channel` varchar(100) NOT NULL DEFAULT '' COMMENT '通道: chatppt',
  `cover_id` varchar(100) DEFAULT '' COMMENT '模版ID',
  `catalog` text COMMENT '大纲',
  `price` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '消耗费用',
  `task_id` varchar(100) NOT NULL DEFAULT '' COMMENT '任务ID',
  `file_url` varchar(200) NOT NULL DEFAULT '' COMMENT '文件地址',
  `preview` text COMMENT '预览图',
  `progress` int(4) UNSIGNED NOT NULL DEFAULT '0' COMMENT '生成进度',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '状态 0-待生成 1-生成中 2-生成成功 3-生成失败 ',
  `fail_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '失败原因',
  `pay_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '支付状态 0-未支付 1-已支付',
  `response` text COMMENT '响应数据',
  `ip` varchar(32) NOT NULL DEFAULT '' COMMENT '来源IP',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 表的结构 `cm_rechange_card_code_log`
--

CREATE TABLE `cm_rechange_card_code_log` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `package_id` int(11) NOT NULL DEFAULT '0' COMMENT '套餐',
  `package_snap` text COMMENT '套餐快照',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡密兑换记录' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_recharge_order`
--

CREATE TABLE `cm_recharge_order` (
  `id` int(10) NOT NULL COMMENT 'ID',
  `order_sn` varchar(64) NOT NULL DEFAULT '' COMMENT '订单编号',
  `user_id` int(10) NOT NULL COMMENT '用户ID',
  `package_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '套餐ID',
  `pay_sn` varchar(255) NOT NULL DEFAULT '' COMMENT '支付编号-冗余字段，针对微信同一主体不同客户端支付需用不同订单号预留。',
  `pay_way` tinyint(2) NOT NULL DEFAULT '2' COMMENT '支付方式: [2=微信支付, 3=支付宝支付]',
  `pay_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付状态: [0=待支付, 1=已支付]',
  `refund_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '退款状态: [0=未退款, 1=已退款]',
  `order_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `order_terminal` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '终端平台',
  `transaction_id` varchar(128) NOT NULL DEFAULT '' COMMENT '第三方平台交易流水号',
  `chat_balance` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '充值的对话数量',
  `robot_number` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '充值的机器人数量',
  `video_duration` int(10) NOT NULL DEFAULT '0' COMMENT '充值的视频合成时长',
  `snapshot` text COMMENT '订单快照',
  `pay_time` int(10) DEFAULT NULL COMMENT '支付时间',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值订单表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_recharge_package`
--

CREATE TABLE `cm_recharge_package` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `name` varchar(250) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '套餐名称',
  `remarks` varchar(800) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '套餐描述',
  `tags` varchar(100) NOT NULL DEFAULT '' COMMENT '套餐标签',
  `sell_price` decimal(8,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '销售价格',
  `line_price` decimal(8,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '划线价格',
  `chat_balance` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '对话的余额',
  `robot_number` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人数量',
  `video_duration` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '视频合成时长',
  `give_chat_balance` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '赠送问答的数量',
  `give_robot_number` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '赠送机器人数量',
  `give_video_duration` int(10) NOT NULL DEFAULT '0' COMMENT '赠送视频合成时长',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序编号',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '套餐状态: [0=关闭, 1=开启]',
  `is_give` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '赠送状态: [0=关闭, 1=开启]',
  `is_recommend` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否推荐: [0=否, 1=是]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值套餐表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_refund_log`
--

CREATE TABLE `cm_refund_log` (
  `id` int(10) NOT NULL COMMENT 'id',
  `sn` varchar(32) DEFAULT NULL COMMENT '编号',
  `record_id` int(10) NOT NULL COMMENT '退款记录id',
  `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联用户',
  `handle_id` int(10) NOT NULL DEFAULT '0' COMMENT '处理人id（管理员id）',
  `order_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '订单总的应付款金额，冗余字段',
  `refund_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '本次退款金额',
  `refund_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '退款状态，0退款中，1退款成功，2退款失败',
  `refund_msg` text COMMENT '退款信息',
  `create_time` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款日志表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_refund_record`
--

CREATE TABLE `cm_refund_record` (
  `id` int(10) NOT NULL COMMENT 'id',
  `sn` varchar(32) NOT NULL DEFAULT '' COMMENT '退款编号',
  `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联用户',
  `order_id` int(10) NOT NULL DEFAULT '0' COMMENT '来源订单id',
  `order_sn` varchar(32) NOT NULL COMMENT '来源单号',
  `order_type` varchar(255) DEFAULT 'order' COMMENT '订单来源 order-商品订单 recharge-充值订单',
  `order_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '订单总的应付款金额，冗余字段',
  `refund_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '本次退款金额',
  `transaction_id` varchar(255) DEFAULT NULL COMMENT '第三方平台交易流水号',
  `refund_way` tinyint(1) NOT NULL DEFAULT '1' COMMENT '退款方式 1-线上退款 2-线下退款',
  `refund_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '退款类型 1-后台退款',
  `refund_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '退款状态，0退款中，1退款成功，2退款失败',
  `create_time` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_role_example`
--

CREATE TABLE `cm_role_example` (
  `id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属类别ID（关联cm_example_category表）',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '示例标题',
  `content` text NOT NULL COMMENT '角色设定内容',
  `description` text COMMENT '示例描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能体角色示例表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_sensitive_word`
--

CREATE TABLE `cm_sensitive_word` (
  `id` int(11) NOT NULL,
  `word` varchar(255) NOT NULL DEFAULT '' COMMENT '敏感词',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '状态值: [1=开启, 0=关闭]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词汇表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_skill`
--

CREATE TABLE `cm_skill` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT '名称',
  `image` varchar(64) NOT NULL COMMENT '图标',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `category_id` int(11) NOT NULL COMMENT '类别id',
  `status` int(11) NOT NULL COMMENT '状态：1-开启，0-关闭',
  `content` text CHARACTER SET utf8 NOT NULL COMMENT '主题内容',
  `tips` text NOT NULL COMMENT '提示内容',
  `describe` text COMMENT '描述',
  `max_tokens` int(5) UNSIGNED NOT NULL DEFAULT '150' COMMENT '回复最大长度',
  `temperature` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.6' COMMENT '词汇属性',
  `context_num` int(5) UNSIGNED NOT NULL DEFAULT '2' COMMENT '上下文总数',
  `top_p` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.9' COMMENT '随机属性',
  `presence_penalty` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.5' COMMENT '话题属性',
  `frequency_penalty` decimal(2,1) UNSIGNED NOT NULL DEFAULT '0.5' COMMENT '重复属性',
  `n` int(5) UNSIGNED NOT NULL DEFAULT '1' COMMENT '最大回复',
  `system` text COMMENT '全局指令',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_skill_category`
--

CREATE TABLE `cm_skill_category` (
  `id` int(11) NOT NULL,
  `name` varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '类目名称',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `status` int(11) NOT NULL COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能类别' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_sms_log`
--

CREATE TABLE `cm_sms_log` (
  `id` int(10) NOT NULL COMMENT 'id',
  `scene_id` int(10) NOT NULL COMMENT '场景id',
  `mobile` varchar(20) NOT NULL COMMENT '手机号码',
  `content` varchar(255) NOT NULL COMMENT '发送内容',
  `code` varchar(32) DEFAULT NULL COMMENT '发送关键字（注册、找回密码）',
  `is_verify` tinyint(1) DEFAULT '0' COMMENT '是否已验证；0-否；1-是',
  `check_num` int(5) DEFAULT '0' COMMENT '验证次数',
  `send_status` tinyint(1) NOT NULL COMMENT '发送状态：0-发送中；1-发送成功；2-发送失败',
  `send_time` int(10) NOT NULL COMMENT '发送时间',
  `results` text COMMENT '短信结果',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_square_category`
--

CREATE TABLE `cm_square_category` (
  `id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL COMMENT '分类名称',
  `sort` int(11) UNSIGNED DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) UNSIGNED NOT NULL COMMENT '状态：1-开启，0-关闭',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '类型：1-绘画,2-音乐，3-视频',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='绘画广场分类表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_system_menu`
--

CREATE TABLE `cm_system_menu` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '上级菜单',
  `type` char(2) NOT NULL DEFAULT '' COMMENT '权限类型: M=目录，C=菜单，A=按钮',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '菜单名称',
  `icon` varchar(100) NOT NULL DEFAULT '' COMMENT '菜单图标',
  `sort` smallint(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '菜单排序',
  `perms` varchar(100) NOT NULL DEFAULT '' COMMENT '权限标识',
  `paths` varchar(100) NOT NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(200) NOT NULL DEFAULT '' COMMENT '前端组件',
  `selected` varchar(200) NOT NULL DEFAULT '' COMMENT '选中路径',
  `params` varchar(200) NOT NULL DEFAULT '' COMMENT '路由参数',
  `is_cache` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否缓存: 0=否, 1=是',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否显示: 0=否, 1=是',
  `is_disable` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否禁用: 0=否, 1=是',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统菜单表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_system_role`
--

CREATE TABLE `cm_system_role` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(16) NOT NULL DEFAULT '' COMMENT '名称',
  `desc` varchar(128) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '描述',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色管理表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_system_role_menu`
--

CREATE TABLE `cm_system_role_menu` (
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色ID',
  `menu_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '菜单ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_task_invite`
--

CREATE TABLE `cm_task_invite` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '邀请人id',
  `new_user_id` int(11) DEFAULT NULL COMMENT '新用户ID',
  `task_share_id` int(11) DEFAULT NULL COMMENT '分享链接ID',
  `balance` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '奖励电力值',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_task_share`
--

CREATE TABLE `cm_task_share` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `channel` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分享渠道: [1-微信小程序 2-微信公众号 3-手机H5 4-电脑PC 5-苹果APP 6-安卓APP]',
  `click_num` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '点击量',
  `balance` decimal(15,7) DEFAULT '0.0000000',
  `invite_num` int(10) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享记录表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_task_sign`
--

CREATE TABLE `cm_task_sign` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `channel` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '签到渠道: [1-微信小程序 2-微信公众号 3-手机H5 4-电脑PC 5-苹果APP 6-安卓APP]',
  `balance` decimal(15,7) DEFAULT NULL COMMENT '电力值',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='签到记录列表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_template`
--

CREATE TABLE `cm_template` (
  `id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属类别ID（关联cm_example_category表）',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '模板名称',
  `description` text COMMENT '模板描述',
  `download_url` varchar(500) NOT NULL DEFAULT '' COMMENT '下载链接地址',
  `file_size` varchar(50) DEFAULT '' COMMENT '文件大小',
  `file_type` varchar(50) DEFAULT '' COMMENT '文件类型',
  `download_count` int(11) DEFAULT '0' COMMENT '下载次数',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板库表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_user`
--

CREATE TABLE `cm_user` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `group_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '分组',
  `sn` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '编号',
  `avatar` varchar(200) NOT NULL DEFAULT '' COMMENT '头像',
  `real_name` varchar(32) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `nickname` varchar(32) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `is_distribution` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否分销商: [1-是, 0-否]',
  `account` varchar(32) NOT NULL DEFAULT '' COMMENT '用户账号',
  `password` varchar(32) NOT NULL DEFAULT '' COMMENT '用户密码',
  `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '用户电话',
  `email` varchar(100) NOT NULL DEFAULT '' COMMENT '用户邮箱',
  `sex` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户性别: [1=男, 2=女]',
  `channel` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '注册渠道: [1=微信小程序, 2=微信公众号, 3=手机H5, 4=电脑PC, 5=苹果APP, 6=安卓APP]',
  `balance` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '钱包余额',
  `robot_num` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人数',
  `video_num` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '视频时长',
  `total_chat` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '累计对话',
  `total_space` int(10) NOT NULL DEFAULT '-1' COMMENT '总的空间: -1不限制',
  `is_disable` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否禁用: [0=否, 1=是]',
  `is_blacklist` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否加入黑名单: [1=是, 0=否]',
  `is_new_user` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否是新注册用户: [1-是, 0-否]',
  `multipoint_login` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '多处登录: [0=否, 1=是]',
  `cancelled_remark` varchar(500) NOT NULL DEFAULT '' COMMENT '注销原因',
  `login_ip` varchar(30) NOT NULL DEFAULT '' COMMENT '最后登录IP',
  `login_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '最后登录时间',
  `user_money` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '可提现佣金',
  `total_user_money` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '累计总佣金',
  `distribution_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '分销状态：1-正常；0-冻结',
  `distribution_time` int(10) DEFAULT NULL COMMENT '成为分销商的时间',
  `first_leader` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '第一个上级',
  `inviter_id` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人id',
  `second_leader` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '第二个上级',
  `total_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户管理表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_user_account_log`
--

CREATE TABLE `cm_user_account_log` (
  `id` int(11) UNSIGNED NOT NULL,
  `sn` varchar(32) NOT NULL DEFAULT '' COMMENT '流水号',
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `admin_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '管理ID',
  `action` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '操作动作: [1=增加, 2=减少]',
  `change_object` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '变动对象: [1=钱包, 2=机器人, 3=数字人]',
  `change_type` smallint(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '变动类型',
  `change_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '变动的数量',
  `left_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '变动后数量',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人的ID',
  `robot_name` varchar(200) NOT NULL COMMENT '机器人名称',
  `source_sn` varchar(200) NOT NULL DEFAULT '' COMMENT '关联的单号',
  `remark` varchar(300) NOT NULL DEFAULT '' COMMENT '备注的信息',
  `extend` varchar(500) NOT NULL DEFAULT '' COMMENT '扩展的信息',
  `extra` text COMMENT '预留的字段',
  `flows` text COMMENT 'token信息',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账户流水表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_user_auth`
--

CREATE TABLE `cm_user_auth` (
  `id` int(10) NOT NULL,
  `user_id` int(10) NOT NULL COMMENT '用户id',
  `openid` varchar(128) NOT NULL COMMENT '微信openid',
  `unionid` varchar(128) NOT NULL DEFAULT '' COMMENT '微信unionid',
  `terminal` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '客户端类型: [1=微信小程序, 2=微信公众号, 3=手机H5, 4=电脑PC, 5=苹果APP, 6=安卓APP]',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户授权表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_user_group`
--

CREATE TABLE `cm_user_group` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '名称',
  `sort` smallint(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户分组表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_user_member`
--

CREATE TABLE `cm_user_member` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `package_name` varchar(255) NOT NULL COMMENT '套餐名称',
  `member_end_time` bigint(20) DEFAULT NULL COMMENT '到期时间',
  `is_perpetual` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否永久套餐：1-是，0-否',
  `package_id` int(11) NOT NULL COMMENT '套餐id',
  `package_price_id` int(11) NOT NULL COMMENT '套餐价格id',
  `package_info` text COMMENT '套餐信息',
  `continue_time` int(10) DEFAULT NULL COMMENT '继会员时间',
  `is_clear` int(11) NOT NULL DEFAULT '0' COMMENT '是否标记清除:1-是,0-否',
  `clear_time` int(11) DEFAULT NULL COMMENT '标记清除时间',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员开通记录表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `cm_user_session`
--

CREATE TABLE `cm_user_session` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) NOT NULL COMMENT '用户ID',
  `terminal` tinyint(1) NOT NULL DEFAULT '1' COMMENT '客户端类型: [1=微信小程序, 2=微信公众号, 3=手机H5, 4=电脑PC, 5=苹果APP, 6=安卓APP]',
  `token` varchar(32) NOT NULL DEFAULT '' COMMENT '令牌的值',
  `expire_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '到期时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户会话表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_video_record`
--

CREATE TABLE `cm_video_record` (
  `id` int(11) UNSIGNED NOT NULL COMMENT 'ID',
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `task_id` varchar(100) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '任务id',
  `style_id` varchar(100) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '风格id',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '1-文生视频 2-图生视频',
  `tags` varchar(200) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '风格',
  `channel` varchar(64) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '模型渠道',
  `model` varchar(128) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '视频模型',
  `prompt` varchar(1000) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '提示词',
  `prompt_en` varchar(1000) NOT NULL DEFAULT '' COMMENT '英文提示词',
  `image` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '垫图地址',
  `video_url` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '视频地址',
  `api_version` varchar(100) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '版本',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '0-待生成 1-生成中 2-生成成功 3-生成失败',
  `tokens` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '消耗金额',
  `scale` varchar(20) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '图片比例',
  `fail_reason` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '失败原因',
  `response` text CHARACTER SET utf8mb4 COMMENT '响应数据',
  `ip` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '访客ip地址',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 表的结构 `cm_video_records_collect`
--

CREATE TABLE `cm_video_records_collect` (
  `id` int(11) NOT NULL,
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `square_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '广场ID',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视频记录收藏表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_video_square`
--

CREATE TABLE `cm_video_square` (
  `id` int(11) UNSIGNED NOT NULL COMMENT 'ID',
  `operate_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `source` tinyint(1) NOT NULL COMMENT '来源：1-官方；2-用户；',
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '分类id',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '1-文生视频 2-图生视频',
  `records_id` int(11) NOT NULL DEFAULT '0' COMMENT '分享来源ID',
  `prompt` varchar(1000) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '提示词',
  `image` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '垫图地址',
  `video_url` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '视频地址',
  `verify_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态：0-待审核；1-审核通过；2-审核不通过；',
  `verify_result` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '审核结果',
  `is_show` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否显示：1-是；0-否；',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视频广场';

-- --------------------------------------------------------

--
-- 表的结构 `cm_video_style`
--

CREATE TABLE `cm_video_style` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `name` varchar(64) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '名称',
  `value` varchar(64) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '英文名称',
  `image` varchar(64) CHARACTER SET utf8mb4 NOT NULL COMMENT '图标',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `status` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视频风格表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_visitor`
--

CREATE TABLE `cm_visitor` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `ip` varchar(255) NOT NULL COMMENT '访客ip地址',
  `terminal` tinyint(1) NOT NULL COMMENT '访问终端',
  `visit` int(11) NOT NULL COMMENT '浏览量',
  `create_time` int(10) DEFAULT NULL COMMENT '访问时间',
  `update_time` int(10) DEFAULT NULL,
  `delete_time` int(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问日志表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_withdraw_apply`
--

CREATE TABLE `cm_withdraw_apply` (
  `id` int(11) UNSIGNED NOT NULL,
  `sn` varchar(25) NOT NULL COMMENT '提现单号',
  `batch_no` varchar(25) DEFAULT NULL COMMENT '商家批次单号',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '提现方式：1-支付宝；2-微信零钱；3-微信收款码；4-支付宝收款码；',
  `money` decimal(10,2) UNSIGNED NOT NULL COMMENT '提现金额',
  `left_money` decimal(10,2) UNSIGNED NOT NULL COMMENT '到账金额(扣除手续费)',
  `handling_fee` decimal(10,2) UNSIGNED NOT NULL COMMENT '手续费',
  `real_name` varchar(255) DEFAULT NULL COMMENT '真实姓名',
  `account` varchar(32) DEFAULT '' COMMENT '提现账号',
  `money_qr_code` varchar(128) DEFAULT '' COMMENT '收款二维码',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '提现状态：1-待审核；2-提现中；3-提现成功；4-提现失败；',
  `verify_status` tinyint(1) DEFAULT '1' COMMENT '审核状态：1-待审核；2-审核通过；3-审核拒绝；',
  `verify_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `verify_time` int(10) DEFAULT NULL COMMENT '审核时间',
  `transfer_remark` varchar(255) DEFAULT NULL COMMENT '转账备注',
  `transfer_result` text COMMENT '转账结果',
  `query_result` text COMMENT '查询结果',
  `finish_time` int(10) DEFAULT NULL COMMENT '完成时间',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表';

-- --------------------------------------------------------

--
-- 表的结构 `cm_works_share_log`
--

CREATE TABLE `cm_works_share_log` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `work_id` int(10) UNSIGNED NOT NULL COMMENT '作品ID',
  `balance` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '金额',
  `channel` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分享渠道: [1-微信小程序 2-微信公众号 3-手机H5 4-电脑PC 5-苹果APP 6-安卓APP]',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '类型：1-绘画，2-音乐，3-视频',
  `square_id` int(10) NOT NULL COMMENT '广场id',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 转储表的索引
--

--
-- 表的索引 `cm_admin`
--
ALTER TABLE `cm_admin`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_admin_dept`
--
ALTER TABLE `cm_admin_dept`
  ADD PRIMARY KEY (`admin_id`,`dept_id`) USING BTREE;

--
-- 表的索引 `cm_admin_jobs`
--
ALTER TABLE `cm_admin_jobs`
  ADD PRIMARY KEY (`admin_id`,`jobs_id`) USING BTREE;

--
-- 表的索引 `cm_admin_role`
--
ALTER TABLE `cm_admin_role`
  ADD PRIMARY KEY (`admin_id`,`role_id`) USING BTREE;

--
-- 表的索引 `cm_admin_session`
--
ALTER TABLE `cm_admin_session`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `admin_id_client` (`admin_id`,`terminal`) USING BTREE COMMENT '一个用户在一个终端只有一个token',
  ADD UNIQUE KEY `token` (`token`) USING BTREE COMMENT 'token是唯一的';

--
-- 表的索引 `cm_ai_search_record`
--
ALTER TABLE `cm_ai_search_record`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_article`
--
ALTER TABLE `cm_article`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_article_cate`
--
ALTER TABLE `cm_article_cate`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_article_collect`
--
ALTER TABLE `cm_article_collect`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_card_code`
--
ALTER TABLE `cm_card_code`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `sn` (`sn`) USING BTREE;

--
-- 表的索引 `cm_card_code_record`
--
ALTER TABLE `cm_card_code_record`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `sn` (`sn`) USING BTREE;

--
-- 表的索引 `cm_chat_category`
--
ALTER TABLE `cm_chat_category`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_chat_record`
--
ALTER TABLE `cm_chat_record`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `category_id` (`category_id`) USING BTREE COMMENT '分类索引';

--
-- 表的索引 `cm_chat_record_category`
--
ALTER TABLE `cm_chat_record_category`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_chat_record_collect`
--
ALTER TABLE `cm_chat_record_collect`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_chat_sample`
--
ALTER TABLE `cm_chat_sample`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_config`
--
ALTER TABLE `cm_config`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_creation_category`
--
ALTER TABLE `cm_creation_category`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_creation_model`
--
ALTER TABLE `cm_creation_model`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_creation_model_collect`
--
ALTER TABLE `cm_creation_model_collect`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_decorate_page`
--
ALTER TABLE `cm_decorate_page`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_dept`
--
ALTER TABLE `cm_dept`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_dev_crontab`
--
ALTER TABLE `cm_dev_crontab`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_dev_pay_config`
--
ALTER TABLE `cm_dev_pay_config`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_dev_pay_way`
--
ALTER TABLE `cm_dev_pay_way`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_dict_data`
--
ALTER TABLE `cm_dict_data`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_dict_type`
--
ALTER TABLE `cm_dict_type`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_distribution_apply`
--
ALTER TABLE `cm_distribution_apply`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_distribution_order`
--
ALTER TABLE `cm_distribution_order`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_draw_lora`
--
ALTER TABLE `cm_draw_lora`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_draw_model`
--
ALTER TABLE `cm_draw_model`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_draw_model_category`
--
ALTER TABLE `cm_draw_model_category`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_draw_model_lora_relation`
--
ALTER TABLE `cm_draw_model_lora_relation`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_draw_prompt`
--
ALTER TABLE `cm_draw_prompt`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_draw_prompt_category`
--
ALTER TABLE `cm_draw_prompt_category`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_draw_prompt_example`
--
ALTER TABLE `cm_draw_prompt_example`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_draw_records`
--
ALTER TABLE `cm_draw_records`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `task_id` (`task_id`) USING BTREE COMMENT '任务索引';

--
-- 表的索引 `cm_draw_records_collect`
--
ALTER TABLE `cm_draw_records_collect`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `cm_draw_square`
--
ALTER TABLE `cm_draw_square`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `cm_draw_task`
--
ALTER TABLE `cm_draw_task`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `task_id` (`task_id`) USING BTREE;

--
-- 表的索引 `cm_email_log`
--
ALTER TABLE `cm_email_log`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_example_category`
--
ALTER TABLE `cm_example_category`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_example_content`
--
ALTER TABLE `cm_example_content`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `idx_category_id` (`category_id`) USING BTREE;

--
-- 表的索引 `cm_file`
--
ALTER TABLE `cm_file`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_file_cate`
--
ALTER TABLE `cm_file_cate`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_generate_column`
--
ALTER TABLE `cm_generate_column`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_generate_table`
--
ALTER TABLE `cm_generate_table`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_jobs`
--
ALTER TABLE `cm_jobs`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_digital`
--
ALTER TABLE `cm_kb_digital`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_know`
--
ALTER TABLE `cm_kb_know`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_know_files`
--
ALTER TABLE `cm_kb_know_files`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `know_idx` (`know_id`) USING BTREE COMMENT '知识库索引';

--
-- 表的索引 `cm_kb_know_qa`
--
ALTER TABLE `cm_kb_know_qa`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `kb_idx` (`kb_id`) USING BTREE COMMENT '知识库索引',
  ADD KEY `fd_idx` (`fd_id`) USING BTREE COMMENT '文件的索引',
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引';

--
-- 表的索引 `cm_kb_know_team`
--
ALTER TABLE `cm_kb_know_team`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_robot`
--
ALTER TABLE `cm_kb_robot`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_robot_category`
--
ALTER TABLE `cm_kb_robot_category`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_robot_instruct`
--
ALTER TABLE `cm_kb_robot_instruct`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_robot_publish`
--
ALTER TABLE `cm_kb_robot_publish`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `robot_idx` (`robot_id`) USING BTREE COMMENT '机器人索引';

--
-- 表的索引 `cm_kb_robot_record`
--
ALTER TABLE `cm_kb_robot_record`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `robot_idx` (`robot_id`) USING BTREE COMMENT '机器人索引',
  ADD KEY `share_idx` (`share_id`) USING BTREE COMMENT '分享编号索引',
  ADD KEY `identity_idx` (`share_identity`) USING BTREE COMMENT '分享身份索引';

--
-- 表的索引 `cm_kb_robot_revenue_config`
--
ALTER TABLE `cm_kb_robot_revenue_config`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_robot_revenue_log`
--
ALTER TABLE `cm_kb_robot_revenue_log`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `idx_user_id` (`user_id`) USING BTREE,
  ADD KEY `idx_sharer_id` (`sharer_id`) USING BTREE,
  ADD KEY `idx_robot_id` (`robot_id`) USING BTREE,
  ADD KEY `idx_square_id` (`square_id`) USING BTREE,
  ADD KEY `idx_settle_status` (`settle_status`) USING BTREE,
  ADD KEY `idx_create_time` (`create_time`) USING BTREE;

--
-- 表的索引 `cm_kb_robot_session`
--
ALTER TABLE `cm_kb_robot_session`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `robot_idx` (`robot_id`) USING BTREE COMMENT '机器人索引';

--
-- 表的索引 `cm_kb_robot_share_log`
--
ALTER TABLE `cm_kb_robot_share_log`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_robot_square`
--
ALTER TABLE `cm_kb_robot_square`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_kb_robot_visitor`
--
ALTER TABLE `cm_kb_robot_visitor`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `robot_idx` (`robot_id`) USING BTREE COMMENT '机器人索引',
  ADD KEY `ip` (`ip`) USING BTREE COMMENT 'IP索引';

--
-- 表的索引 `cm_key_pool`
--
ALTER TABLE `cm_key_pool`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_key_rule`
--
ALTER TABLE `cm_key_rule`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `cm_member_adjust_log`
--
ALTER TABLE `cm_member_adjust_log`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_member_benefits`
--
ALTER TABLE `cm_member_benefits`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_member_order`
--
ALTER TABLE `cm_member_order`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_member_package`
--
ALTER TABLE `cm_member_package`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_member_package_apply`
--
ALTER TABLE `cm_member_package_apply`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_member_package_comment`
--
ALTER TABLE `cm_member_package_comment`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_member_package_price`
--
ALTER TABLE `cm_member_package_price`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_models`
--
ALTER TABLE `cm_models`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_models_cost`
--
ALTER TABLE `cm_models_cost`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_music_record`
--
ALTER TABLE `cm_music_record`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `category_id` (`style_id`) USING BTREE COMMENT '分类索引';

--
-- 表的索引 `cm_music_records_collect`
--
ALTER TABLE `cm_music_records_collect`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `cm_music_square`
--
ALTER TABLE `cm_music_square`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_music_style`
--
ALTER TABLE `cm_music_style`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_music_task`
--
ALTER TABLE `cm_music_task`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `category_id` (`style_id`) USING BTREE COMMENT '分类索引';

--
-- 表的索引 `cm_notice_record`
--
ALTER TABLE `cm_notice_record`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_notice_setting`
--
ALTER TABLE `cm_notice_setting`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_official_account_reply`
--
ALTER TABLE `cm_official_account_reply`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_operation_log`
--
ALTER TABLE `cm_operation_log`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_ppt_record`
--
ALTER TABLE `cm_ppt_record`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_rechange_card_code_log`
--
ALTER TABLE `cm_rechange_card_code_log`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `cm_recharge_order`
--
ALTER TABLE `cm_recharge_order`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_recharge_package`
--
ALTER TABLE `cm_recharge_package`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_refund_log`
--
ALTER TABLE `cm_refund_log`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_refund_record`
--
ALTER TABLE `cm_refund_record`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_role_example`
--
ALTER TABLE `cm_role_example`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `idx_category_id` (`category_id`) USING BTREE,
  ADD KEY `idx_status` (`status`) USING BTREE;

--
-- 表的索引 `cm_sensitive_word`
--
ALTER TABLE `cm_sensitive_word`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_skill`
--
ALTER TABLE `cm_skill`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_skill_category`
--
ALTER TABLE `cm_skill_category`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_sms_log`
--
ALTER TABLE `cm_sms_log`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_square_category`
--
ALTER TABLE `cm_square_category`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `cm_system_menu`
--
ALTER TABLE `cm_system_menu`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_system_role`
--
ALTER TABLE `cm_system_role`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_system_role_menu`
--
ALTER TABLE `cm_system_role_menu`
  ADD PRIMARY KEY (`role_id`,`menu_id`) USING BTREE;

--
-- 表的索引 `cm_task_invite`
--
ALTER TABLE `cm_task_invite`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_task_share`
--
ALTER TABLE `cm_task_share`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_task_sign`
--
ALTER TABLE `cm_task_sign`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `cm_template`
--
ALTER TABLE `cm_template`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `idx_category_id` (`category_id`) USING BTREE,
  ADD KEY `idx_status` (`status`) USING BTREE;

--
-- 表的索引 `cm_user`
--
ALTER TABLE `cm_user`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `sn` (`sn`) USING BTREE COMMENT '编号唯一',
  ADD UNIQUE KEY `account` (`account`) USING BTREE COMMENT '账号唯一';

--
-- 表的索引 `cm_user_account_log`
--
ALTER TABLE `cm_user_account_log`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_user_auth`
--
ALTER TABLE `cm_user_auth`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `openid` (`openid`) USING BTREE;

--
-- 表的索引 `cm_user_group`
--
ALTER TABLE `cm_user_group`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_user_member`
--
ALTER TABLE `cm_user_member`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_user_session`
--
ALTER TABLE `cm_user_session`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `token` (`token`) USING BTREE COMMENT 'token是唯一的';

--
-- 表的索引 `cm_video_record`
--
ALTER TABLE `cm_video_record`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `category_id` (`style_id`) USING BTREE COMMENT '分类索引';

--
-- 表的索引 `cm_video_records_collect`
--
ALTER TABLE `cm_video_records_collect`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `cm_video_square`
--
ALTER TABLE `cm_video_square`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`operate_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `category_id` (`category_id`) USING BTREE COMMENT '分类索引';

--
-- 表的索引 `cm_video_style`
--
ALTER TABLE `cm_video_style`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_visitor`
--
ALTER TABLE `cm_visitor`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_withdraw_apply`
--
ALTER TABLE `cm_withdraw_apply`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `cm_works_share_log`
--
ALTER TABLE `cm_works_share_log`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `cm_admin`
--
ALTER TABLE `cm_admin`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_admin_session`
--
ALTER TABLE `cm_admin_session`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_ai_search_record`
--
ALTER TABLE `cm_ai_search_record`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_article`
--
ALTER TABLE `cm_article`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '文章id';

--
-- 使用表AUTO_INCREMENT `cm_article_cate`
--
ALTER TABLE `cm_article_cate`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '文章分类id';

--
-- 使用表AUTO_INCREMENT `cm_article_collect`
--
ALTER TABLE `cm_article_collect`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_card_code`
--
ALTER TABLE `cm_card_code`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_card_code_record`
--
ALTER TABLE `cm_card_code_record`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_chat_category`
--
ALTER TABLE `cm_chat_category`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_chat_record`
--
ALTER TABLE `cm_chat_record`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_chat_record_category`
--
ALTER TABLE `cm_chat_record_category`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_chat_record_collect`
--
ALTER TABLE `cm_chat_record_collect`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_chat_sample`
--
ALTER TABLE `cm_chat_sample`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_config`
--
ALTER TABLE `cm_config`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_creation_category`
--
ALTER TABLE `cm_creation_category`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_creation_model`
--
ALTER TABLE `cm_creation_model`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_creation_model_collect`
--
ALTER TABLE `cm_creation_model_collect`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_decorate_page`
--
ALTER TABLE `cm_decorate_page`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_dept`
--
ALTER TABLE `cm_dept`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_dev_crontab`
--
ALTER TABLE `cm_dev_crontab`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_dev_pay_config`
--
ALTER TABLE `cm_dev_pay_config`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_dev_pay_way`
--
ALTER TABLE `cm_dev_pay_way`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_dict_data`
--
ALTER TABLE `cm_dict_data`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_dict_type`
--
ALTER TABLE `cm_dict_type`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_distribution_apply`
--
ALTER TABLE `cm_distribution_apply`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_distribution_order`
--
ALTER TABLE `cm_distribution_order`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_draw_lora`
--
ALTER TABLE `cm_draw_lora`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_draw_model`
--
ALTER TABLE `cm_draw_model`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_draw_model_category`
--
ALTER TABLE `cm_draw_model_category`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_draw_model_lora_relation`
--
ALTER TABLE `cm_draw_model_lora_relation`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_draw_prompt`
--
ALTER TABLE `cm_draw_prompt`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_draw_prompt_category`
--
ALTER TABLE `cm_draw_prompt_category`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_draw_prompt_example`
--
ALTER TABLE `cm_draw_prompt_example`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_draw_records`
--
ALTER TABLE `cm_draw_records`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_draw_records_collect`
--
ALTER TABLE `cm_draw_records_collect`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_draw_square`
--
ALTER TABLE `cm_draw_square`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_draw_task`
--
ALTER TABLE `cm_draw_task`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id';

--
-- 使用表AUTO_INCREMENT `cm_email_log`
--
ALTER TABLE `cm_email_log`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_example_category`
--
ALTER TABLE `cm_example_category`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_example_content`
--
ALTER TABLE `cm_example_content`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_file`
--
ALTER TABLE `cm_file`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `cm_file_cate`
--
ALTER TABLE `cm_file_cate`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `cm_generate_column`
--
ALTER TABLE `cm_generate_column`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_generate_table`
--
ALTER TABLE `cm_generate_table`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_jobs`
--
ALTER TABLE `cm_jobs`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_kb_digital`
--
ALTER TABLE `cm_kb_digital`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_know`
--
ALTER TABLE `cm_kb_know`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_know_files`
--
ALTER TABLE `cm_kb_know_files`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_know_qa`
--
ALTER TABLE `cm_kb_know_qa`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_know_team`
--
ALTER TABLE `cm_kb_know_team`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot`
--
ALTER TABLE `cm_kb_robot`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_category`
--
ALTER TABLE `cm_kb_robot_category`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_instruct`
--
ALTER TABLE `cm_kb_robot_instruct`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_publish`
--
ALTER TABLE `cm_kb_robot_publish`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_record`
--
ALTER TABLE `cm_kb_robot_record`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_revenue_config`
--
ALTER TABLE `cm_kb_robot_revenue_config`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_revenue_log`
--
ALTER TABLE `cm_kb_robot_revenue_log`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_session`
--
ALTER TABLE `cm_kb_robot_session`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_share_log`
--
ALTER TABLE `cm_kb_robot_share_log`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_square`
--
ALTER TABLE `cm_kb_robot_square`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_visitor`
--
ALTER TABLE `cm_kb_robot_visitor`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_key_pool`
--
ALTER TABLE `cm_key_pool`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_key_rule`
--
ALTER TABLE `cm_key_rule`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `cm_member_adjust_log`
--
ALTER TABLE `cm_member_adjust_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_member_benefits`
--
ALTER TABLE `cm_member_benefits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_member_order`
--
ALTER TABLE `cm_member_order`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_member_package`
--
ALTER TABLE `cm_member_package`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_member_package_apply`
--
ALTER TABLE `cm_member_package_apply`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_member_package_comment`
--
ALTER TABLE `cm_member_package_comment`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_member_package_price`
--
ALTER TABLE `cm_member_package_price`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_models`
--
ALTER TABLE `cm_models`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_models_cost`
--
ALTER TABLE `cm_models_cost`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `cm_music_record`
--
ALTER TABLE `cm_music_record`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_music_records_collect`
--
ALTER TABLE `cm_music_records_collect`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_music_square`
--
ALTER TABLE `cm_music_square`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_music_style`
--
ALTER TABLE `cm_music_style`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_music_task`
--
ALTER TABLE `cm_music_task`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_notice_record`
--
ALTER TABLE `cm_notice_record`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_notice_setting`
--
ALTER TABLE `cm_notice_setting`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_official_account_reply`
--
ALTER TABLE `cm_official_account_reply`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_operation_log`
--
ALTER TABLE `cm_operation_log`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_ppt_record`
--
ALTER TABLE `cm_ppt_record`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_rechange_card_code_log`
--
ALTER TABLE `cm_rechange_card_code_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_recharge_order`
--
ALTER TABLE `cm_recharge_order`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_recharge_package`
--
ALTER TABLE `cm_recharge_package`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_refund_log`
--
ALTER TABLE `cm_refund_log`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_refund_record`
--
ALTER TABLE `cm_refund_record`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_role_example`
--
ALTER TABLE `cm_role_example`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_sensitive_word`
--
ALTER TABLE `cm_sensitive_word`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_skill`
--
ALTER TABLE `cm_skill`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_skill_category`
--
ALTER TABLE `cm_skill_category`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_sms_log`
--
ALTER TABLE `cm_sms_log`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id';

--
-- 使用表AUTO_INCREMENT `cm_square_category`
--
ALTER TABLE `cm_square_category`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_system_menu`
--
ALTER TABLE `cm_system_menu`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_system_role`
--
ALTER TABLE `cm_system_role`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_task_invite`
--
ALTER TABLE `cm_task_invite`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_task_share`
--
ALTER TABLE `cm_task_share`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_task_sign`
--
ALTER TABLE `cm_task_sign`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_template`
--
ALTER TABLE `cm_template`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_user`
--
ALTER TABLE `cm_user`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_user_account_log`
--
ALTER TABLE `cm_user_account_log`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_user_auth`
--
ALTER TABLE `cm_user_auth`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_user_group`
--
ALTER TABLE `cm_user_group`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_user_member`
--
ALTER TABLE `cm_user_member`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_user_session`
--
ALTER TABLE `cm_user_session`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_video_record`
--
ALTER TABLE `cm_video_record`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_video_records_collect`
--
ALTER TABLE `cm_video_records_collect`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_video_square`
--
ALTER TABLE `cm_video_square`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_video_style`
--
ALTER TABLE `cm_video_style`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `cm_visitor`
--
ALTER TABLE `cm_visitor`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `cm_withdraw_apply`
--
ALTER TABLE `cm_withdraw_apply`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `cm_works_share_log`
--
ALTER TABLE `cm_works_share_log`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
