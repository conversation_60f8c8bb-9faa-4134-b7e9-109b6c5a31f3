<template>
    <page-meta :page-style="$theme.pageStyle">
        <!-- #ifndef H5 -->
        <navigation-bar
            :front-color="$theme.navColor"
            :background-color="$theme.navBgColor"
        />
        <!-- #endif -->
    </page-meta>
    <view class="flex-1 min-h-0">
        <z-paging-swiper :fixed="false" :swiper-style="{ height: '100%' }">
            <!-- 需要固定在顶部不滚动的view放在slot="top"的view中 -->
            <!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
            <template #top>
                <u-tabs
                    :is-scroll="false"
                    ref="tabs"
                    :active-color="$theme.primaryColor"
                    :list="tabState.list"
                    :current="tabState.current"
                    @change="tabsChange"
                />
            </template>
            <!-- swiper必须设置height:100%，因为swiper有默认的高度，只有设置高度100%才可以铺满页面  -->
            <swiper
                class="h-full"
                :current="tabState.current"
                @change="swiperChange"
            >
                <swiper-item
                    class="swiper-item h-full"
                    v-for="(item, index) in tabState.list"
                    :key="index"
                >
                    <Robot
                        ref="tabsItemRef"
                        v-if="item.type == 'robot'"
                        :tab-index="index"
                        :current-index="tabState.current"
                    />
                    <Kb
                        ref="tabsItemRef"
                        v-if="item.type == 'kb'"
                        :tab-index="index"
                        :current-index="tabState.current"
                    />
                    <Digital
                        ref="tabsItemRef"
                        v-if="item.type == 'digital'"
                        :tab-index="index"
                        :current-index="tabState.current"
                    />
                </swiper-item>
            </swiper>
        </z-paging-swiper>
    </view>
    <tabbar />
</template>

<script setup lang="ts">
import { reactive, shallowRef, watch, nextTick, ref } from 'vue'
import { useRoute, useRouter } from 'uniapp-router-next'
import Robot from './components/robot/index.vue'
import Kb from './components/kb/index.vue'
import Digital from './components/digital/index.vue'
import { onLoad, onShow, onReady } from '@dcloudio/uni-app'

const route = useRoute()
const router = useRouter()

const tabState = reactive({
    list: [
        {
            name: '智能体应用',
            type: 'robot'
        },
        {
            name: '知识库',
            type: 'kb'
        },
        {
            name: '虚拟形象',
            type: 'digital'
        }
    ],
    current: 0
})

const tabsItemRef = shallowRef()
const isInitialized = ref(false)

// 根据URL参数设置默认页签
const getInitialTab = () => {
    let tabParam = route.query.tab
    
    // #ifdef H5
    // H5环境下也尝试从浏览器URL获取参数
    if (!tabParam && typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search)
        tabParam = urlParams.get('tab')
    }
    // #endif
    
    console.log('getInitialTab - tabParam:', tabParam)
    if (tabParam) {
        const tabIndex = tabState.list.findIndex(item => item.type === tabParam)
        console.log('getInitialTab - tabIndex:', tabIndex)
        return tabIndex >= 0 ? tabIndex : 0
    }
    return 0
}

// 更新URL参数的函数
const updateURL = (tabType: string) => {
    console.log('updateURL - tabType:', tabType)
    
    try {
        // #ifdef H5
        // H5环境下直接操作浏览器URL
        if (typeof window !== 'undefined' && window.history) {
            const url = new URL(window.location.href)
            url.searchParams.set('tab', tabType)
            window.history.replaceState({}, '', url.toString())
            console.log('updateURL - H5 URL updated:', url.toString())
        }
        // #endif
        
        // #ifndef H5
        // 非H5环境使用router
        router.replace({
            path: route.path,
            query: {
                ...route.query,
                tab: tabType
            }
        })
        // #endif
    } catch (error) {
        console.error('updateURL error:', error)
    }
}

// 页签切换处理
const tabsChange = (index: number) => {
    console.log('tabsChange - index:', index)
    tabState.current = index
    const currentTab = tabState.list[index]
    
    if (isInitialized.value) {
        updateURL(currentTab.type)
    }
}

// swiper切换处理
const swiperChange = (e: any) => {
    const index = e.detail.current
    console.log('swiperChange - index:', index)
    tabState.current = index
    const currentTab = tabState.list[index]
    
    if (isInitialized.value) {
        updateURL(currentTab.type)
    }
}

// 初始化页签状态
const initializeTab = () => {
    console.log('initializeTab - route.query:', route.query)
    const initialTab = getInitialTab()
    tabState.current = initialTab
    
    // 如果URL中没有tab参数，设置默认的tab参数
    if (!route.query.tab) {
        const currentTab = tabState.list[initialTab]
        console.log('initializeTab - setting default tab:', currentTab.type)
        updateURL(currentTab.type)
    }
    
    isInitialized.value = true
    console.log('initializeTab - completed, current:', tabState.current)
}

// 监听路由变化，同步页签状态
watch(() => route.query.tab, (newTab) => {
    console.log('watch route.query.tab - newTab:', newTab, 'isInitialized:', isInitialized.value)
    if (newTab && isInitialized.value) {
        const tabIndex = tabState.list.findIndex(item => item.type === newTab)
        if (tabIndex >= 0 && tabIndex !== tabState.current) {
            console.log('watch - updating tabState.current to:', tabIndex)
            tabState.current = tabIndex
        }
    }
})

// 页面加载完成后初始化
onReady(() => {
    console.log('onReady - initializing')
    nextTick(() => {
        initializeTab()
    })
})

onShow(() => {
    setTimeout(() => {
        tabsItemRef.value?.forEach((item: any) => {
            item.reload?.()
        })
    })
})
</script>

<style>
page {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f4f8fd;
}
</style>
