<template>
    <page-meta :page-style="$theme.pageStyle">
        <!-- #ifndef H5 -->
        <navigation-bar
            :front-color="$theme.navColor"
            :background-color="$theme.navBgColor"
        />
        <!-- #endif -->
    </page-meta>
    <view class="h-full flex flex-col">
        <u-navbar
            :title="'智能体'"
            title-color="#333"
            :title-bold="true"
        >
            <template #right>
                <view class="flex items-center">
                    <!-- 保留原有的+图标 -->
                    <view class="w-[60rpx] h-[60rpx] bg-primary rounded-full flex items-center justify-center mr-[20rpx]" @click="addRobot">
                        <u-icon name="plus" color="#fff" size="24" />
                    </view>
                </view>
            </template>
        </u-navbar>
        
        <view class="flex-1 min-h-0 p-[20rpx]">
            <!-- 醒目的新增操作区域 -->
            <view class="mb-[30rpx]">
                <view class="bg-white rounded-[20rpx] p-[30rpx] shadow-sm">
                    <view class="text-center">
                        <view class="text-[32rpx] font-bold text-[#333] mb-[20rpx]">创建您的专属智能体</view>
                        <view class="text-[28rpx] text-[#666] mb-[40rpx]">让AI助手为您提供个性化服务</view>
                        
                        <view class="flex justify-center space-x-[40rpx]">
                            <!-- 快速创建 -->
                            <view class="flex flex-col items-center" @click="addRobot">
                                <view class="w-[120rpx] h-[120rpx] bg-gradient-to-br from-[#4A90E2] to-[#357ABD] rounded-[20rpx] flex items-center justify-center mb-[20rpx] shadow-lg">
                                    <u-icon name="plus-circle" color="#fff" size="40" />
                                </view>
                                <view class="text-[28rpx] font-medium text-[#333]">快速创建</view>
                                <view class="text-[24rpx] text-[#666] mt-[8rpx]">立即开始</view>
                            </view>
                            
                            <!-- 模板创建 -->
                            <view class="flex flex-col items-center" @click="createFromTemplate">
                                <view class="w-[120rpx] h-[120rpx] bg-gradient-to-br from-[#50C878] to-[#3A9B5C] rounded-[20rpx] flex items-center justify-center mb-[20rpx] shadow-lg">
                                    <u-icon name="file-text" color="#fff" size="40" />
                                </view>
                                <view class="text-[28rpx] font-medium text-[#333]">模板创建</view>
                                <view class="text-[24rpx] text-[#666] mt-[8rpx]">选择模板</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            
            <!-- 我的智能体列表 -->
            <view class="mb-[20rpx]">
                <view class="text-[32rpx] font-bold text-[#333] mb-[20rpx]">我的智能体</view>
            </view>
            
            // ... existing code ...
        </view>
    </view>
</template>

<script setup lang="ts">
// ... existing code ...

const addRobot = () => {
    // 现有的新增智能体逻辑
    uni.navigateTo({
        url: '/packages/pages/robot_info/robot_info'
    })
}

const createFromTemplate = () => {
    // 模板创建逻辑
    uni.showToast({
        title: '模板功能开发中',
        icon: 'none'
    })
}

// ... existing code ...
</script>

<style lang="scss" scoped>
.shadow-sm {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.bg-gradient-to-br {
    background: linear-gradient(135deg, var(--from-color), var(--to-color));
}

.space-x-\[40rpx\] > view:not(:last-child) {
    margin-right: 40rpx;
}

// ... existing code ...
</style> 