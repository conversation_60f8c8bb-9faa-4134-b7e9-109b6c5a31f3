-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- 主机： 172.20.0.2
-- 生成日期： 2025-06-04 14:06:11
-- 服务器版本： 5.7.29
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `chatmoney`
--

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_record`
--

CREATE TABLE `cm_kb_robot_record` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户的ID',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分类的ID',
  `square_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '广场的ID',
  `chat_model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '对话模型ID',
  `emb_model_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '向量模型ID',
  `ask` text COMMENT '提问',
  `reply` text COMMENT '答复',
  `reasoning` text COMMENT '思考过程',
  `files_plugin` longtext COMMENT '文件理解',
  `images` text COMMENT '附带图片',
  `video` text COMMENT '附带视频',
  `files` text COMMENT '附带文件',
  `quotes` text COMMENT '引用内容',
  `context` text COMMENT '上下文组',
  `correlation` text COMMENT '相关问题',
  `flows` text NOT NULL COMMENT 'tokens信息',
  `model` varchar(100) NOT NULL DEFAULT '' COMMENT '对话模型',
  `tokens` decimal(15,7) NOT NULL COMMENT '消耗金额',
  `is_revenue_shared` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否已分成：0-未分成 1-已分成',
  `revenue_log_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分成记录ID',
  `feedback` text COMMENT '用户反馈',
  `share_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分享的ID',
  `share_apikey` varchar(80) NOT NULL DEFAULT '' COMMENT '分享的密钥',
  `share_identity` varchar(60) NOT NULL DEFAULT '' COMMENT '分享的身份',
  `censor_status` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '审核状态: [0=未审核, 1=合规, 2=不合规, 3=疑似, 4=审核失败]',
  `censor_result` text COMMENT '审核结果',
  `censor_num` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '审核次数',
  `is_feedback` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否反馈: [0=否, 1=是]',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否显示: [0=否, 1=是]',
  `is_flow` tinyint(1) NOT NULL DEFAULT '0' COMMENT '使用工作流 0-未使用 1-已使用',
  `task_time` int(60) UNSIGNED NOT NULL DEFAULT '0' COMMENT '对话耗时',
  `unique_id` varchar(100) NOT NULL DEFAULT '' COMMENT '分享唯一ID',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人对话表';

--
-- 转存表中的数据 `cm_kb_robot_record`
--

INSERT INTO `cm_kb_robot_record` (`id`, `user_id`, `robot_id`, `category_id`, `square_id`, `chat_model_id`, `emb_model_id`, `ask`, `reply`, `reasoning`, `files_plugin`, `images`, `video`, `files`, `quotes`, `context`, `correlation`, `flows`, `model`, `tokens`, `is_revenue_shared`, `revenue_log_id`, `feedback`, `share_id`, `share_apikey`, `share_identity`, `censor_status`, `censor_result`, `censor_num`, `is_feedback`, `is_show`, `is_flow`, `task_time`, `unique_id`, `create_time`, `update_time`, `delete_time`) VALUES
(1, 1, 2, 2, 0, 3, 0, '333', '444', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 333\"},{\"role\":\"assistant\",\"content\":\"444\"}]', '[]', '[{\"name\":\"chat\",\"model\":\"generalv3\",\"total_price\":\"0\",\"prompt_tokens\":1,\"completion_tokens\":2,\"total_tokens\":3,\"str_length\":6}]', 'generalv3', '0.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748662035, 1748662035, NULL),
(6, 1, 3, 4, 0, 10, 0, '你是什么版本', '我并没有版本的说法哦。字节跳动的研发团队会持续对我进行优化和改进，让我能更好地为你提供各种服务和帮助呢。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"system\",\"content\":\"你是一位经验丰富的学习导师，擅长帮助学生理解复杂概念，制定学习计划，并提供学习方法指导。你的教学风格耐心细致，善于用简单易懂的方式解释复杂问题。\"},{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 你是什么版本\"},{\"role\":\"assistant\",\"content\":\"我并没有版本的说法哦。字节跳动的研发团队会持续对我进行优化和改进，让我能更好地为你提供各种服务和帮助呢。 \"}]', '[\"研发团队是如何对你进行优化的\",\"最近一次优化是什么时候\"]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"388\",\"prompt_tokens\":248,\"completion_tokens\":115,\"total_tokens\":363,\"str_length\":194}]', 'Doubao-lite-4k', '388.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748857576, 1748857576, NULL),
(11, 2, 3, 0, 3, 10, 0, '你是什么版本', '我并没有所谓“版本”的概念呢。字节跳动的研发团队会持续对我进行优化和改进，不断提升我的能力和表现，让我能更好地为你服务。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"system\",\"content\":\"你是一位经验丰富的学习导师，擅长帮助学生理解复杂概念，制定学习计划，并提供学习方法指导。你的教学风格耐心细致，善于用简单易懂的方式解释复杂问题。\"},{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 你是什么版本\"},{\"role\":\"assistant\",\"content\":\"我并没有所谓“版本”的概念呢。字节跳动的研发团队会持续对我进行优化和改进，不断提升我的能力和表现，让我能更好地为你服务。 \"}]', '[\"研发团队是通过什么方式对你进行优化的\",\"优化后能带来哪些明显的提升\"]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"404\",\"prompt_tokens\":248,\"completion_tokens\":130,\"total_tokens\":378,\"str_length\":202}]', 'Doubao-lite-4k', '404.0000000', 1, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748863804, 1748863804, NULL),
(12, 2, 3, 0, 3, 10, 0, '优化后能带来哪些明显的提升', '由于不清楚具体是什么进行了优化，我先从几个常见方面为你分析优化后可能带来的明显提升：\n\n### 产品性能方面\n- **运行速度提升**：比如软件优化代码后，能减少不必要的计算和资源占用，让程序启动和运行得更快。以电脑操作系统为例，经过优化清理磁盘碎片、优化内存管理后，系统响应速度变快，打开软件和文件的时间明显缩短。\n- **稳定性增强**：优化可以修复产品中的漏洞和潜在问题，降低系统崩溃、软件报错的概率。像手机应用优化后，在各种复杂环境下运行更稳定，很少出现闪退等情况。\n\n### 学习效果方面\n- **知识理解加深**：对学习方法或学习资料进行优化后，能让学生更清晰地理解复杂概念。例如将抽象的数学公式用生动形象的实例进行解释，学生就能更好地掌握其原理和应用。\n- **学习效率提高**：制定合理的学习计划并不断优化后，学生可以更合理地分配时间和精力，避免盲目学习。比如采用番茄工作法优化学习时间安排，能让学生在更短的时间内完成学习任务。\n\n### 业务流程方面\n- **工作效率提升**：企业对业务流程进行优化后，各环节之间的衔接更加顺畅，减少了不必要的等待和重复工作。比如通过引入自动化办公系统优化审批流程，原本需要几天的审批时间可能缩短至几个小时。\n- **成本降低**：优化业务流程可以减少资源浪费和人力成本。例如优化供应链管理流程，能降低库存积压，减少仓储成本和资金占用。\n\n如果你能明确指出具体是什么进行了优化，我可以给出更有针对性的回答。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"system\",\"content\":\"你是一位经验丰富的学习导师，擅长帮助学生理解复杂概念，制定学习计划，并提供学习方法指导。你的教学风格耐心细致，善于用简单易懂的方式解释复杂问题。\"},{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 优化后能带来哪些明显的提升\"},{\"role\":\"assistant\",\"content\":\"由于不清楚具体是什么进行了优化，我先从几个常见方面为你分析优化后可能带来的明显提升：\\n\\n### 产品性能方面\\n- **运行速度提升**：比如软件优化代码后，能减少不必要的计算和资源占用，让程序启动和运行得更快。以电脑操作系统为例，经过优化清理磁盘碎片、优化内存管理后，系统响应速度变快，打开软件和文件的时间明显缩短。\\n- **稳定性增强**：优化可以修复产品中的漏洞和潜在问题，降低系统崩溃、软件报错的概率。像手机应用优化后，在各种复杂环境下运行更稳定，很少出现闪退等情况。\\n\\n### 学习效果方面\\n- **知识理解加深**：对学习方法或学习资料进行优化后，能让学生更清晰地理解复杂概念。例如将抽象的数学公式用生动形象的实例进行解释，学生就能更好地掌握其原理和应用。\\n- **学习效率提高**：制定合理的学习计划并不断优化后，学生可以更合理地分配时间和精力，避免盲目学习。比如采用番茄工作法优化学习时间安排，能让学生在更短的时间内完成学习任务。\\n\\n### 业务流程方面\\n- **工作效率提升**：企业对业务流程进行优化后，各环节之间的衔接更加顺畅，减少了不必要的等待和重复工作。比如通过引入自动化办公系统优化审批流程，原本需要几天的审批时间可能缩短至几个小时。\\n- **成本降低**：优化业务流程可以减少资源浪费和人力成本。例如优化供应链管理流程，能降低库存积压，减少仓储成本和资金占用。\\n\\n如果你能明确指出具体是什么进行了优化，我可以给出更有针对性的回答。 \"}]', '[\"如何对产品性能进行优化\",\"怎样优化业务流程能降低成本\"]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"1556\",\"prompt_tokens\":265,\"completion_tokens\":1284,\"total_tokens\":1549,\"str_length\":778}]', 'Doubao-lite-4k', '1556.0000000', 1, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748863907, 1748863907, NULL),
(13, 2, 3, 0, 3, 10, 0, 'nishi', '不太明确你说的“nishi”具体是什么意思呢。你可以补充更多信息，比如它是某个单词、拼音想表达的内容，还是一个特定概念等，这样我就能更好地为你解答啦。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"system\",\"content\":\"你是一位经验丰富的学习导师，擅长帮助学生理解复杂概念，制定学习计划，并提供学习方法指导。你的教学风格耐心细致，善于用简单易懂的方式解释复杂问题。\"},{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: nishi\"},{\"role\":\"assistant\",\"content\":\"不太明确你说的“nishi”具体是什么意思呢。你可以补充更多信息，比如它是某个单词、拼音想表达的内容，还是一个特定概念等，这样我就能更好地为你解答啦。 \"}]', '[\"我想说的是‘你是’，该怎么用英语表达\",\"我想问的是‘你是’后面接不同内容的用法\"]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"432\",\"prompt_tokens\":239,\"completion_tokens\":148,\"total_tokens\":387,\"str_length\":216}]', 'Doubao-lite-4k', '432.0000000', 1, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748867474, 1748867474, NULL),
(14, 2, 3, 0, 3, 10, 0, '你是什么版本', '我并没有版本的概念呢。字节跳动的研发团队会持续对我进行优化和改进，让我能更好地为你服务、解答各类问题。如果你在学习上有任何需求，比如理解复杂概念、制定学习计划或者获取学习方法指导，都可以随时跟我说。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"system\",\"content\":\"你是一位经验丰富的学习导师，擅长帮助学生理解复杂概念，制定学习计划，并提供学习方法指导。你的教学风格耐心细致，善于用简单易懂的方式解释复杂问题。\"},{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 你是什么版本\"},{\"role\":\"assistant\",\"content\":\"我并没有版本的概念呢。字节跳动的研发团队会持续对我进行优化和改进，让我能更好地为你服务、解答各类问题。如果你在学习上有任何需求，比如理解复杂概念、制定学习计划或者获取学习方法指导，都可以随时跟我说。 \"}]', '[\"能举例说明研发团队对您做了哪些优化吗\",\"研发团队一般多久对您进行一次优化\"]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"482\",\"prompt_tokens\":248,\"completion_tokens\":214,\"total_tokens\":462,\"str_length\":241}]', 'Doubao-lite-4k', '482.0000000', 1, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748928857, 1748928857, NULL),
(15, 2, 3, 0, 3, 10, 0, '你是什么版本', '我没有传统意义上的版本号呢。字节跳动的研发团队会持续对我进行优化和改进，不断提升我的能力和性能，让我能更好地为你服务。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"system\",\"content\":\"你是一位经验丰富的学习导师，擅长帮助学生理解复杂概念，制定学习计划，并提供学习方法指导。你的教学风格耐心细致，善于用简单易懂的方式解释复杂问题。\"},{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 你是什么版本\"},{\"role\":\"assistant\",\"content\":\"我没有传统意义上的版本号呢。字节跳动的研发团队会持续对我进行优化和改进，不断提升我的能力和性能，让我能更好地为你服务。 \"}]', '[\"研发团队是通过什么方式对你进行优化的\",\"优化的周期大概是多久\"]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"402\",\"prompt_tokens\":248,\"completion_tokens\":126,\"total_tokens\":374,\"str_length\":201}]', 'Doubao-lite-4k', '402.0000000', 1, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748933371, 1748933371, NULL),
(16, 2, 3, 0, 3, 10, 0, 'nishi', '不太明确“nishi”具体是什么意思呢。你可以补充更多信息，比如这是某个单词的拼音、缩写，还是一个特定概念的表述，这样我就能更好地为你解答啦。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"system\",\"content\":\"你是一位经验丰富的学习导师，擅长帮助学生理解复杂概念，制定学习计划，并提供学习方法指导。你的教学风格耐心细致，善于用简单易懂的方式解释复杂问题。\"},{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: nishi\"},{\"role\":\"assistant\",\"content\":\"不太明确“nishi”具体是什么意思呢。你可以补充更多信息，比如这是某个单词的拼音、缩写，还是一个特定概念的表述，这样我就能更好地为你解答啦。 \"}]', '[\"nishi是指某个英文单词的发音吗\",\"我想解释一下nishi的含义\"]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"424\",\"prompt_tokens\":239,\"completion_tokens\":139,\"total_tokens\":378,\"str_length\":212}]', 'Doubao-lite-4k', '424.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748936540, 1748936540, NULL),
(17, 1, 4, 0, 5, 10, 0, 'nishishenme', '不太明确“nishishenme”具体想表达什么意思。如果是“你是什么”，但这样的表述不太自然，可能你想问“你是谁”，对应的英文是“Who are you?”；如果是其他意思，可以进一步解释说明，以便我更准确理解你的需求。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: nishishenme\"},{\"role\":\"assistant\",\"content\":\"不太明确“nishishenme”具体想表达什么意思。如果是“你是什么”，但这样的表述不太自然，可能你想问“你是谁”，对应的英文是“Who are you?”；如果是其他意思，可以进一步解释说明，以便我更准确理解你的需求。 \"}]', '[]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"372\",\"prompt_tokens\":88,\"completion_tokens\":192,\"total_tokens\":280,\"str_length\":186}]', 'Doubao-lite-4k', '372.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748938348, 1748938348, NULL),
(18, 1, 4, 0, 5, 10, 0, '2221111', '不太明确“2221111”具体所指的问题是什么。它可能是一个单纯的数字，比如一个编号、密码、金额等等。你可以详细说明一下关于“2221111”的具体问题，以便我更准确地为你解答。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 2221111\"},{\"role\":\"assistant\",\"content\":\"不太明确“2221111”具体所指的问题是什么。它可能是一个单纯的数字，比如一个编号、密码、金额等等。你可以详细说明一下关于“2221111”的具体问题，以便我更准确地为你解答。 \"}]', '[]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"320\",\"prompt_tokens\":85,\"completion_tokens\":160,\"total_tokens\":245,\"str_length\":160}]', 'Doubao-lite-4k', '320.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748940411, 1748940411, NULL),
(19, 1, 4, 0, 5, 10, 0, '23213213213', '不太明确你关于“23213213213”具体的问题是什么。它是一个整数，你可以进一步说明是要对它进行数学运算（比如加减乘除、取余等）、分析它的数字特征、将它转换为其他进制，还是有其他方面的需求呢？ ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 23213213213\"},{\"role\":\"assistant\",\"content\":\"不太明确你关于“23213213213”具体的问题是什么。它是一个整数，你可以进一步说明是要对它进行数学运算（比如加减乘除、取余等）、分析它的数字特征、将它转换为其他进制，还是有其他方面的需求呢？ \"}]', '[]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"346\",\"prompt_tokens\":87,\"completion_tokens\":179,\"total_tokens\":266,\"str_length\":173}]', 'Doubao-lite-4k', '346.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748941685, 1748941685, NULL),
(20, 1, 4, 0, 5, 10, 0, '444444', '不太明确你所说的“444444”具体是什么问题。你可以详细解释一下，比如是对这个数字进行数学运算、分析其含义、将其转换为其他形式等等，以便我能更准确地为你提供答案。', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 444444\"},{\"role\":\"assistant\",\"content\":\"不太明确你所说的“444444”具体是什么问题。你可以详细解释一下，比如是对这个数字进行数学运算、分析其含义、将其转换为其他形式等等，以便我能更准确地为你提供答案。\"}]', '[]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"304\",\"prompt_tokens\":85,\"completion_tokens\":164,\"total_tokens\":249,\"str_length\":152}]', 'Doubao-lite-4k', '304.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1748942253, 1748942253, NULL),
(21, 1, 4, 0, 5, 10, 0, '66666', '不太明确“66666”具体代表的含义呢。它可能是单纯表达一种惊叹、佩服的语气词，在网络用语中“666”常用来形容某人或某事很厉害、很牛；也可能是一个具体的数字。你可以给我更多关于“66666”的背景信息，以便我更准确地和你交流。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 66666\"},{\"role\":\"assistant\",\"content\":\"不太明确“66666”具体代表的含义呢。它可能是单纯表达一种惊叹、佩服的语气词，在网络用语中“666”常用来形容某人或某事很厉害、很牛；也可能是一个具体的数字。你可以给我更多关于“66666”的背景信息，以便我更准确地和你交流。 \"}]', '[]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"366\",\"prompt_tokens\":85,\"completion_tokens\":209,\"total_tokens\":294,\"str_length\":183}]', 'Doubao-lite-4k', '366.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1749014950, 1749014950, NULL),
(22, 1, 4, 0, 5, 10, 0, '55555', '不太明确你说的“55555”具体是什么意思。“55555”在网络用语中常用来表达哭泣、难过、悲伤等情绪，类似呜呜呜的谐音。你可以详细解释一下你的问题，这样我就能更好地为你解答啦。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 55555\"},{\"role\":\"assistant\",\"content\":\"不太明确你说的“55555”具体是什么意思。“55555”在网络用语中常用来表达哭泣、难过、悲伤等情绪，类似呜呜呜的谐音。你可以详细解释一下你的问题，这样我就能更好地为你解答啦。 \"}]', '[]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"316\",\"prompt_tokens\":85,\"completion_tokens\":169,\"total_tokens\":254,\"str_length\":158}]', 'Doubao-lite-4k', '316.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1749015202, 1749015202, NULL),
(23, 1, 4, 0, 5, 10, 0, '77777', '不太明确“77777”具体所指的问题是什么。它可能是一个单纯的数字，也可能在特定情境中有其他含义，比如是密码、编号、金额等等。你可以详细说明一下关于“77777”的问题，以便我更准确地为你解答。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 77777\"},{\"role\":\"assistant\",\"content\":\"不太明确“77777”具体所指的问题是什么。它可能是一个单纯的数字，也可能在特定情境中有其他含义，比如是密码、编号、金额等等。你可以详细说明一下关于“77777”的问题，以便我更准确地为你解答。 \"}]', '[]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"332\",\"prompt_tokens\":85,\"completion_tokens\":183,\"total_tokens\":268,\"str_length\":166}]', 'Doubao-lite-4k', '332.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1749015478, 1749015478, NULL),
(24, 1, 4, 0, 5, 10, 0, '33333', '不太明确“33333”具体代表的问题含义。你可以详细解释一下，比如它是一个数学计算相关的数字，还是在某个特定情境下有特殊意义的代码之类的，以便我更准确地为你解答。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 33333\"},{\"role\":\"assistant\",\"content\":\"不太明确“33333”具体代表的问题含义。你可以详细解释一下，比如它是一个数学计算相关的数字，还是在某个特定情境下有特殊意义的代码之类的，以便我更准确地为你解答。 \"}]', '[]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"300\",\"prompt_tokens\":85,\"completion_tokens\":159,\"total_tokens\":244,\"str_length\":150}]', 'Doubao-lite-4k', '300.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1749016728, 1749016728, NULL),
(25, 1, 4, 0, 5, 10, 0, '77777', '不太明确“77777”具体所指的问题是什么。它可能是一个单纯的数字，也可能在特定的情境中有特殊含义，比如是编号、密码、金额等等。你可以详细说明一下关于“77777”的问题，以便我能更好地为你解答。 ', '', '[]', '[]', '[]', '[]', '[]', '[{\"role\":\"user\",\"content\":\"\\n回答要求:\\n    - 使用 Markdown 语法优化回答格式。\\n    - 使用与问题相同的语言回答。\\n    \\n问题: 77777\"},{\"role\":\"assistant\",\"content\":\"不太明确“77777”具体所指的问题是什么。它可能是一个单纯的数字，也可能在特定的情境中有特殊含义，比如是编号、密码、金额等等。你可以详细说明一下关于“77777”的问题，以便我能更好地为你解答。 \"}]', '[]', '[{\"name\":\"chat\",\"model\":\"Doubao-lite-4k\",\"total_price\":\"334\",\"prompt_tokens\":85,\"completion_tokens\":184,\"total_tokens\":269,\"str_length\":167}]', 'Doubao-lite-4k', '334.0000000', 0, 0, NULL, 0, '', '', 0, NULL, 0, 0, 1, 0, 0, '', 1749016814, 1749016814, NULL);

--
-- 转储表的索引
--

--
-- 表的索引 `cm_kb_robot_record`
--
ALTER TABLE `cm_kb_robot_record`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE COMMENT '用户索引',
  ADD KEY `robot_idx` (`robot_id`) USING BTREE COMMENT '机器人索引',
  ADD KEY `share_idx` (`share_id`) USING BTREE COMMENT '分享编号索引',
  ADD KEY `identity_idx` (`share_identity`) USING BTREE COMMENT '分享身份索引';

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_record`
--
ALTER TABLE `cm_kb_robot_record`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID', AUTO_INCREMENT=26;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
