#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/jiti@1.21.7/node_modules/jiti/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/jiti@1.21.7/node_modules/jiti/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/jiti@1.21.7/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/jiti@1.21.7/node_modules/jiti/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/jiti@1.21.7/node_modules/jiti/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/jiti@1.21.7/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../jiti/bin/jiti.js" "$@"
else
  exec node  "$basedir/../../../jiti/bin/jiti.js" "$@"
fi
