<!DOCTYPE html>
<html lang="zh">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>lime-watermark</title>
	<style>
		html,body {
			margin: 0;
			padding: 0;
			pointer-events: none;
			/* background-color: rgba(255,0,0,0.1) */
		}
	</style>
</head>
<body>
	<script type="text/javascript" src="./uni.webview.1.5.3.js"></script>
	<script type="text/javascript" src="./watermark.min.js"></script>
	<script>
		
		var canvas = document.createElement('canvas')
		var ratio = window.devicePixelRatio || 1
		var outerWidth = 300
		var outerHeight = 150
		function appendWatermark(image) {
			const mark = document.createElement('canvas')
			var width = outerWidth * ratio
			var height = outerHeight * ratio
			mark.width = width
			mark.height = height
			document.body.append(mark)
			const ctx = mark.getContext('2d');
			ctx.scale(ratio, ratio)
			const pattern = ctx.createPattern(canvas, 'repeat');
			ctx.fillStyle = pattern;
			ctx.fillRect(0, 0, width, height);
			
			emit('appendWatermark', mark.toDataURL())
		}
		
		var watermark = new lime.Watermark(canvas, {
			appendWatermark
		})
		function render(props) {
			if(props.screenWidth) {
				outerWidth = props.screenWidth
			}
			if(props.screenHeight) {
				outerHeight = props.screenHeight
			}
			if(props.pixelRatio) {
				ratio = props.pixelRatio
			}
			if(watermark) {
				watermark.render(props)
			}
		}
		function emit(event, data) {
			postMessage({
				event,
				data
			});
		};
		function postMessage(data) {
			uni.postMessage({
				data
			});
		};
		// render({
		// 	content: ['Lime UI'],
		// 	// rotate: -22,
		// 	// baseSize: 2,
		// 	// fontGap: 3
		// })
	</script>
</body>
</html>