-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- 主机： 172.20.0.2
-- 生成日期： 2025-06-04 14:05:58
-- 服务器版本： 5.7.29
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `chatmoney`
--

-- --------------------------------------------------------

--
-- 表的结构 `cm_kb_robot_revenue_log`
--

CREATE TABLE `cm_kb_robot_revenue_log` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '使用者用户ID',
  `sharer_id` int(10) UNSIGNED NOT NULL COMMENT '分享者用户ID',
  `robot_id` int(10) UNSIGNED NOT NULL COMMENT '智能体ID',
  `square_id` int(10) UNSIGNED NOT NULL COMMENT '广场记录ID',
  `record_id` int(10) UNSIGNED NOT NULL COMMENT '对话记录ID',
  `total_cost` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '总消耗电力值',
  `share_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '分享者获得电力值',
  `platform_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '平台保留电力值',
  `share_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '分成比例(百分比)',
  `settle_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '结算状态：0-未结算 1-已结算',
  `settle_time` int(10) UNSIGNED DEFAULT NULL COMMENT '结算时间',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能体分成收益记录表';

--
-- 转存表中的数据 `cm_kb_robot_revenue_log`
--

INSERT INTO `cm_kb_robot_revenue_log` (`id`, `user_id`, `sharer_id`, `robot_id`, `square_id`, `record_id`, `total_cost`, `share_amount`, `platform_amount`, `share_ratio`, `settle_status`, `settle_time`, `create_time`, `update_time`) VALUES
(1, 1, 2, 1, 1, 1, '1.0000000', '0.3000000', '0.7000000', '30.00', 1, 1748930288, 1748930288, 1748930288),
(2, 2, 1, 1, 1, 2, '2.5000000', '0.7500000', '1.7500000', '30.00', 1, 1749014128, 1748926688, 1749014128);

--
-- 转储表的索引
--

--
-- 表的索引 `cm_kb_robot_revenue_log`
--
ALTER TABLE `cm_kb_robot_revenue_log`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `idx_user_id` (`user_id`) USING BTREE,
  ADD KEY `idx_sharer_id` (`sharer_id`) USING BTREE,
  ADD KEY `idx_robot_id` (`robot_id`) USING BTREE,
  ADD KEY `idx_square_id` (`square_id`) USING BTREE,
  ADD KEY `idx_settle_status` (`settle_status`) USING BTREE,
  ADD KEY `idx_create_time` (`create_time`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `cm_kb_robot_revenue_log`
--
ALTER TABLE `cm_kb_robot_revenue_log`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID', AUTO_INCREMENT=3;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
