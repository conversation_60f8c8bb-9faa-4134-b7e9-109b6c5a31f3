<template>
    <view class="web-page">
        <!-- 重要提示 -->
        <view class="important-notice mb-[20rpx]">
            <view class="notice-header">
                <text class="notice-icon">⚠️</text>
                <text class="notice-title">重要提示</text>
            </view>
            <view class="notice-content">
                为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。
            </view>
        </view>
        <view class="py-[20rpx]">
            <view class="flex-1">
                <!-- <el-input
                            v-model="url"
                            :placeholder="`请输入要解析的网页链接，添加多个请按回车键分隔`"
                            type="textarea"
                            resize="none"
                            :rows="6"
                        /> -->
                <u-input
                    v-model="url"
                    type="textarea"
                    :maxlength="-1"
                    border
                    :height="200"
                    :auto-height="false"
                    placeholder="请输入要解析的网页链接，添加多个请按回车键分隔"
                >
                </u-input>
            </view>
            <view class="inline-flex mt-[20rpx]">
                <u-button
                    type="primary"
                    :loading="isLock"
                    @click="parseUrl"
                    :customStyle="{
                        width: '180rpx'
                    }"
                >
                    解析
                </u-button>
            </view>
            <view>
                <view
                    v-for="(item, index) in formData"
                    :key="index"
                    class="mt-[20rpx]"
                >
                    <view class="mb-[10rpx] font-medium text-lg">
                        #{{ index + 1 }}
                        {{ item.name }}

                        <u-icon
                            class="text-muted ml-[20rpx]"
                            name="trash"
                            @click="handleDelete(item)"
                        />
                    </view>
                    <template v-for="(data, index) in item.data" :key="index">
                        <u-input
                            v-model="data.q"
                            type="textarea"
                            :maxlength="-1"
                            border
                            :height="300"
                            :auto-height="false"
                            placeholder="文件内容，空内容会自动省略"
                        >
                        </u-input>
                    </template>
                </view>
            </view>
        </view>
    </view>
</template>
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import type { IDataItem } from './hook'
import { webHtmlCapture } from '@/api/kb'
import { ref } from 'vue'
import { useLockFn } from '@/hooks/useLockFn'

const props = defineProps<{
    modelValue: IDataItem[]
}>()

const emit = defineEmits<{
    (event: 'update:modelValue', value: IDataItem): void
}>()
const formData = useVModel(props, 'modelValue', emit)
const url = ref('')

const handleDelete = async (item: IDataItem) => {
    const index = formData.value.indexOf(item)
    if (index !== -1) {
        formData.value.splice(index, 1)
    }
}

const { lockFn: parseUrl, isLock } = useLockFn(async () => {
    if (!url.value) return uni.$u.toast('请输入网页链接')
    const data = await webHtmlCapture({
        url: url.value.split('\n').filter(Boolean)
    })
    formData.value = [
        ...data.map((item: any) => ({
            data: [
                {
                    a: '',
                    q: item.content
                }
            ],
            path: '',
            name: item.url
        })),
        ...formData.value
    ]
    url.value = ''
})
</script>

<style scoped lang="scss">
.important-notice {
    background: linear-gradient(135deg, #fff8e6 0%, #fff3d6 100%);
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
    
    .notice-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .notice-icon {
            font-size: 18px;
            margin-right: 8px;
        }
        
        .notice-title {
            font-weight: 600;
            color: #e6a23c;
            font-size: 16px;
        }
    }
    
    .notice-content {
        color: #856404;
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
        padding-left: 26px;
    }
}
</style>
