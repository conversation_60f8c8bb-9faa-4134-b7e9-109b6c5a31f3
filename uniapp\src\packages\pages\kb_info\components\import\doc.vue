<template>
  <view>
    <!-- 模板库选择区域 -->
    <view class="mb-4 p-4 bg-gray-50 rounded-lg">
      <view class="flex items-center mb-3">
        <text class="font-medium">📄 模板库</text>
        <text class="ml-2 text-xs text-gray-500">选择模板快速下载导入文档</text>
      </view>
      
      <!-- #ifdef H5 -->
      <view class="grid grid-cols-2 gap-4">
        <view>
          <select 
            v-model="selectedCategoryId" 
            @change="handleCategoryChangeH5"
            class="w-full p-2 border border-gray-300 rounded"
          >
            <option value="">请选择模板类别</option>
            <option 
              v-for="(category, index) in templateCategories" 
              :key="category.id" 
              :value="index"
            >
              {{ category.name }}
            </option>
          </select>
        </view>
        <view>
          <select 
            v-model="selectedTemplateId" 
            @change="handleTemplateChangeH5"
            :disabled="!selectedCategoryId && selectedCategoryId !== ''"
            class="w-full p-2 border border-gray-300 rounded"
          >
            <option value="">请选择具体模板</option>
            <option 
              v-for="(template, index) in currentTemplates" 
              :key="template.id" 
              :value="index"
            >
              {{ template.name }} ({{ template.file_size }})
            </option>
          </select>
        </view>
      </view>
      <!-- #endif -->
      
      <!-- #ifndef H5 -->
      <view class="grid grid-cols-2 gap-4">
        <view>
          <u-picker 
            :show="showCategoryPicker" 
            :columns="[pickerCategories]" 
            @confirm="handleCategoryChange"
            @cancel="showCategoryPicker = false"
          ></u-picker>
          <view 
            class="p-2 border border-gray-300 rounded text-center"
            @click="showCategoryPicker = true"
          >
            {{ selectedCategoryName || '请选择模板类别' }}
          </view>
        </view>
        <view>
          <u-picker 
            :show="showTemplatePicker" 
            :columns="[pickerTemplates]" 
            @confirm="handleTemplateChange"
            @cancel="showTemplatePicker = false"
          ></u-picker>
          <view 
            class="p-2 border border-gray-300 rounded text-center"
            :class="{ 'text-gray-400': !selectedCategoryId }"
            @click="selectedCategoryId && (showTemplatePicker = true)"
          >
            {{ selectedTemplateName || '请选择具体模板' }}
          </view>
        </view>
      </view>
      <!-- #endif -->
      
      <view v-if="selectedTemplate" class="mt-3 p-3 bg-white rounded border">
        <view class="flex justify-between items-start">
          <view class="flex-1">
            <view class="font-medium text-sm">{{ selectedTemplate.name }}</view>
            <view class="text-xs text-gray-500 mt-1">{{ selectedTemplate.description }}</view>
            <view class="text-xs text-gray-400 mt-1">
              文件类型：{{ selectedTemplate.file_type }}
            </view>
          </view>
          <view 
            class="ml-2 px-3 py-1 bg-primary text-white rounded text-sm"
            @click="downloadTemplate"
          >
            {{ downloadLoading ? '下载中...' : '下载模板' }}
          </view>
        </view>
      </view>
    </view>

    <view class="mt-2">
      <app-file-picker @importFile="fileInput" :fileExtname="fileAccept">
        <view
          class="text-center py-[100rpx] rounded-lg border border-dashed border-info bg-[#FCFCFC] relative"
        >
          <view>选择文件</view>
          <view>支持 .txt ,.docx ,.pdf ,.md文件</view>
          <view
            v-if="isLock"
            class="absolute inset-0 flex justify-center items-center bg-[rgba(0,0,0,0.4)]"
          >
            <u-loading :size="50" :color="$theme.primaryColor" />
          </view>
        </view>
      </app-file-picker>
    </view>
    <template v-if="data.length > 0">
      <view class="mt-2">
        <view class="py-[20rpx] font-bold">文件（{{ data.length }}）</view>
        <view class="px-[10rpx] py-[15rpx]">
          <view
            v-for="(item, index) in data"
            :key="index"
            class="fileItem flex items-center p-2 rounded-lg mt-1 hover:cursor-pointer bg-page transition duration-300"
            :class="{
              'bg-page border border-solid border-primary rounded-lg':
                showIndex == index,
            }"
            @click="selectShow(index)"
          >
            <view class="ml-2">
              {{ item.name }}
            </view>
            <view class="ml-auto flex items-center" @click.stop="delFile(index)">
              <u-icon name="trash-fill" color="#2979ff" size="28"></u-icon>
            </view>
          </view>
        </view>
      </view>
      <view class="mt-2">
        <view class="py-[20rpx] font-bold">文件长度</view>
        <view
          class="px-[10rpx] py-[15rpx] border border-solid border-[#DCDFE6] rounded-lg"
        >
          <input @input="reSplit" v-model="step" />
        </view>
      </view>
      <!-- <view
                class="my-[40rpx] comsume py-[15rpx] text-center text-[#F7A40A]"
            >
                预计消耗：0.033积分
            </view> -->
      <view>
        <view class="py-[20rpx] font-bold"
          >结果预览（{{ data[showIndex]?.data.length }}组）</view
        >
        <view class="px-[10rpx] pb-[20rpx]">
          <view
            class="bg-page rounded p-[10px] mt-2"
            v-for="(item, index) in data[showIndex]?.data"
            :key="index"
          >
            <data-item
              :index="index"
              :name="data[showIndex].name"
              v-model:data="item.q"
              @delete="handleDelete(index)"
            />
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { type IDataItem, isSameFile } from "./hook";
import {
  readDocContent,
  readPdfContent,
  readTxtContent,
} from "@/utils/fileReader";
import { ref, reactive, computed, onMounted, nextTick, toRaw } from "vue";
import { useVModel } from "@vueuse/core";
import { useLockFn } from "@/hooks/useLockFn";
import { splitText2ChunksArray } from "@/utils/textSplitter";
import DataItem from "./data-item.vue";
import { getAllTemplates, downloadTemplate as downloadTemplateApi } from '@/api/kb'

const props = defineProps<{
  modelValue: IDataItem[];
}>();
const emit = defineEmits(["update:modelValue"]);
const data = useVModel(props, "modelValue", emit);

const fileList = ref<File[]>([]);
const fileAccept = [".txt", ".docx", ".pdf", ".md"];
const accept = fileAccept.join(", ");

//预览
const showIndex = ref(-1);

//分段长度
const step = ref(512);

// 模板库相关数据
const selectedCategoryId = ref('')
const selectedCategoryName = ref('')
const selectedTemplateId = ref('')
const selectedTemplateName = ref('')
const templateCategories = ref<any[]>([])
const allTemplates = ref<any[]>([])
const downloadLoading = ref(false)

// picker相关状态
const showCategoryPicker = ref(false)
const showTemplatePicker = ref(false)

// 创建纯净的普通数组，完全移除响应式代理
const createPlainArray = (data: any[]) => {
    const plainArray: any[] = []
    for (let i = 0; i < data.length; i++) {
        const item = data[i]
        plainArray.push({
            text: item.name,
            value: item.id
        })
    }
    return plainArray
}

const currentTemplates = computed(() => {
    if (!selectedCategoryId.value) return []
    const category = allTemplates.value.find((c: any) => c.id === selectedCategoryId.value)
    return category ? category.templates : []
})

const selectedTemplate = computed(() => {
    if (!selectedTemplateId.value) return null
    return currentTemplates.value.find((t: any) => t.id === selectedTemplateId.value)
})

// 为picker组件提供原生数组
const pickerCategories = computed(() => {
    const raw = toRaw(templateCategories.value)
    return createPlainArray(raw)
})

const pickerTemplates = computed(() => {
    const templates = currentTemplates.value
    return templates.map((template: any) => ({
        text: `${template.name} (${template.file_size})`,
        value: template.id
    }))
})

// 获取模板库数据
const fetchTemplates = async () => {
    try {
        const res = await getAllTemplates()
        allTemplates.value = res
        
        // 使用toRaw彻底移除响应式代理，然后创建普通数组
        const rawData = toRaw(res)
        const plainCategories = rawData.map((category: any) => ({
            id: category.id,
            name: category.name
        }))
        
        // 强制清空现有数据
        templateCategories.value = []
        await nextTick()
        
        // 重新赋值
        templateCategories.value = plainCategories
        
        // 强制更新一次界面
        await nextTick()
        
    } catch (error) {
        console.error('获取模板库数据失败:', error)
    }
}

// 处理类别选择变化（小程序端）
const handleCategoryChange = (e: any) => {
    if (!e.value || !e.value[0]) {
        return
    }
    
    const selectedValue = e.value[0]
    const category = templateCategories.value.find((c: any) => c.id === selectedValue)
    
    if (category) {
        selectedCategoryId.value = category.id
        selectedCategoryName.value = category.name
        // 重置模板选择
        selectedTemplateId.value = ''
        selectedTemplateName.value = ''
    }
    
    showCategoryPicker.value = false
}

// 处理模板选择变化（小程序端）
const handleTemplateChange = (e: any) => {
    if (!e.value || !e.value[0]) {
        return
    }
    
    const selectedValue = e.value[0]
    const template = currentTemplates.value.find((t: any) => t.id === selectedValue)
    
    if (template) {
        selectedTemplateId.value = template.id
        selectedTemplateName.value = template.name
    }
    
    showTemplatePicker.value = false
}

// H5端专用的类别选择处理函数
const handleCategoryChangeH5 = (e: any) => {
    const index = parseInt(e.target.value)
    if (isNaN(index) || index < 0 || index >= templateCategories.value.length) {
        // 如果选择了空值或无效索引，重置选择
        selectedCategoryId.value = ''
        selectedCategoryName.value = ''
        selectedTemplateId.value = ''
        selectedTemplateName.value = ''
        return
    }
    
    const category = templateCategories.value[index]
    
    if (category && category.id) {
        selectedCategoryId.value = category.id
        selectedCategoryName.value = category.name
        // 重置模板选择
        selectedTemplateId.value = ''
        selectedTemplateName.value = ''
    }
}

// H5端专用的模板选择处理函数
const handleTemplateChangeH5 = (e: any) => {
    const index = parseInt(e.target.value)
    if (isNaN(index) || index < 0 || index >= currentTemplates.value.length) {
        // 如果选择了空值或无效索引，重置选择
        selectedTemplateId.value = ''
        selectedTemplateName.value = ''
        return
    }
    
    const template = currentTemplates.value[index]
    
    if (template && template.id) {
        selectedTemplateId.value = template.id
        selectedTemplateName.value = template.name
    }
}

// 下载模板
const downloadTemplate = async () => {
    if (!selectedTemplate.value) return
    
    try {
        downloadLoading.value = true
        const res = await downloadTemplateApi({ id: selectedTemplate.value.id })
        
        // 在uniapp中使用uni.downloadFile下载文件
        uni.downloadFile({
            url: res.download_url,
            success: (downloadRes) => {
                if (downloadRes.statusCode === 200) {
                    uni.showToast({
                        title: '模板下载成功',
                        icon: 'success'
                    })
                    
                    // 保存文件到相册或文件管理器
                    uni.saveFile({
                        tempFilePath: downloadRes.tempFilePath,
                        success: (saveRes) => {
                            console.log('文件保存成功:', saveRes.savedFilePath)
                        },
                        fail: (err) => {
                            console.error('文件保存失败:', err)
                        }
                    })
                } else {
                    uni.showToast({
                        title: '下载失败',
                        icon: 'error'
                    })
                }
            },
            fail: (err) => {
                console.error('下载失败:', err)
                uni.showToast({
                    title: '下载失败',
                    icon: 'error'
                })
            }
        })
    } catch (error: any) {
        console.error('模板下载失败:', error)
        uni.showToast({
            title: '下载失败: ' + (error.message || '未知错误'),
            icon: 'error'
        })
    } finally {
        downloadLoading.value = false
    }
}

// 组件挂载时获取模板库数据
onMounted(() => {
    fetchTemplates()
})

const fileToData = async (file: any) => {
  try {
    // loading.value = true
    await isSameFile(file, fileList.value);
    const content = await parseFile(file);
    if (!content) {
      throw "解析结果为空，已自动忽略";
    }

    data.value.push({
      name: file.name,
      path: "",
      data: [],
    });
    //@ts-ignore
    file.data = content;

    fileList.value.push(file);
    selectShow(fileList.value.length - 1);
    reSplit();
  } catch (error: any) {
    console.log(error);

    // feedback.msgError(error)
  } finally {
    // loading.value = false
    // uploadRef.value?.clearFiles()
  }
};
const { lockFn: fileInput, isLock } = useLockFn(async (fileData: any) => {
  const promise = fileData.tempFiles.map(async (item: any) => {
    await fileToData(item.file);
  });
  await Promise.all(promise);
});

const handleDelete = async (index: any) => {
  data.value[showIndex.value].data.splice(index, 1);
};

const reSplit = () => {
  data.value.forEach((item: any) => {
    item.data = [];
    const index = fileList.value.findIndex(
      (fileItem) => fileItem.name == item.name
    );

    const contentList = splitText2ChunksArray({
      //@ts-ignore
      text: fileList.value[index].data,
      chunkLen: step.value,
    });

    contentList.forEach((contentListItem) => {
      item.data.push({ q: contentListItem, a: "" });
    });
  });
};

const parseFile = async (file: File) => {
  const suffix = file.name.substring(file.name.lastIndexOf(".") + 1);
  let res = "";
  switch (suffix) {
    case "md":
    case "txt":
      res = await readTxtContent(file);
      break;
    case "pdf":
      res = await readPdfContent(file);
      break;
    case "doc":
    case "docx":
      res = await readDocContent(file);
      break;
    default:
      res = await readTxtContent(file);
      break;
  }
  return res;
};

//选择预览文件
const selectShow = (index: number) => {
  showIndex.value = index;
};

//删除文件
const delFile = (index: number) => {
  data.value.splice(index, 1);
  fileList.value.splice(index, 1);
};
</script>

<style scoped lang="scss">
.comsume {
  background: linear-gradient(
    90deg,
    #fff0e000 0%,
    #fff0e0 49.32%,
    #fff0e000 100%
  );
}
</style>
