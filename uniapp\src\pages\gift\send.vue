<template>
  <page-meta :page-style="$theme.pageStyle"></page-meta>
  <view class="gift-send">
    <!-- 导航栏 -->
    <u-navbar title="赠送灵感值" :is-back="true">
    </u-navbar>
    
    <!-- 余额显示 -->
    <view class="balance-card mx-4 mt-4 p-6 rounded-2xl text-white">
      <view class="text-center">
        <view class="text-sm opacity-90 mb-2">当前余额</view>
        <view class="text-3xl font-bold">{{ userStore.userInfo.balance || '0' }}</view>
        <view class="text-sm opacity-90 mt-1">{{ appStore.getChatConfig.price_unit }}</view>
      </view>
    </view>

    <!-- 快捷操作区 -->
    <view class="quick-actions p-4">
      <view 
        class="record-btn-wrapper bg-white rounded-2xl p-4 border border-gray-100"
        @click="goToRecords"
      >
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <view class="icon-wrapper bg-gradient-to-r from-blue-500 to-blue-600 w-10 h-10 rounded-xl flex items-center justify-center mr-3">
              <u-icon name="list" size="20" color="#ffffff"></u-icon>
            </view>
            <view>
              <view class="text-lg font-medium text-main">查看赠送记录</view>
              <view class="text-sm text-tips">查看历史赠送和接收记录</view>
            </view>
          </view>
          <view class="arrow-icon">
            <u-icon name="arrow-right" size="16" color="#c0c4cc"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-container p-4">
      <!-- 接收用户选择 -->
      <view class="form-item bg-white rounded-xl p-4 mb-4">
        <view class="label text-gray-700 mb-3 font-medium">接收用户</view>
        <view class="user-selector" @click="selectUser">
          <view v-if="!selectedUser" class="placeholder flex items-center justify-center h-12 bg-gray-50 border border-gray-200 rounded-lg">
            <u-icon name="plus" size="16" color="#3c9cff" class="mr-2"></u-icon>
            <text class="text-primary">点击选择用户</text>
          </view>
          <view v-else class="selected-user flex items-center p-3 bg-gray-50 rounded-lg">
            <u-avatar :src="selectedUser.avatar" size="32" class="mr-3"></u-avatar>
            <view class="flex-1">
              <view class="font-medium text-main">{{ selectedUser.nickname }}</view>
              <view class="text-sm text-tips">ID: {{ selectedUser.id }}</view>
            </view>
            <u-icon name="arrow-right" size="16" color="#c0c4cc"></u-icon>
          </view>
        </view>
      </view>

      <!-- 赠送灵感值数量 -->
      <view class="form-item bg-white rounded-xl p-4 mb-4">
        <view class="label text-gray-700 mb-3 font-medium">赠送灵感值数量</view>
        <u-input 
          v-model="form.gift_amount"
          type="number"
          placeholder="请输入赠送灵感值数量"
          class="amount-input"
          input-align="center"
          font-size="24"
          height="48"
          bg-color="#f7f8fa"
          @input="validateAmount"
        />
        <view class="amount-tips text-sm text-tips mt-2 text-center">
          <text v-if="config?.min_gift_amount">最小: {{ config?.min_gift_amount || 1 }}</text>
          <text v-if="config?.max_gift_amount" class="ml-4">最大: {{ config?.max_gift_amount || 1000 }}</text>
        </view>
      </view>

      <!-- 规则说明 -->
      <view v-if="dataLoaded && config && statistics" class="rules-card bg-warning-light border border-warning-disabled rounded-xl p-4 mb-6">
        <view class="flex items-center mb-2">
          <u-icon name="info-circle" size="18" color="#f9ae3d" class="mr-2"></u-icon>
          <text class="font-medium text-warning">赠送规则</text>
        </view>
        <view class="text-sm text-warning leading-relaxed">
          <view>• 单次赠送范围: {{ config.min_gift_amount }}-{{ config.max_gift_amount }} {{ appStore.getChatConfig.price_unit }}</view>
          <view>• 每日赠送限额: {{ config.daily_gift_limit }} {{ appStore.getChatConfig.price_unit }}</view>
          <view>• 每日接收限额: {{ config.daily_receive_limit }} {{ appStore.getChatConfig.price_unit }}</view>
          <view>• 每日赠送次数: {{ config.gift_times_limit }} 次</view>
          <view>• 每日接收次数: {{ config.receive_times_limit }} 次</view>
          <view class="border-t border-warning-disabled pt-2 mt-2">
            <text class="font-medium">今日使用情况：</text>
          </view>
          <view>• 今日已赠送: {{ statistics.dailyGiftAmount }} {{ appStore.getChatConfig.price_unit }}（{{ statistics.dailyGiftTimes }} 次）</view>
          <view>• 今日剩余额度: {{ Math.max(0, config.daily_gift_limit - statistics.dailyGiftAmount) }} {{ appStore.getChatConfig.price_unit }}</view>
          <view>• 今日剩余次数: {{ Math.max(0, config.gift_times_limit - statistics.dailyGiftTimes) }} 次</view>
        </view>
      </view>
      
      <!-- 数据加载中 -->
      <view v-else-if="!dataLoaded" class="rules-card bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6">
        <view class="flex items-center justify-center">
          <u-loading-icon mode="spinner" size="16" class="mr-2"></u-loading-icon>
          <text class="text-gray-500">正在加载规则信息...</text>
        </view>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="fixed-bottom">
      <view class="p-4 bg-white border-t border-gray-100">
        <u-button
          type="primary"
          shape="circle"
          :disabled="!canSubmit || loading"
          :loading="loading"
          loading-text="赠送中..."
          @click="handleSubmit"
        >
          确认赠送 {{ form.gift_amount || 0 }} {{ appStore.getChatConfig.price_unit }}
        </u-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { getGiftConfig, executeGift, getUserGiftStatistics } from '@/api/gift'
import type { UserInfo, GiftConfig, GiftStatistics } from '@/api/gift'

const appStore = useAppStore()
const userStore = useUserStore()

// 响应式数据
const selectedUser = ref<UserInfo | null>(null)
const config = ref<GiftConfig | null>(null)
const statistics = ref<GiftStatistics | null>(null)
const loading = ref(false)
const dataLoaded = ref(false)

const form = ref({
  gift_amount: ''
})

// 计算属性
const canSubmit = computed(() => {
  if (!selectedUser.value || !form.value.gift_amount || !config.value) {
    return false
  }
  
  const amount = parseFloat(form.value.gift_amount)
  return amount >= config.value.min_gift_amount &&
         amount <= config.value.max_gift_amount &&
         amount <= Math.floor(userStore.userInfo.balance)
})

// 方法
const selectUser = () => {
  uni.navigateTo({
    url: '/pages/gift/select-user'
  })
}

const goToRecords = () => {
  uni.navigateTo({
    url: '/pages/gift/records'
  })
}

const validateAmount = (e: any) => {
  let value = e.detail ? e.detail.value : e
  // 只允许数字，不允许小数点
  value = value.replace(/[^\d]/g, '')
  form.value.gift_amount = value
}

const handleSubmit = async () => {
  if (!canSubmit.value) return
  
  try {
    loading.value = true
    
    await executeGift({
      to_user_id: selectedUser.value!.id,
      gift_amount: parseInt(form.value.gift_amount)
    })
    
    uni.showToast({
      title: '赠送成功',
      icon: 'success'
    })
    
    // 刷新用户信息
    await userStore.getUser()
    
    // 延迟跳转到记录页面
    setTimeout(() => {
      uni.redirectTo({
        url: '/pages/gift/records'
      })
    }, 1500)
    
  } catch (error: any) {
    // HTTP拦截器会将失败的请求转换为Promise.reject，错误信息在error中
    uni.showToast({
      title: typeof error === 'string' ? error : (error?.message || '赠送失败'),
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const loadConfig = async () => {
  try {
    const data = await getGiftConfig()
    // 由于HTTP拦截器的处理，成功的请求会直接返回data
    config.value = data
  } catch (error) {
    console.error('加载配置失败:', error)
    // 设置默认配置作为降级方案
    config.value = {
      is_enable: true,
      min_gift_amount: 1,
      max_gift_amount: 1000,
      daily_gift_limit: 100,
      daily_receive_limit: 500,
      gift_times_limit: 10,
      receive_times_limit: 20
    }
  }
}

const loadStatistics = async () => {
  try {
    const data = await getUserGiftStatistics()
    // 由于HTTP拦截器的处理，成功的请求会直接返回data
    statistics.value = data
  } catch (error) {
    console.error('加载统计失败:', error)
    // 设置默认统计数据作为降级方案
    statistics.value = {
      monthSend: 0,
      monthReceive: 0,
      totalSend: 0,
      totalReceive: 0,
      dailyGiftAmount: 0,
      dailyReceiveAmount: 0,
      dailyGiftTimes: 0,
      dailyReceiveTimes: 0
    }
  }
}

// 初始化数据
const initData = async () => {
  try {
    // 并行加载配置和统计数据
    await Promise.all([
      loadConfig(),
      loadStatistics()
    ])
  } finally {
    // 无论成功失败都标记为已加载，确保界面显示
    dataLoaded.value = true
  }
}

// 监听页面显示事件，获取选中的用户
const handlePageShow = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  
  // 从页面参数或全局状态获取选中的用户
  const selectedUserData = (currentPage as any).selectedUser
  if (selectedUserData) {
    selectedUser.value = selectedUserData
    console.log('接收到选中的用户:', selectedUserData)
    // 清除参数避免重复设置
    delete (currentPage as any).selectedUser
  }
}

// 使用 onShow 生命周期钩子
onShow(() => {
  handlePageShow()
})

// 监听用户选择事件
const handleUserSelect = (user: any) => {
  console.log('监听到用户选择事件:', user)
  selectedUser.value = user
  
  // 显示用户选择成功的反馈
  uni.showToast({
    title: `已选择 ${user.nickname}`,
    icon: 'success',
    duration: 1500
  })
}

onMounted(() => {
  initData()
  
  // 监听用户选择事件
  uni.$on('selectUser', handleUserSelect)
})

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('selectUser', handleUserSelect)
})
</script>

<style lang="scss" scoped>
.gift-send {
  min-height: 100vh;
  background: $u-bg-color;
  padding-bottom: 100rpx;
}

.balance-card {
  background: linear-gradient(135deg, $u-type-primary 0%, $u-type-primary-dark 100%);
}

.form-item {
  .label {
    font-size: 30rpx;
    color: $u-main-color;
  }
}

.user-selector {
  .placeholder {
    height: 96rpx;
    border: 1rpx solid $u-border-color;
    background-color: $u-bg-color;
  }
  
  .selected-user {
    height: auto;
    min-height: 96rpx;
  }
}

.rules-card {
  background-color: $u-type-warning-light;
  border-color: $u-type-warning-disabled;
  
  view {
    margin-bottom: 8rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

// 使用系统颜色变量
.text-primary {
  color: $u-type-primary;
}

.text-main {
  color: $u-main-color;
}

.text-tips {
  color: $u-tips-color;
}

.text-warning {
  color: $u-type-warning;
}

.bg-warning-light {
  background-color: $u-type-warning-light;
}

.border-warning-disabled {
  border-color: $u-type-warning-disabled;
}

.record-btn-wrapper {
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  }
  
  .icon-wrapper {
    background: linear-gradient(135deg, #3c9cff 0%, #2979ff 100%);
    box-shadow: 0 4rpx 12rpx rgba(60, 156, 255, 0.3);
  }
}

.nav-right-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background-color: rgba(60, 156, 255, 0.1);
  
  &:active {
    background-color: rgba(60, 156, 255, 0.2);
  }
}
</style> 