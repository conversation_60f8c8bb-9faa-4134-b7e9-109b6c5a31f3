/**
 *
 * Icon Library for <zui-svg-icon> usage
 *
 * Auto generated by /tools/generate-svg-icon.js
 *
 * !!! DO NOT MODIFY MANUALLY !!!
 *
 * @datetime 2024/8/27 20:39:47
 *
 */

// == collection start
const collections = {
  default: {
    "icons": {
      // #ifdef H5
      "document": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><defs><clipPath id=\"a\"><rect width=\"28\" height=\"28\" fill=\"#22ac38\" rx=\"0\"/></clipPath></defs><g fill=\"#22ac38\" clip-path=\"url(#a)\"><path d=\"M22.75 10.5h-7v-7H5.25v21h17.5zm-.71-1.75L17.5 4.21v4.54zm-17.665-7H17.5l7 7v16.625a.85.85 0 0 1-.************ 0 0 1-.63.245H4.375a.85.85 0 0 1-.63-.245.85.85 0 0 1-.245-.63V2.625q0-.382.245-.63a.85.85 0 0 1 .63-.245M8.75 14h10.5v1.75H8.75zm0-5.25h4.375v1.75H8.75zm0 10.5h10.5V21H8.75z\" style=\"mix-blend-mode:passthrough\"/><path fill-rule=\"evenodd\" d=\"M4.377 1.65q-.41-.014-.702.274h-.001q-.274.278-.274.701v22.748q-.014.41.275.703.292.289.702.274h19.246q.41.015.703-.275.289-.292.274-.702V8.71l-7.058-7.06zm-.561.416q-.216.218-.216.56v22.753q-.012.325.215.555.232.228.556.216H23.63q.325.012.555-.215.228-.231.216-.556V8.792L17.46 1.85H4.37q-.324-.012-.555.216M5.15 24.6h17.7V10.4h-7v-7H5.15zm17.5-.2H5.35V3.6h10.3v7h7zM17.4 3.97v4.88h4.881zm.2 4.68h4.198L17.6 4.452zm-8.95 0v1.95h4.575V8.65zm.2 1.75h4.175V8.85H8.85zm-.2 3.5v1.95h10.7V13.9zm.2 1.75h10.3V14.1H8.85zm-.2 3.5v1.95h10.7v-1.95zm.2 1.75h10.3v-1.55H8.85z\"/></g></svg>",
        0
      ],
      "fullscreen-exit": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"64\" height=\"64\" class=\"icon\" viewBox=\"0 0 1024 1024\"><path fill=\"#22ac38\" d=\"M379.336 697.237 153.362 921.55c-14.11 14.007-36.905 13.922-50.912-.188s-13.922-36.905.188-50.912l227.6-225.927H138.645c-18.99 0-34.385-15.446-34.385-34.5 0-19.053 15.395-34.5 34.385-34.5H413.72c18.99 0 34.384 15.447 34.384 34.5v276c0 9.15-3.622 17.926-10.07 24.396a34.33 34.33 0 0 1-24.314 10.104 34.33 34.33 0 0 1-24.314-10.104 34.56 34.56 0 0 1-10.071-24.396V697.237zm263.395-366.88 227.813-227.813c14.059-14.059 36.853-14.059 50.912 0s14.059 36.853 0 50.912l-225.18 225.18h187.147c18.99 0 34.385 15.445 34.385 34.5 0 19.053-15.395 34.5-34.385 34.5H608.346c-18.99 0-34.384-15.447-34.384-34.5v-276c0-9.15 3.622-17.926 10.07-24.396a34.33 34.33 0 0 1 24.314-10.105c9.12 0 17.865 3.635 24.314 10.105a34.56 34.56 0 0 1 10.07 24.395zM99.385 410a34.33 34.33 0 0 1-24.314-10.105A34.56 34.56 0 0 1 65 375.5v-276C65 80.446 80.395 65 99.385 65h275.077c18.99 0 34.384 15.446 34.384 34.5S393.452 134 374.462 134H133.769v241.5c0 9.15-3.622 17.925-10.07 24.395A34.33 34.33 0 0 1 99.384 410zm825.23 552H649.538c-18.99 0-34.384-15.446-34.384-34.5s15.394-34.5 34.384-34.5h240.693V651.5c0-19.054 15.394-34.5 34.384-34.5S959 632.446 959 651.5v276c0 19.054-15.395 34.5-34.385 34.5\"/></svg>",
        0
      ],
      "fullscreen": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"64\" height=\"64\" class=\"icon\" viewBox=\"0 0 1024 1024\"><path fill=\"#22ac38\" d=\"M924 616a36 36 0 0 0-36 36v236H652a36 36 0 0 0 0 72h272a36 36 0 0 0 36-36V652a36 36 0 0 0-36-36M372 64H100a36 36 0 0 0-36 36v272a36 36 0 0 0 72 0V136h236a36 36 0 0 0 0-72m552 0H652a36 36 0 0 0 0 72h185.09L626.54 346.54a36 36 0 1 0 50.92 50.91L888 186.91V372a36 36 0 0 0 72 0V100a36 36 0 0 0-36-36M372 616a35.87 35.87 0 0 0-25.46 10.55L136 837.09V652a36 36 0 0 0-72 0v272a36 36 0 0 0 36 36h272a36 36 0 0 0 0-72H186.91l210.55-210.54A36 36 0 0 0 372 616\"/></svg>",
        0
      ],
      "link": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"34\" height=\"34\" fill=\"none\"><defs><clipPath id=\"a\"><rect width=\"34\" height=\"34\" rx=\"0\"/></clipPath></defs><g fill=\"#101010\" clip-path=\"url(#a)\"><path d=\"M19.407 14.795a5.5 5.5 0 0 0-.56-.484 1.113 1.113 0 0 0-1.623 1.517q.117.14.274.236c.***************.305.268l.091.092c1.153 1.152.809 3.138-.344 4.291l-4.918 4.922a2.96 2.96 0 0 1-4.179 0l-.092-.093a2.96 2.96 0 0 1 0-4.181l2.173-2.174a1.208 1.208 0 0 0-1.429-1.947l-.002-.005-.023.022a1.2 1.2 0 0 0-.23.215L6.592 19.59c-2.123 2.126-2.123 5.604 0 7.727l.092.092c2.122 2.123 5.594 2.123 7.717 0l4.917-4.923c2.12-2.125 2.299-5.476.18-7.6z\" style=\"mix-blend-mode:passthrough\"/><path d=\"m27.405 6.737-.092-.094a5.37 5.37 0 0 0-7.732 0l-4.926 5.075c-2.127 2.19-2.244 5.354-.117 7.546l.09.092q.145.149.3.283.11.128.257.216l.002.002c.155.09.333.143.524.143.58 0 1.052-.485 1.052-1.083 0-.17-.038-.33-.105-.472-.139-.321-.399-.517-.58-.703l-.09-.091c-1.154-1.19-.716-2.918.439-4.107l4.93-5.074a2.9 2.9 0 0 1 4.184 0l.092.093c1.155 1.19 1.155 3.125 0 4.313l-2.17 2.237a1.25 1.25 0 0 0-.487.994c0 .683.538 1.236 1.2 1.236.232 0 .447-.067.63-.183l.004.007.034-.032q.155-.109.273-.26l2.287-2.173c2.128-2.19 2.128-5.774.001-7.965\" style=\"mix-blend-mode:passthrough\"/></g></svg>",
        1
      ],
      "mind_map": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"34\" height=\"34\" fill=\"none\"><defs><clipPath id=\"a\"><rect width=\"34\" height=\"34\" rx=\"0\"/></clipPath></defs><g clip-path=\"url(#a)\"><path fill=\"#101010\" d=\"M19.161 7.792c0 .844.685 1.534 1.521 1.534h6.083c.836 0 1.52-.69 1.52-1.534V6.257c0-.844-.684-1.535-1.52-1.535h-6.083c-.836 0-1.52.69-1.52 1.535-4.791 0-8.897 3.223-10.19 7.673H6.235c-.836 0-1.52.691-1.52 1.535v3.07c0 .844.684 1.534 1.52 1.534h2.737c1.293 4.451 5.399 7.674 10.19 7.674 0 .844.684 1.535 1.52 1.535h6.083c.836 0 1.52-.69 1.52-1.535v-1.535c0-.844-.684-1.534-1.52-1.534h-6.083c-.836 0-1.52.69-1.52 1.534a9.06 9.06 0 0 1-8.593-6.139h1.749c.836 0 1.52-.69 1.52-1.534v-.768h5.323c0 .844.685 1.535 1.521 1.535h6.083c.836 0 1.52-.69 1.52-1.535v-1.534c0-.845-.684-1.535-1.52-1.535h-6.083c-.836 0-1.52.69-1.52 1.535h-5.323v-.768c0-.844-.685-1.535-1.521-1.535h-1.749a9.06 9.06 0 0 1 8.592-6.138m1.521-1.535h6.083v1.535h-6.083zm0 19.951h6.083v1.535h-6.083zm0-9.975h6.083v1.534h-6.083z\" style=\"mix-blend-mode:passthrough\"/></g></svg>",
        1
      ],
      "outline": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"34\" height=\"34\" fill=\"none\"><defs><clipPath id=\"a\"><rect width=\"34\" height=\"34\" rx=\"0\"/></clipPath></defs><g clip-path=\"url(#a)\"><path fill=\"#101010\" d=\"M5.929 7C4.863 7 4 7.784 4 8.75s.863 1.75 1.929 1.75c1.065 0 1.928-.784 1.928-1.75S6.994 7 5.93 7m0 8.75c-1.067 0-1.93.784-1.93 1.75s.863 1.75 1.929 1.75c1.065 0 1.928-.784 1.928-1.75s-.863-1.75-1.928-1.75M4 26.25c0-.966.863-1.75 1.929-1.75 1.065 0 1.928.784 1.928 1.75S6.994 28 5.93 28C4.863 28 4 27.216 4 26.25M13.643 7c-1.065 0-1.929.784-1.929 1.75s.864 1.75 1.929 1.75H29.07c1.067 0 1.93-.784 1.93-1.75S30.137 7 29.071 7zm-1.929 10.5c0-.966.864-1.75 1.929-1.75H29.07c1.066 0 1.929.784 1.929 1.75s-.863 1.75-1.929 1.75H13.643c-1.065 0-1.929-.784-1.929-1.75m1.929 7c-1.065 0-1.929.784-1.929 1.75S12.578 28 13.643 28H29.07c1.067 0 1.93-.784 1.93-1.75s-.863-1.75-1.929-1.75z\" style=\"mix-blend-mode:passthrough\"/></g></svg>",
        1
      ],
      "science": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><defs><clipPath id=\"a\"><rect width=\"28\" height=\"28\" fill=\"#22ac38\" rx=\"0\"/></clipPath></defs><g clip-path=\"url(#a)\"><path fill=\"#22ac38\" d=\"M22.026 2.105c1.217 0 2.203.986 2.203 2.2v15.278c0 1.215-.986 2.2-2.203 2.2H5.215c-.843 0-1.5.607-1.5 1.32 0 .707.644 1.309 1.475 1.32l.025.001h19.014a.818.818 0 0 1 .02 1.634H5.214c-1.719 0-3.137-1.307-3.137-2.954q0-.083.005-.163l-.005-.07V4.307c0-1.215.986-2.2 2.203-2.2zm0 1.635H4.281a.566.566 0 0 0-.567.55v15.293c0 .307.245.557.55.566h17.762a.566.566 0 0 0 .566-.55V4.307a.566.566 0 0 0-.549-.566zm-1.636 9.493a.818.818 0 0 1 .02 1.635H5.917a.818.818 0 0 1-.02-1.634zm0-4.652a.818.818 0 0 1 .02 1.634H5.917a.818.818 0 0 1-.02-1.634z\" style=\"mix-blend-mode:passthrough\"/></g></svg>",
        0
      ],
      "search_base": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><rect width=\"14\" height=\"2.667\" x=\"8\" y=\"6\" fill=\"#22ac38\" rx=\"1.333\"/><rect width=\"18\" height=\"2.667\" x=\"4\" y=\"12.666\" fill=\"#22ac38\" rx=\"1.333\"/><rect width=\"18\" height=\"2.667\" x=\"4\" y=\"19.333\" fill=\"#22ac38\" rx=\"1.333\"/><rect width=\"2.769\" height=\"2.667\" x=\"4\" y=\"6\" fill=\"#22ac38\" fill-opacity=\".6\" rx=\"1.333\"/></svg>",
        0
      ],
      "search_copilot": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path fill=\"#22ac38\" fill-rule=\"evenodd\" d=\"M6.308 16.393q1.144-2.176 1.599-3.721.464-1.579.168-2.138-.151-.286-.902-.314-1.72.5-1.89 2.08-.183 1.696 1.025 4.093m19.71 8.384q.184-.195.283-.444t.099-.516q0-.07-.007-.138-.006-.068-.02-.135-.013-.068-.033-.134t-.047-.13q-.026-.063-.058-.123-.033-.06-.07-.118-.04-.057-.083-.11-.043-.054-.092-.102-.049-.049-.102-.093-.053-.043-.11-.081t-.118-.071-.124-.059-.13-.046-.133-.033-.136-.02-.137-.007q-.143 0-.283.029t-.272.085-.248.14q-.118.081-.216.185-2.486 2.639-8.748 1.58-4.064-.687-7.26-5.27 1.945-3.408 2.62-5.704.786-2.672-.043-4.238Q9.593 7.417 7 7.417h-.172l-.168.041Q2.886 8.402 2.5 12q-.298 2.767 1.65 6.3.265.48.537.934-1.566 2.569-3.83 5.775-.125.177-.19.384Q.6 25.6.6 25.817q0 .068.007.137.006.068.02.136.013.067.033.133t.047.13.058.124q.033.06.07.117.04.058.083.11.043.054.092.103t.102.092.11.082q.057.038.118.07t.124.06q.064.025.13.045.065.02.133.034.067.013.136.02.068.007.137.007.168 0 .332-.04.163-.04.312-.117.15-.078.276-.188t.224-.248l.003-.005q1.85-2.62 3.261-4.858 3.62 4.635 8.359 5.436 7.737 1.308 11.251-2.42\"/><path fill=\"#22ac38\" d=\"M15.523 8.232a.2.2 0 0 0 0 .38l3.466 1.116a2 2 0 0 1 1.291 1.291l1.116 3.467a.2.2 0 0 0 .38 0l1.117-3.467a2 2 0 0 1 1.29-1.29l3.467-1.117a.2.2 0 0 0 0-.38l-3.466-1.116a2 2 0 0 1-1.291-1.291l-1.116-3.467a.2.2 0 0 0-.381 0L20.28 5.825a2 2 0 0 1-1.29 1.29z\" style=\"opacity:.6000000238418579\" transform=\"rotate(13.763 14.931 1.767)\"/></svg>",
        0
      ],
      "search_research": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path fill=\"#22ac38\" d=\"M.51 15.812a.2.2 0 0 0 0 .376l6.475 2.382a2 2 0 0 1 1.235 1.335l2.087 7.412c.055.194.33.194.385 0l2.088-7.412a2 2 0 0 1 1.235-1.335l6.475-2.382a.2.2 0 0 0 0-.376l-6.475-2.382a2 2 0 0 1-1.235-1.335l-2.087-7.412c-.055-.194-.33-.194-.385 0L8.22 12.095a2 2 0 0 1-1.235 1.335z\"/><path fill=\"#22ac38\" d=\"M14.591 7.81a.2.2 0 0 0 0 .38l2.971.957a2 2 0 0 1 1.291 1.29l.957 2.972a.2.2 0 0 0 .38 0l.957-2.971a2 2 0 0 1 1.29-1.291l2.972-.957a.2.2 0 0 0 0-.38l-2.971-.957a2 2 0 0 1-1.291-1.29L20.19 2.59a.2.2 0 0 0-.38 0l-.957 2.971a2 2 0 0 1-1.29 1.291z\" style=\"opacity:.6000000238418579\"/></svg>",
        0
      ],
      "whole_network": [
        "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><defs><clipPath id=\"a\"><rect width=\"28\" height=\"28\" fill=\"#22ac38\" rx=\"0\"/></clipPath></defs><g clip-path=\"url(#a)\"><path fill=\"#22ac38\" d=\"m24.652 23.058.12-.142.096-.115.106-.134.096-.123.101-.13q.049-.064.093-.127.05-.064.098-.133l.09-.129.096-.137.088-.128.093-.14.084-.13.09-.146q.042-.065.08-.13l.09-.154c.025-.04.05-.085.074-.126q.049-.085.098-.175.03-.051.06-.109l.148-.279c.02-.035.035-.07.055-.109l.09-.183.06-.126.08-.167.06-.134q.037-.081.07-.164l.058-.136.068-.164.055-.14q.034-.083.063-.166l.052-.14.06-.172q.025-.07.046-.14l.058-.18.043-.134.058-.194.035-.12q.037-.126.069-.251.007-.033.019-.066.041-.16.079-.32.013-.051.022-.104l.05-.221q.01-.067.024-.129.02-.096.038-.2l.025-.139.032-.19.022-.143c.008-.063.02-.128.028-.191l.019-.145.022-.191.016-.145.016-.197.011-.142.014-.205.008-.134.008-.23.003-.112q.005-.173.006-.344c0-4.07-1.747-7.738-4.53-10.297l-.01-.01-.225-.203-.021-.019-.222-.189-.033-.027a7 7 0 0 0-.218-.178q-.022-.015-.039-.032a7 7 0 0 0-.215-.167l-.047-.036-.213-.158-.055-.041q-.104-.075-.21-.148l-.063-.043-.208-.14-.068-.046-.208-.132-.074-.046-.205-.126-.082-.049c-.068-.038-.134-.08-.202-.117l-.088-.05q-.099-.056-.202-.109l-.093-.05q-.1-.052-.2-.1l-.1-.05-.197-.095-.107-.05-.197-.09-.112-.049-.194-.082-.117-.049q-.096-.04-.192-.077l-.123-.046-.19-.071-.13-.047-.187-.064-.134-.044-.186-.06-.14-.041-.185-.055-.145-.038-.183-.05-.15-.037-.18-.044a3 3 0 0 1-.157-.036q-.089-.018-.177-.038-.083-.017-.164-.03L16.44.213a3 3 0 0 0-.17-.027c-.057-.008-.114-.02-.172-.027l-.175-.025q-.084-.013-.17-.022l-.18-.022-.164-.019-.189-.016a3 3 0 0 0-.16-.015q-.102-.006-.202-.014L14.711.02l-.235-.008q-.06 0-.12-.006A15 15 0 0 0 14 0q-.18.001-.355.005-.06 0-.12.006l-.236.008-.147.008-.202.014q-.082.005-.16.014l-.188.016q-.081.008-.164.02l-.183.021q-.084.013-.17.022l-.174.025-.172.027c-.058.008-.112.019-.17.027l-.175.033-.164.03-.178.038-.155.036q-.09.02-.18.044a3 3 0 0 0-.151.038l-.183.05-.145.037q-.093.026-.186.055l-.14.041q-.094.028-.185.06L9.583.72l-.189.065q-.063.021-.128.047l-.191.071-.123.046-.192.077-.117.05-.194.081-.112.05-.197.09-.107.049q-.098.046-.197.095l-.1.05-.2.1-.093.05-.202.11-.088.049-.205.117q-.041.025-.082.047l-.205.125c-.025.017-.05.03-.074.047l-.207.13-.069.047-.208.14-.062.044-.21.147-.056.041q-.106.077-.213.159l-.046.035-.216.167q-.021.016-.038.033-.11.086-.22.177-.014.015-.032.028l-.221.188-.022.02q-.114.098-.224.202l-.011.01A13.95 13.95 0 0 0 0 14q.001.173.005.344l.003.113.008.23.009.133q.004.103.013.205l.011.142.017.197.016.145.022.191q.006.073.019.145.011.096.027.191l.022.142.033.194.025.14q.016.098.038.2l.024.128.05.221q.01.053.022.104.036.16.079.32.007.033.019.066.032.127.068.251l.036.12.098.328.058.18.046.137.06.173q.025.07.052.139l.063.167.055.14q.032.08.068.163l.057.137.071.164.06.134.077.166.06.129.09.183q.028.053.055.11l.148.278.06.11c.033.057.063.114.098.172q.038.062.074.125l.09.154q.042.065.08.13l.09.145.084.132q.044.07.093.14.044.066.088.128.046.068.096.136l.09.129.098.134.093.125.101.132.096.123.106.134q.048.056.096.117l.12.142q.455.533.962 1.023c.077.07.15.144.23.215l.014.011q.11.102.224.203l.022.019.218.188.033.028.216.177q.021.016.038.033.107.086.214.167l.049.035.213.159.055.04q.103.076.21.148l.063.044c.068.047.14.093.208.14l.068.046.205.131q.037.026.077.047l.205.123.082.049c.068.038.134.08.202.117l.087.05q.1.056.203.109l.095.05a5 5 0 0 0 .3.15l.198.095.106.05.197.09.112.049.194.082.118.049q.095.04.194.077l.123.046q.096.039.191.071l.129.047.188.065.134.044.186.06.14.041q.09.028.185.055l.145.038.183.05.15.037.181.044q.078.016.156.036l.178.038.16.03.176.033q.082.015.17.027c.057.008.114.02.171.027l.175.025q.085.013.17.022l.183.022c.054.005.11.014.164.019l.188.016q.08.01.159.014l.202.014.148.008.235.008q.06 0 .12.006.178.004.356.005.179-.001.355-.005.06 0 .12-.006l.235-.008.148-.008.202-.014q.082-.005.159-.014l.188-.016a3 3 0 0 0 .164-.02l.183-.021c.058-.008.112-.016.17-.022l.175-.024q.085-.015.172-.028l.17-.027.174-.033.162-.03q.089-.017.177-.038.078-.016.156-.036.09-.019.18-.044.076-.016.15-.038l.184-.049.145-.038q.093-.026.186-.055l.139-.041q.095-.029.186-.06l.134-.044.188-.065q.065-.021.129-.047.096-.033.191-.071l.123-.046.194-.077.118-.05.194-.081.112-.05.197-.09.106-.049q.1-.046.197-.095l.101-.05.2-.1.096-.05.202-.11.087-.049q.104-.056.203-.117l.081-.05.206-.122.076-.047.205-.13.068-.047.208-.14c.022-.013.041-.03.063-.043l.21-.148.055-.041q.107-.077.213-.159l.05-.035c.07-.055.144-.11.213-.167q.02-.016.038-.033a7 7 0 0 0 .216-.177q.016-.015.033-.028.11-.093.218-.188l.022-.02q.114-.098.224-.202c.006-.002.008-.008.014-.01l.23-.216a13 13 0 0 0 .962-1.023m2.085-8.107-.01.134-.017.18q-.01.07-.014.14l-.019.172-.044.306-.021.142-.028.164-.024.142-.03.162-.03.142q-.017.081-.036.158l-.033.143-.038.158q-.016.07-.036.14l-.043.158-.039.137-.046.155-.041.137q-.024.078-.052.156a4 4 0 0 1-.101.295l-.044.123q-.05.138-.104.276-.024.067-.055.134-.028.073-.06.145l-.06.14c-.019.043-.038.09-.06.133l-.066.143-.06.13q-.033.072-.068.143c-.022.04-.041.085-.063.126l-.074.142-.065.123-.077.142-.068.12-.082.14-.068.117q-.041.07-.085.14l-.071.114-.088.14-.073.109q-.044.07-.093.137l-.074.106-.099.14-.073.1q-.05.07-.104.14l-.071.093-.11.142q-.032.041-.065.08l-.123.15q-.012.017-.028.03-.172.204-.35.404a18 18 0 0 0-3.247-1.142c.632-1.965 1.014-4.27 1.075-6.76h5.466l-.005.093q.003.09-.006.186m-5.803 9.772-.213.134-.038.024a5 5 0 0 1-.254.15l-.194.113-.063.035-.192.104q-.033.02-.068.036l-.188.098-.074.038-.189.093-.076.036q-.094.046-.192.087l-.076.033-.194.085-.071.03-.205.082c-.017.005-.033.014-.05.019q-.36.139-.732.257l-.05.016-.177.055-.077.022c.875-.919 1.635-2.17 2.24-3.674a18 18 0 0 1 2.727.908 12.6 12.6 0 0 1-1.577 1.208zM10.18 26.196l-.05-.016q-.371-.119-.732-.257-.025-.008-.046-.02l-.205-.081-.071-.028a4 4 0 0 1-.194-.085l-.077-.032-.191-.088-.077-.035q-.095-.044-.188-.093l-.074-.038-.189-.099c-.022-.01-.046-.025-.068-.035l-.192-.104-.062-.036a5 5 0 0 1-.246-.142l-.203-.12q-.018-.013-.038-.022l-.213-.134-.011-.008a13 13 0 0 1-1.574-1.208 18 18 0 0 1 2.727-.908c.602 1.503 1.364 2.755 2.239 3.674l-.077-.022a2 2 0 0 0-.188-.063m-6.027-4.059-.123-.15q-.034-.04-.063-.08-.056-.07-.11-.142-.035-.044-.07-.093l-.104-.139q-.038-.049-.074-.101l-.098-.14-.074-.106-.093-.14-.071-.109-.088-.14-.07-.114-.085-.14q-.037-.059-.069-.117l-.08-.14-.067-.12-.077-.14-.066-.122-.073-.142-.063-.126c-.025-.046-.047-.096-.069-.142l-.06-.131-.065-.142-.06-.134-.06-.14q-.031-.073-.06-.147l-.055-.131A8 8 0 0 1 2 18.392l-.044-.123-.057-.164-.044-.134-.052-.155-.041-.137-.046-.156-.039-.14c-.013-.051-.03-.103-.043-.158l-.036-.14-.038-.158-.033-.139-.036-.161-.03-.142-.03-.162-.024-.142-.028-.164-.022-.142-.024-.167-.02-.14-.018-.171q-.01-.07-.014-.14l-.016-.18-.011-.134q-.008-.094-.011-.189l-.006-.093h5.467c.057 2.493.443 4.795 1.074 6.76a18 18 0 0 0-3.247 1.142q-.18-.2-.356-.407-.016-.001-.024-.019m2.903-18.86q.106-.068.213-.134l.038-.024a5 5 0 0 1 .254-.15c.066-.039.132-.074.194-.113l.063-.035q.096-.054.192-.104.033-.02.068-.036l.189-.098q.036-.02.073-.038l.189-.093.076-.036q.094-.046.192-.087l.076-.033q.1-.042.194-.085l.071-.03.205-.082.05-.019q.36-.139.73-.257l.049-.016.18-.055.077-.022c-.875.919-1.635 2.17-2.239 3.674a18 18 0 0 1-2.728-.908 13 13 0 0 1 1.594-1.219m10.758-1.473.05.016q.369.119.732.257l.049.02.205.081.071.03q.1.041.194.085l.077.033q.095.041.19.088.04.017.077.035l.192.093c.024.01.049.025.074.036l.19.098c.023.01.047.025.07.035.062.036.128.069.19.104l.063.036a5 5 0 0 1 .246.142l.203.12.038.025.213.134.011.008q.836.547 1.577 1.208c-.822.355-1.74.662-2.728.908-.601-1.504-1.364-2.756-2.238-3.674l.074.022c.06.024.12.04.18.06m3.474 11.526c-.057-2.492-.443-4.794-1.074-6.76 1.189-.3 2.282-.685 3.247-1.142a12.74 12.74 0 0 1 3.294 7.905h-5.467zm-7.962-7.248a24.5 24.5 0 0 1-3.903-.41c.984-2.367 2.367-3.996 3.903-4.368zm0 1.216v6.032H7.918c.063-2.378.451-4.608 1.072-6.489 1.35.263 2.807.421 4.335.457m0 7.372v6.032a26 26 0 0 0-4.335.456c-.62-1.883-1.009-4.11-1.072-6.488zm0 7.248v4.778c-1.536-.372-2.92-2-3.903-4.368a24.5 24.5 0 0 1 3.903-.41m1.34 0a24.6 24.6 0 0 1 3.902.41c-.984 2.367-2.367 3.997-3.903 4.368zm0-1.216V14.67h5.406c-.063 2.378-.451 4.608-1.072 6.489a26 26 0 0 0-4.335-.457m0-7.372V7.298a26 26 0 0 0 4.334-.457c.62 1.884 1.009 4.111 1.072 6.49zm0-7.248V1.304c1.535.372 2.918 2 3.902 4.368-1.23.232-2.542.374-3.903.41M4.528 5.426a18 18 0 0 0 3.247 1.142c-.631 1.965-1.014 4.27-1.074 6.76H1.235A12.7 12.7 0 0 1 4.53 5.426\"/></g></svg>",
        0
      ]
      // #endif
    },
    "currentColor": "",
    "$_colorPalette": [
      "#22ac38",
      "#101010"
    ]
  },
}
// == collection end

const svglib = {}

svglib.registerCollection = (key, lib) => {
  if (collections[key]) {
    return
  }

  if (typeof lib.registerCollection === 'function') {
    collections[key] = lib.getCollection('default')
  } else {
    collections[key] = lib
  }
}

svglib.getCollection = (key = 'default') => {
  if (!collections[key]) throw new Error(`没有找到名为 ${key} 的图标库。`)

  return collections[key]
}

export const SvgIconLib = svglib
export default SvgIconLib
