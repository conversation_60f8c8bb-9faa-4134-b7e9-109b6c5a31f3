-- 智能体分成收益管理菜单添加文件
-- 执行日期: 2024-12-19
-- 功能说明: 为后台管理系统添加智能体分成收益管理菜单

-- 在AI知识库菜单下添加智能体分成收益管理菜单项
INSERT INTO `cm_system_menu` (`id`, `pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`) VALUES 
(50300, 6500, 'C', '智能体分成收益', '', 10, 'kb.robotRevenue/lists', 'robot_revenue', 'knowledge_base/robot_revenue/index', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加分成收益管理的操作权限
INSERT INTO `cm_system_menu` (`id`, `pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`) VALUES 
(50392, 50300, 'A', '分成统计', '', 0, 'kb.robotRevenue/statistics', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

INSERT INTO `cm_system_menu` (`id`, `pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`) VALUES 
(50393, 50300, 'A', '批量结算', '', 0, 'kb.robotRevenue/batchSettle', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

INSERT INTO `cm_system_menu` (`id`, `pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`) VALUES 
(50394, 50300, 'A', '分成配置', '', 0, 'kb.robotRevenue/getConfig', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

INSERT INTO `cm_system_menu` (`id`, `pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`) VALUES 
(50395, 50300, 'A', '设置配置', '', 0, 'kb.robotRevenue/setConfig', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 菜单说明:
-- 50300: 智能体分成收益 - 主菜单项，对应前端页面
-- 50301: 分成统计 - 获取统计数据的权限
-- 50302: 批量结算 - 批量结算分成收益的权限  
-- 50303: 分成配置 - 获取分成配置的权限
-- 50304: 设置配置 - 设置分成配置的权限

-- 注意事项:
-- 1. 菜单ID需要确保不与现有菜单冲突
-- 2. pid=6500 对应"AI知识库"菜单
-- 3. 权限标识格式: kb.robotRevenue/方法名
-- 4. 前端组件路径: knowledge_base/robot_revenue/index 