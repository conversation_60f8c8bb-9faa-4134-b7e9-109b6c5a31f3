import type { ComputedRef, MaybeRef } from 'vue'
export type LayoutKey = "blank" | "components-account-bind-mobile" | "components-account-bind-weixin" | "components-account-forgot-pwd" | "components-account-hooks-use-captcha-effect" | "components-account" | "components-account-login" | "components-account-login-mailbox-login" | "components-account-login-mobile-login" | "components-account-login-weixin-login" | "components-account-register" | "components-account-tologin" | "components-aside" | "components-aside-menu-item" | "components-aside-menu" | "components-aside-nav" | "components-aside-panel" | "components-customer" | "components-customer-manual" | "components-customer-online" | "components-footer" | "components-header-application" | "components-header-fold" | "components-header" | "components-header-member-btn" | "components-header-menu-item" | "components-header-menu" | "components-header-notification" | "components-header-redeem-code-pop" | "components-header-title-logo" | "components-header-user-info" | "components-header-user" | "components-notice" | "components-setting-drawer" | "components-setting" | "components-tabbar" | "default" | "single-row"
declare module "../../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/pages/runtime/composables" {
  interface PageMeta {
    layout?: MaybeRef<LayoutKey | false> | ComputedRef<LayoutKey | false>
  }
}