<template>
    <svg
        :width="size + 'rpx'"
        :height="size + 'rpx'"
        viewBox="0 0 64 64"
        class="kb-animated-icon"
        xmlns="http://www.w3.org/2000/svg"
    >
        <!-- 书本左页 -->
        <rect x="8" y="12" width="22" height="40" rx="3" fill="var(--kb-color, #38B2AC)" />

        <!-- 书本右页 -->
        <rect x="34" y="12" width="22" height="40" rx="3" fill="var(--kb-color, #38B2AC)" />

        <!-- 翻动动画页 -->
        <rect x="34" y="12" width="22" height="40" rx="3" fill="#4FD1C5" opacity="0.8">
            <animate
                attributeName="x"
                values="34;30;34"
                dur="2s"
                repeatCount="indefinite"
            />
            <animate
                attributeName="opacity"
                values="0.8;0.2;0.8"
                dur="2s"
                repeatCount="indefinite"
            />
        </rect>

        <!-- 书脊 -->
        <rect x="30" y="10" width="4" height="44" fill="#2C7A7B" />
    </svg>
</template>

<script lang="ts" setup>
/**
 * 知识库动态图标
 * props:
 *   size: 图标大小（默认64 rpx）
 *   color: 基础填充颜色，可通过 CSS 变量 --kb-color 覆盖
 */
const props = withDefaults(defineProps<{ size?: number }>(), {
    size: 64
})
</script>

<style scoped>
.kb-animated-icon {
    display: block;
}
</style> 