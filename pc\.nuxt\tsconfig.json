// Generated by nuxi
{
  "compilerOptions": {
    "paths": {
      "nitropack": [
        "../node_modules/.pnpm/nitropack@2.10.4_typescript@4.9.3/node_modules/nitropack"
      ],
      "defu": [
        "../node_modules/.pnpm/defu@6.1.4/node_modules/defu"
      ],
      "h3": [
        "../node_modules/.pnpm/h3@1.13.0/node_modules/h3"
      ],
      "consola": [
        "../node_modules/.pnpm/consola@3.3.3/node_modules/consola"
      ],
      "ofetch": [
        "../node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch"
      ],
      "@unhead/vue": [
        "../node_modules/.pnpm/@unhead+vue@1.11.14_vue@3.5.13_typescript@4.9.3_/node_modules/@unhead/vue"
      ],
      "@nuxt/devtools": [
        "../node_modules/.pnpm/@nuxt+devtools@1.7.0_rollup@4.29.1_vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0_c3kctwi2jhyjg3b5iupkmutmwe/node_modules/@nuxt/devtools"
      ],
      "@vue/runtime-core": [
        "../node_modules/.pnpm/@vue+runtime-core@3.5.13/node_modules/@vue/runtime-core"
      ],
      "@vue/compiler-sfc": [
        "../node_modules/.pnpm/@vue+compiler-sfc@3.5.13/node_modules/@vue/compiler-sfc"
      ],
      "@vue/runtime-dom": [
        "../node_modules/.pnpm/@vue+runtime-dom@3.5.13/node_modules/@vue/runtime-dom"
      ],
      "vue-router": [
        "../node_modules/.pnpm/vue-router@4.5.0_vue@3.5.13_typescript@4.9.3_/node_modules/vue-router"
      ],
      "vue-router/auto-routes": [
        "../node_modules/.pnpm/vue-router@4.5.0_vue@3.5.13_typescript@4.9.3_/node_modules/vue-router/vue-router-auto-routes"
      ],
      "unplugin-vue-router/client": [
        "../node_modules/.pnpm/unplugin-vue-router@0.10.9_rollup@4.29.1_vue-router@4.5.0_vue@3.5.13_typescript@4.9.3___vue@3.5.13_typescript@4.9.3_/node_modules/unplugin-vue-router/client"
      ],
      "@nuxt/schema": [
        "../node_modules/.pnpm/@nuxt+schema@3.12.4_rollup@4.29.1/node_modules/@nuxt/schema"
      ],
      "nuxt": [
        "../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt"
      ],
      "~": [
        "../src"
      ],
      "~/*": [
        "../src/*"
      ],
      "@": [
        "../src"
      ],
      "@/*": [
        "../src/*"
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "assets": [
        "../src/assets"
      ],
      "assets/*": [
        "../src/assets/*"
      ],
      "public": [
        "../src/public"
      ],
      "public/*": [
        "../src/public/*"
      ],
      "#app": [
        "../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "pinia": [
        "../node_modules/.pnpm/pinia@2.3.0_typescript@4.9.3_vue@3.5.13_typescript@4.9.3_/node_modules/pinia/dist/pinia"
      ],
      "#vue-router": [
        "../node_modules/.pnpm/vue-router@4.5.0_vue@3.5.13_typescript@4.9.3_/node_modules/vue-router"
      ],
      "#imports": [
        "./imports"
      ],
      "#components": [
        "./components"
      ]
    },
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ESNext",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "strict": true,
    "noUncheckedIndexedAccess": false,
    "forceConsistentCasingInFileNames": true,
    "noImplicitOverride": true,
    "module": "ESNext",
    "noEmit": true,
    "lib": [
      "ESNext",
      "dom",
      "dom.iterable",
      "webworker"
    ],
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "types": [
      "swiper/vue"
    ],
    "moduleResolution": "Bundler",
    "useDefineForClassFields": true,
    "noImplicitThis": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "./nuxt.d.ts",
    "../.config/nuxt.*",
    "../**/*",
    "../src/**/*",
    "../node_modules/.pnpm/@pinia+nuxt@0.4.11_magicast@0.3.5_rollup@4.29.1_typescript@4.9.3_vue@3.5.13_typescript@4.9.3_/node_modules/@pinia/nuxt/runtime",
    "../node_modules/.pnpm/@pinia+nuxt@0.4.11_magicast@0.3.5_rollup@4.29.1_typescript@4.9.3_vue@3.5.13_typescript@4.9.3_/node_modules/@pinia/nuxt/dist/runtime",
    "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.8.0_magicast@0.3.5_rollup@4.29.1_webpack@5.97.1/node_modules/@nuxtjs/tailwindcss/runtime",
    "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.8.0_magicast@0.3.5_rollup@4.29.1_webpack@5.97.1/node_modules/@nuxtjs/tailwindcss/dist/runtime",
    "../node_modules/.pnpm/@element-plus+nuxt@1.1.1_@element-plus+icons-vue@2.3.1_vue@3.5.13_typescript@4.9.3___element-_s2kpmq34wc6w6e4qwtynrq7aym/node_modules/@element-plus/nuxt/runtime",
    "../node_modules/.pnpm/@element-plus+nuxt@1.1.1_@element-plus+icons-vue@2.3.1_vue@3.5.13_typescript@4.9.3___element-_s2kpmq34wc6w6e4qwtynrq7aym/node_modules/@element-plus/nuxt/dist/runtime",
    "../node_modules/.pnpm/nuxt-swiper@1.2.2_magicast@0.3.5_rollup@4.29.1/node_modules/nuxt-swiper/runtime",
    "../node_modules/.pnpm/nuxt-swiper@1.2.2_magicast@0.3.5_rollup@4.29.1/node_modules/nuxt-swiper/dist/runtime",
    "../node_modules/.pnpm/@nuxt+devtools@1.7.0_rollup@4.29.1_vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0_c3kctwi2jhyjg3b5iupkmutmwe/node_modules/@nuxt/devtools/runtime",
    "../node_modules/.pnpm/@nuxt+devtools@1.7.0_rollup@4.29.1_vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0_c3kctwi2jhyjg3b5iupkmutmwe/node_modules/@nuxt/devtools/dist/runtime",
    "../node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/telemetry/runtime",
    "../node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/telemetry/dist/runtime",
    ".."
  ],
  "exclude": [
    "../dist",
    "../node_modules",
    "../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/node_modules",
    "../node_modules/.pnpm/@pinia+nuxt@0.4.11_magicast@0.3.5_rollup@4.29.1_typescript@4.9.3_vue@3.5.13_typescript@4.9.3_/node_modules/@pinia/nuxt/runtime/server",
    "../node_modules/.pnpm/@pinia+nuxt@0.4.11_magicast@0.3.5_rollup@4.29.1_typescript@4.9.3_vue@3.5.13_typescript@4.9.3_/node_modules/@pinia/nuxt/dist/runtime/server",
    "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.8.0_magicast@0.3.5_rollup@4.29.1_webpack@5.97.1/node_modules/@nuxtjs/tailwindcss/runtime/server",
    "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.8.0_magicast@0.3.5_rollup@4.29.1_webpack@5.97.1/node_modules/@nuxtjs/tailwindcss/dist/runtime/server",
    "../node_modules/.pnpm/@element-plus+nuxt@1.1.1_@element-plus+icons-vue@2.3.1_vue@3.5.13_typescript@4.9.3___element-_s2kpmq34wc6w6e4qwtynrq7aym/node_modules/@element-plus/nuxt/runtime/server",
    "../node_modules/.pnpm/@element-plus+nuxt@1.1.1_@element-plus+icons-vue@2.3.1_vue@3.5.13_typescript@4.9.3___element-_s2kpmq34wc6w6e4qwtynrq7aym/node_modules/@element-plus/nuxt/dist/runtime/server",
    "../node_modules/.pnpm/nuxt-swiper@1.2.2_magicast@0.3.5_rollup@4.29.1/node_modules/nuxt-swiper/runtime/server",
    "../node_modules/.pnpm/nuxt-swiper@1.2.2_magicast@0.3.5_rollup@4.29.1/node_modules/nuxt-swiper/dist/runtime/server",
    "../node_modules/.pnpm/@nuxt+devtools@1.7.0_rollup@4.29.1_vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0_c3kctwi2jhyjg3b5iupkmutmwe/node_modules/@nuxt/devtools/runtime/server",
    "../node_modules/.pnpm/@nuxt+devtools@1.7.0_rollup@4.29.1_vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0_c3kctwi2jhyjg3b5iupkmutmwe/node_modules/@nuxt/devtools/dist/runtime/server",
    "../node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/telemetry/runtime/server",
    "../node_modules/.pnpm/@nuxt+telemetry@2.6.2_magicast@0.3.5_rollup@4.29.1/node_modules/@nuxt/telemetry/dist/runtime/server",
    "../.output"
  ]
}