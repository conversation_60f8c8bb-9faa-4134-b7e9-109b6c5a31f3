<template>
    <view class="role-example-selector">
        <view class="selector-wrapper">
            <u-button 
                type="primary" 
                size="normal"
                @click="showPopup = true"
                class="example-selector-btn"
                :custom-style="buttonStyle"
            >
                <view class="btn-content">
                    <text class="btn-text">选择角色示例</text>
                    <u-icon name="arrow-right" size="14" class="btn-icon"></u-icon>
                </view>
            </u-button>
            
            <!-- 选择状态显示 -->
            <view v-if="selectedExample" class="selected-indicator">
                <u-tag 
                    text="已选择" 
                    type="success" 
                    size="mini"
                    :show="true"
                    closable
                    @close="clearSelection"
                >
                    <template #icon>
                        <u-icon name="checkmark" size="12"></u-icon>
                    </template>
                </u-tag>
                <text class="selected-title">{{ selectedExample.title }}</text>
            </view>
        </view>
        
        <u-popup 
            v-model="showPopup" 
            mode="bottom" 
            height="85%"
            border-radius="20"
            closeable
            safe-area-inset-bottom
            class="example-popup"
        >
            <view class="popup-content">
                <view class="popup-header">
                    <view class="header-content">
                        <u-icon name="star-fill" size="20" color="#667eea"></u-icon>
                        <text class="popup-title">选择角色示例</text>
                    </view>
                    <view class="header-actions">
                        <u-icon name="close" size="20" @click="showPopup = false"></u-icon>
                    </view>
                </view>
                
                <!-- 搜索栏 -->
                <view class="search-section">
                    <u-search 
                        v-model="searchKeyword"
                        placeholder="搜索角色示例..."
                        :show-action="false"
                        shape="round"
                        bg-color="#f8f9fa"
                        class="search-input"
                    ></u-search>
                </view>
                
                <view class="content-wrapper">
                    <!-- 分类标签 -->
                    <view class="category-section">
                        <view class="section-title">
                            <u-icon name="folder" size="16" color="#667eea"></u-icon>
                            <text>示例分类</text>
                        </view>
                        <scroll-view scroll-x="true" class="category-scroll">
                            <view class="category-tabs">
                                <view
                                    v-for="category in categories"
                                    :key="category.id"
                                    :class="[
                                        'category-tab',
                                        selectedCategoryId === category.id ? 'category-active' : ''
                                    ]"
                                    @click="selectCategory(category.id)"
                                >
                                    <text class="category-name">{{ category.name }}</text>
                                    <u-badge 
                                        :count="getCategoryExampleCount(category.id)" 
                                        :max="99"
                                        :absolute="false"
                                        bg-color="#f56c6c"
                                        class="category-badge"
                                    ></u-badge>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                    
                    <!-- 示例列表 -->
                    <view class="example-section">
                        <view class="section-title">
                            <u-icon name="file-text" size="16" color="#667eea"></u-icon>
                            <text>角色示例</text>
                            <text v-if="filteredExamples.length" class="example-count">
                                ({{ filteredExamples.length }} 个)
                            </text>
                        </view>
                        
                        <view v-if="loading" class="loading-container">
                            <u-loading-icon mode="spinner" size="32" color="#667eea"></u-loading-icon>
                            <text class="loading-text">加载中...</text>
                        </view>
                        
                        <view v-else-if="filteredExamples.length === 0" class="empty-container">
                            <u-icon name="inbox" size="48" color="#c0c4cc"></u-icon>
                            <text class="empty-text">
                                {{ searchKeyword ? '未找到匹配的角色示例' : '暂无角色示例' }}
                            </text>
                        </view>
                        
                        <scroll-view v-else scroll-y="true" class="example-scroll">
                            <view class="example-list">
                                <view
                                    v-for="example in filteredExamples"
                                    :key="example.id"
                                    :class="[
                                        'example-item',
                                        selectedExampleId === example.id ? 'example-selected' : ''
                                    ]"
                                    @click="selectExample(example)"
                                >
                                    <view class="example-header">
                                        <view class="example-title-section">
                                            <u-icon name="star" size="14" color="#f39c12"></u-icon>
                                            <text class="example-title">{{ example.title }}</text>
                                        </view>
                                        <view class="example-actions">
                                            <u-icon 
                                                v-if="selectedExampleId === example.id" 
                                                name="checkmark-circle-fill" 
                                                size="20" 
                                                color="#67c23a"
                                                class="selected-icon"
                                            ></u-icon>
                                        </view>
                                    </view>
                                    
                                    <view v-if="example.description" class="example-description">
                                        {{ example.description }}
                                    </view>
                                    
                                    <view class="example-content">
                                        <view class="content-preview">
                                            {{ example.content }}
                                        </view>
                                        <view class="content-meta">
                                            <text class="content-length">{{ example.content.length }} 字符</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </view>
                
                <!-- 底部按钮 -->
                <view class="popup-footer">
                    <view class="footer-info">
                        <view v-if="selectedExample" class="selected-info">
                            <u-icon name="checkmark-circle" size="16" color="#67c23a"></u-icon>
                            <text class="selected-text">已选择：{{ selectedExample.title }}</text>
                        </view>
                    </view>
                    <view class="footer-buttons">
                        <u-button 
                            type="default" 
                            @click="showPopup = false"
                            class="cancel-btn"
                            size="normal"
                        >
                            取消
                        </u-button>
                        <u-button 
                            type="primary" 
                            @click="confirmSelect"
                            :disabled="!selectedExample"
                            class="confirm-btn"
                            size="normal"
                            :custom-style="confirmButtonStyle"
                        >
                            <view class="confirm-content">
                                <u-icon name="checkmark" size="14" color="#fff"></u-icon>
                                <text>确定选择</text>
                            </view>
                        </u-button>
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getAllRoleExamples } from '@/api/role_example'

interface RoleExample {
    id: number
    title: string
    content: string
    description?: string
}

interface Category {
    id: number
    name: string
    examples: RoleExample[]
}

const emit = defineEmits<{
    (event: 'select', example: RoleExample): void
}>()

const showPopup = ref(false)
const loading = ref(false)
const searchKeyword = ref('')
const categories = ref<Category[]>([])
const selectedCategoryId = ref<number | null>(null)
const selectedExampleId = ref<number | null>(null)
const selectedExample = ref<RoleExample | null>(null)

// 按钮样式
const buttonStyle = {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    border: 'none',
    borderRadius: '16rpx',
    boxShadow: '0 4rpx 16rpx rgba(102, 126, 234, 0.3)'
}

const confirmButtonStyle = {
    background: 'linear-gradient(135deg, #67c23a 0%, #85ce61 100%)',
    border: 'none'
}

// 当前选中分类的示例列表
const currentExamples = computed(() => {
    if (!selectedCategoryId.value) return []
    const category = categories.value.find(cat => cat.id === selectedCategoryId.value)
    return category?.examples || []
})

// 过滤后的示例列表（支持搜索）
const filteredExamples = computed(() => {
    let examples = currentExamples.value
    if (searchKeyword.value.trim()) {
        const keyword = searchKeyword.value.toLowerCase()
        examples = examples.filter(example => 
            example.title.toLowerCase().includes(keyword) ||
            example.content.toLowerCase().includes(keyword) ||
            (example.description && example.description.toLowerCase().includes(keyword))
        )
    }
    return examples
})

// 获取分类下的示例数量
const getCategoryExampleCount = (categoryId: number) => {
    const category = categories.value.find(cat => cat.id === categoryId)
    return category?.examples?.length || 0
}

// 选择分类
const selectCategory = (categoryId: number) => {
    selectedCategoryId.value = categoryId
    selectedExampleId.value = null
    selectedExample.value = null
    searchKeyword.value = '' // 清空搜索
}

// 选择示例
const selectExample = (example: RoleExample) => {
    selectedExampleId.value = example.id
    selectedExample.value = example
}

// 清除选择
const clearSelection = () => {
    selectedExampleId.value = null
    selectedExample.value = null
}

// 确认选择
const confirmSelect = () => {
    if (selectedExample.value) {
        emit('select', selectedExample.value)
        showPopup.value = false
        uni.showToast({
            title: '角色示例选择成功',
            icon: 'success',
            duration: 2000
        })
    }
}

// 获取角色示例数据
const fetchRoleExamples = async () => {
    try {
        loading.value = true
        const data = await getAllRoleExamples()
        categories.value = data || []
        
        // 默认选中第一个分类
        if (categories.value.length > 0) {
            selectedCategoryId.value = categories.value[0].id
        }
    } catch (error) {
        console.error('获取角色示例失败:', error)
        uni.showToast({
            title: '获取角色示例失败',
            icon: 'error'
        })
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    fetchRoleExamples()
})
</script>

<style lang="scss" scoped>
.role-example-selector {
    display: inline-block;
    width: 100%;
}

.selector-wrapper {
    width: 100%;
}

.example-selector-btn {
    width: 100%;
    border-radius: 16rpx;
    overflow: hidden;
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8rpx 0;
}

.btn-text {
    color: #fff;
    font-weight: 500;
    margin-right: 8rpx;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.selected-indicator {
    margin-top: 16rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 12rpx 16rpx;
    background: #f0f9ff;
    border-radius: 12rpx;
    border: 1px solid #e0f2fe;
}

.selected-title {
    color: #0369a1;
    font-size: 26rpx;
    font-weight: 500;
}

.example-popup {
    z-index: 9999;
}

.popup-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
}

.popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 40rpx 20rpx;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.popup-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #2c3e50;
}

.search-section {
    padding: 24rpx 40rpx;
    background: #fff;
}

.search-input {
    width: 100%;
}

.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 0 40rpx;
}

.category-section {
    margin-bottom: 32rpx;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20rpx;
}

.example-count {
    color: #909399;
    font-weight: normal;
    margin-left: 8rpx;
}

.category-scroll {
    width: 100%;
    white-space: nowrap;
}

.category-tabs {
    display: flex;
    gap: 16rpx;
    padding-bottom: 8rpx;
}

.category-tab {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 16rpx 24rpx;
    border-radius: 24rpx;
    border: 2rpx solid #e4e7ed;
    background: #fff;
    transition: all 0.3s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.category-tab.category-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.category-name {
    font-size: 26rpx;
    font-weight: 500;
    color: #2c3e50;
}

.category-active .category-name {
    color: #fff;
}

.category-badge {
    margin-left: 8rpx;
}

.example-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.loading-container, .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400rpx;
    color: #909399;
}

.loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #909399;
}

.empty-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #909399;
}

.example-scroll {
    flex: 1;
    height: 100%;
}

.example-list {
    padding-bottom: 20rpx;
}

.example-item {
    border: 2rpx solid #e4e7ed;
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    background: #fff;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.example-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8rpx;
    height: 100%;
    background: transparent;
    transition: all 0.3s ease;
}

.example-item.example-selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.25);
}

.example-item.example-selected::before {
    background: #667eea;
}

.example-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
}

.example-title-section {
    display: flex;
    align-items: center;
    gap: 8rpx;
    flex: 1;
}

.example-title {
    font-weight: 600;
    font-size: 30rpx;
    color: #2c3e50;
}

.selected-icon {
    animation: bounce 0.5s ease;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-4rpx);
    }
    60% {
        transform: translateY(-2rpx);
    }
}

.example-description {
    font-size: 26rpx;
    color: #606266;
    margin-bottom: 20rpx;
    line-height: 1.5;
}

.example-content {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 24rpx;
    position: relative;
}

.content-preview {
    font-size: 26rpx;
    color: #606266;
    line-height: 1.6;
    max-height: 160rpx;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

.content-meta {
    margin-top: 16rpx;
    display: flex;
    justify-content: flex-end;
}

.content-length {
    font-size: 22rpx;
    color: #c0c4cc;
    background: rgba(255, 255, 255, 0.8);
    padding: 4rpx 12rpx;
    border-radius: 8rpx;
}

.popup-footer {
    padding: 32rpx 40rpx;
    background: #f8f9fa;
    border-top: 1px solid #e4e7ed;
}

.footer-info {
    margin-bottom: 24rpx;
}

.selected-info {
    display: flex;
    align-items: center;
    gap: 8rpx;
}

.selected-text {
    color: #67c23a;
    font-size: 26rpx;
    font-weight: 500;
}

.footer-buttons {
    display: flex;
    gap: 24rpx;
}

.cancel-btn, .confirm-btn {
    flex: 1;
    border-radius: 16rpx;
}

.confirm-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
}
</style> 