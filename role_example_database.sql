-- 智能体角色示例库功能数据库设计
-- 创建时间：2025-05-24
-- 说明：智能体角色示例库功能，复用示例库类别，新增角色示例表

-- 1. 创建智能体角色示例表
CREATE TABLE `cm_role_example` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属类别ID（关联cm_example_category表）',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '示例标题',
  `content` text NOT NULL COMMENT '角色设定内容',
  `description` text COMMENT '示例描述',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id` (`category_id`) USING BTREE,
  INDEX `idx_status` (`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能体角色示例表';

-- 2. 添加智能体角色示例菜单（在智能体管理菜单下，ID为50124）
INSERT INTO `cm_system_menu` (`id`, `pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`)
VALUES (60030, 50124, 'C', '角色示例', '', 5, 'kb.robot/roleExampleLists', 'ai_role/role_example', 'ai_role/role_example/index', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 3. 添加角色示例的按钮权限
INSERT INTO `cm_system_menu` (`id`, `pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`)
VALUES 
(60031, 60030, 'A', '添加', '', 0, 'kb.robot/roleExampleAdd', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60032, 60030, 'A', '编辑', '', 0, 'kb.robot/roleExampleEdit', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60033, 60030, 'A', '删除', '', 0, 'kb.robot/roleExampleDel', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60034, 60030, 'A', '状态', '', 0, 'kb.robot/roleExampleStatus', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60035, 60030, 'A', '详情', '', 0, 'kb.robot/roleExampleDetail', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60036, 60030, 'A', '获取类别列表', '', 0, 'kb.robot/roleExampleCategoryList', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60037, 60030, 'A', '根据类别获取示例', '', 0, 'kb.robot/roleExampleListByCategory', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(60038, 60030, 'A', '获取所有角色示例', '', 0, 'kb.robot/roleExampleAll', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 4. 插入示例数据（可选）
INSERT INTO `cm_role_example` (`category_id`, `title`, `content`, `description`, `sort`, `status`, `create_time`) 
VALUES 
(1, '专业助手', '你是一个专业的AI助手，具有丰富的知识和经验。你的任务是为用户提供准确、有用的信息和建议。请始终保持专业、友好和耐心的态度。', '通用专业助手角色设定，适用于大多数咨询场景', 100, 1, UNIX_TIMESTAMP()),
(1, '学习导师', '你是一位经验丰富的学习导师，擅长帮助学生理解复杂概念，制定学习计划，并提供学习方法指导。你的教学风格耐心细致，善于用简单易懂的方式解释复杂问题。', '专门用于教育和学习指导的角色设定', 90, 1, UNIX_TIMESTAMP()),
(2, '创意写手', '你是一位富有创意的写手，擅长创作各种类型的文章、故事和文案。你的写作风格生动有趣，能够根据不同需求调整语言风格和表达方式。', '适用于创意写作和文案创作的角色设定', 80, 1, UNIX_TIMESTAMP()),
(2, '技术专家', '你是一位资深的技术专家，在软件开发、系统架构和技术解决方案方面有着深厚的经验。你能够提供专业的技术建议和解决方案。', '专门用于技术咨询和问题解决的角色设定', 70, 1, UNIX_TIMESTAMP()); 