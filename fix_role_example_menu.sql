-- 修正智能体角色示例库菜单权限配置
-- 执行时间：2025-05-25
-- 说明：将错误的权限路径修正为正确的kb.robot前缀

-- 1. 更新主菜单权限
UPDATE `cm_system_menu` SET 
    `perms` = 'kb.robot/roleExampleLists',
    `paths` = 'ai_role/role_example',
    `component` = 'ai_role/role_example/index'
WHERE `id` = 60030;

-- 2. 更新按钮权限
UPDATE `cm_system_menu` SET `perms` = 'kb.robot/roleExampleAdd' WHERE `id` = 60031;
UPDATE `cm_system_menu` SET `perms` = 'kb.robot/roleExampleEdit' WHERE `id` = 60032;
UPDATE `cm_system_menu` SET `perms` = 'kb.robot/roleExampleDel' WHERE `id` = 60033;
UPDATE `cm_system_menu` SET `perms` = 'kb.robot/roleExampleStatus' WHERE `id` = 60034;
UPDATE `cm_system_menu` SET `perms` = 'kb.robot/roleExampleDetail' WHERE `id` = 60035;
UPDATE `cm_system_menu` SET `perms` = 'kb.robot/roleExampleCategoryList' WHERE `id` = 60036;
UPDATE `cm_system_menu` SET `perms` = 'kb.robot/roleExampleListByCategory' WHERE `id` = 60037;
UPDATE `cm_system_menu` SET `perms` = 'kb.robot/roleExampleAll' WHERE `id` = 60038;

-- 3. 验证更新结果
SELECT id, name, perms, paths, component FROM `cm_system_menu` WHERE id BETWEEN 60030 AND 60038; 