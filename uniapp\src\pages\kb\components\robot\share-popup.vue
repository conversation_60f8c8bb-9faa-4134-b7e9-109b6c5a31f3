<template>
    <u-popup
        v-model="show"
        mode="center"
        border-radius="14"
        duration="16"
        @close="close"
    >
        <view class="w-[650rpx]">
            <view class="p-[30rpx] text-center text-lg font-medium">分享至广场</view>
            
            <!-- 审核拒绝原因显示 -->
            <view v-if="rejectReason" class="mx-[30rpx] mb-[20rpx] p-[24rpx] bg-red-50 rounded-[14rpx] border-1 border-red-200">
                <view class="flex items-start">
                    <view class="text-red-500 mr-[10rpx] mt-[4rpx]">
                        <text class="text-[24rpx]">❌</text>
                    </view>
                    <view class="text-[24rpx] text-red-700 flex-1">
                        <view class="font-medium mb-[8rpx]">上次审核拒绝原因</view>
                        <view class="bg-red-100 px-[20rpx] py-[16rpx] rounded-[12rpx] mb-[12rpx]">
                            {{ rejectReason }}
                        </view>
                        <view class="text-[22rpx] text-red-600 leading-[1.4]">
                            请根据拒绝原因调整智能体内容后重新提交
                        </view>
                    </view>
                </view>
            </view>
            
            <!-- 分成收益说明 -->
            <view v-if="revenueConfig.is_enable" class="mx-[30rpx] mb-[20rpx] p-[30rpx] bg-gradient-to-r from-green-50 to-emerald-50 rounded-[16rpx] border-1 border-green-200">
                <view class="flex items-start">
                    <view class="text-green-500 mr-[15rpx] mt-[8rpx]">
                        <text class="text-[30rpx]">💰</text>
                    </view>
                    <view class="flex-1">
                        <view class="font-bold text-[28rpx] text-green-800 mb-[16rpx]">分成收益说明</view>
                        <view class="text-[24rpx] text-green-700 leading-[1.5]">
                            <view class="mb-[12rpx]">
                                <text class="font-medium text-green-800">🎯 收益机制：</text>
                                <text>分享智能体至广场后，其他用户每次使用您的智能体时，您都将获得分成收益！</text>
                            </view>
                            <view class="mb-[12rpx]">
                                <text class="font-medium text-green-800">💎 分成比例：</text>
                                <text>用户消耗{{ tokenUnit }}的 </text>
                                <text class="bg-green-200 px-[12rpx] py-[6rpx] rounded font-bold text-green-900">{{ shareRatio }}%</text>
                                <text> 将作为您的收益</text>
                            </view>
                            <view class="bg-green-100 px-[20rpx] py-[16rpx] rounded-[12rpx] mt-[16rpx]">
                                <text class="text-[22rpx] text-green-600">
                                    <text class="font-medium">示例：</text>用户使用您的智能体消耗了100{{ tokenUnit }}，您将获得{{ Math.round(100 * shareRatio / 100) }}{{ tokenUnit }}收益
                                </text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            
            <!-- 审核模式提示 -->
            <view v-if="!revenueConfig.auto_audit" class="mx-[30rpx] mb-[20rpx] p-[24rpx] bg-amber-50 rounded-[14rpx] border-1 border-amber-200">
                <view class="flex items-start">
                    <view class="text-amber-500 mr-[10rpx] mt-[4rpx]">
                        <text class="text-[24rpx]">⚠️</text>
                    </view>
                    <view class="text-[24rpx] text-amber-700 flex-1">
                        <view class="font-medium mb-[8rpx]">审核提醒</view>
                        <view class="leading-[1.4]">
                            当前系统采用人工审核模式，提交后需要管理员审核通过才能在广场显示，请耐心等待审核结果。
                        </view>
                    </view>
                </view>
            </view>
            
            <view class="h-[230rpx] p-[30rpx]">
                <view
                    class="flex items-center justify-between rounded-[12rpx] p-[20rpx] text-content bg-[#eeeeee]"
                    @click="showSelect = true"
                >
                    {{ formData.cate_name ? formData.cate_name : '全部' }}
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>
            <view class="flex justify-center mt-[30rpx] pb-[20rpx]">
                <u-button
                    type="primary"
                    :customStyle="{
                        width: '220rpx'
                    }"
                    :loading="isLock"
                    @click="handleSubmit"
                >
                    确定
                </u-button>
            </view>
        </view>
    </u-popup>
    <u-select
        v-model="showSelect"
        :list="cateLists"
        valueName="id"
        labelName="name"
        :confirmColor="$theme.primaryColor"
        @confirm="handleChoiceCate"
    ></u-select>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { useAppStore } from '@/stores/app'
import { getAgentCategoryList, shareAgent } from '@/api/task_reward'
import { useLockFn } from '@/hooks/useLockFn'

const emit = defineEmits(['success', 'close'])

const appStore = useAppStore()
const show = ref<boolean>(false)
const showSelect = ref<boolean>(false)
const cateLists = ref<{
    name: string,
    id: string
    image: string
}[]>([])
const formData = reactive<{
    cate_id: string,
    id: string,
    cate_name: string
}>({
    cate_id: '',
    id: '',
    cate_name: ''
})
const rejectReason = ref<string>('')

// 获取分成收益配置信息
const revenueConfig = computed(() => appStore.getSquareConfig?.robot_award?.revenue_config || {})
const tokenUnit = computed(() => appStore.getTokenUnit)
const shareRatio = computed(() => revenueConfig.value.share_ratio || 30)

const getData = async () => {
    try {
        const list = await getAgentCategoryList()
        list.unshift({ name: '全部', id: '' })
        cateLists.value = list
    } catch (error) {
        console.log('获取智能体分类失败=>', error)
    }
}

//提交
const { lockFn: handleSubmit, isLock } = useLockFn(async () => {
    await shareAgent(formData)
    show.value = false
    emit('success', formData.id)
})

const handleChoiceCate = (e: any) => {
    formData.cate_id = e[0].value
    formData.cate_name = e[0].label
}

const open = (id: string, verifyResult?: string) => {
    getData()
    show.value = true
    formData.id = id
    rejectReason.value = verifyResult || ''  // 设置拒绝原因
}

const close = () => {
    emit('close')
}

defineExpose({ open })
</script>
