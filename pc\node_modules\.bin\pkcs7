#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/pkcs7@1.0.4/node_modules/pkcs7/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/pkcs7@1.0.4/node_modules/pkcs7/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/pkcs7@1.0.4/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/pkcs7@1.0.4/node_modules/pkcs7/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/pkcs7@1.0.4/node_modules/pkcs7/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/pkcs7@1.0.4/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../pkcs7/bin/cli.js" "$@"
else
  exec node  "$basedir/../pkcs7/bin/cli.js" "$@"
fi
