<template>
    <!-- 账号组件 -->
    <u-popup
        v-model="showPopup"
        :closeable="true"
        mode="center"
        :maskCloseAble="false"
        border-radius="20"
    >
        <view class="px-[50rpx] py-[40rpx] bg-white" style="width: 85vw">
            <u-form>
                <view class="mb-[70rpx] text-xl text-center">
                    {{ aliAccount == '' ? '添加' : '修改' }}支付宝账号
                </view>
                <u-form-item borderBottom>
                    <input
                        class="h-[60rpx] w-full"
                        v-model="aliAccount"
                        name="account"
                        type="text"
                        placeholder="请输入支付宝账号"
                    />
                </u-form-item>
                <u-form-item borderBottom>
                    <input
                        class="mt-[20rpx] h-[60rpx] w-full"
                        v-model="aliRealName"
                        name="real_name"
                        type="text"
                        placeholder="请输入支付宝名称"
                    />
                </u-form-item>
                <view class="mt-[80rpx]">
                    <u-button
                        type="primary"
                        shape="circle"
                        size="default"
                        :customStyle="{
                            padding: '0 30rpx',
                            height: '80rpx'
                        }"
                        @click="handleSubmit"
                    >
                        确定
                    </u-button>
                </view>
            </u-form>
        </view>
    </u-popup>
</template>

<script lang="ts" setup>
import { ref, computed, watchEffect } from 'vue'

const emit = defineEmits<{
    (event: 'update:show', value: boolean): void
    (event: 'update:account', value: string | number): void
    (event: 'update:real_name', value: string | number): void
}>()
const props = defineProps<{
    show: boolean
    account: string | number
    real_name: string | number
}>()

const aliAccount = ref<string | number>('')
const aliRealName = ref<string | number>('')

const showPopup = computed({
    get: () => props.show,
    set: (value) => {
        emit('update:show', value)
    }
})

watchEffect(() => {
    aliAccount.value = props.account
    aliRealName.value = props.real_name
})

const handleSubmit = () => {
    uni.$u.toast('添加成功')
    showPopup.value = false
    emit('update:account', aliAccount.value)
    emit('update:real_name', aliRealName.value)
}
</script>
