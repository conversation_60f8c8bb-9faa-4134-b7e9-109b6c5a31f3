#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nuxt/bin/nuxt.mjs" "$@"
else
  exec node  "$basedir/../nuxt/bin/nuxt.mjs" "$@"
fi
