<template>
    <view v-if="processedLists.length && isHidden" class="px-[30rpx] pt-[24rpx]">
        <view v-if="isLoading" class="banner-loading h-[280rpx] bg-[#f3f4f6] rounded-[20rpx] flex items-center justify-center">
            <text class="text-[#999] text-sm">加载中...</text>
        </view>
        <u-swiper
            v-else
            :list="cachedImageList"
            :height="280"
            name="image"
            :borderRadius="20"
            :autoplay="!isLoading && processedLists.length > 1"
            :interval="4000"
            @click="handleClick"
        >
        </u-swiper>
    </view>
</template>

<script setup lang="ts">
import {watchEffect, ref, computed, nextTick, onMounted} from "vue";
import {navigateTo} from "@/utils/navigate";
import {useAppStore} from "@/stores/app";
import bannerImageCache from "@/utils/bannerImageCache";

const {getImageUrl} = useAppStore();

const props = defineProps<{
    isHidden: boolean
    prop: Record<string, any>
}>()

const isLoading = ref(true)
const cachedImageList = ref<Array<any>>([])

const processedLists = computed(() => {
    if (!props.prop?.data) return []
    
    return props.prop.data
        .filter((item: any) => item.isShow)
        .map((item: any) => ({
            ...item,
            image: getImageUrl(item.image)
        }))
})

watchEffect(async () => {
    if (processedLists.value.length === 0) {
        isLoading.value = false
        cachedImageList.value = []
        return
    }

    try {
        isLoading.value = true
        
        const imageUrls = processedLists.value.map((item: any) => item.image).filter(Boolean)
        
        if (imageUrls.length === 0) {
            cachedImageList.value = processedLists.value
            isLoading.value = false
            return
        }

        const cachedUrls = await bannerImageCache.preloadBannerImages(imageUrls)
        
        const urlMap = new Map()
        imageUrls.forEach((originalUrl: string, index: number) => {
            urlMap.set(originalUrl, cachedUrls[index])
        })
        
        cachedImageList.value = processedLists.value.map((item: any) => ({
            ...item,
            image: urlMap.get(item.image) || item.image
        }))

        await nextTick()
        isLoading.value = false
        
        console.log('Banner图片缓存处理完成', {
            原始数量: imageUrls.length,
            缓存数量: cachedUrls.length,
            缓存信息: bannerImageCache.getCacheInfo()
        })
        
    } catch (error) {
        console.warn('Banner图片缓存处理失败:', error)
        cachedImageList.value = processedLists.value
        isLoading.value = false
    }
})

onMounted(() => {
    setTimeout(() => {
        if (processedLists.value.length === 0) {
            isLoading.value = false
        }
    }, 100)
})

const handleClick = (item: any) => {
    if (!item.link) return;
    navigateTo(item.link);
};
</script>

<style lang="scss" scoped>
.banner-loading {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}
</style>

