#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0/node_modules/vite/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0/node_modules/vite/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0/node_modules/vite/bin/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0/node_modules/vite/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/vite@5.4.11_@types+node@18.19.68_sass@1.62.1_terser@5.37.0/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../vite/bin/vite.js" "$@"
fi
