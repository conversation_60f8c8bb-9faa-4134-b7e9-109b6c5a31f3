## 1.0.13（2023-12-06）
1. 优化
## 1.0.12（2023-12-06）
1. 阻止事件冒泡问题
## 1.0.11（2023-11-10）
1. 调整清除按钮样式的marginLeft，避免微信上多数情况触发不了的BUG
## 1.0.10（2023-10-07）
1. 修复搜狗输入法下存在不可清空的情况
## 1.0.9（2023-09-14）
1. 修复H5等情况设置禁用或可读情况下，点击事件无效的问题
## 1.0.8（2023-08-22）
1. 修复无法@keyboardheightchange无法获取键盘高度的BUG
## 1.0.7（2023-08-18）
1. 修复ios端不能输入的BUG
## 1.0.6（2023-08-05）
1. 修复在vue2模式下，v-model设置为0时不生效的BUG
## 1.0.5（2023-07-18）
1. 修复在微信小程序端清除内容存在不能清除的BUG
## 1.0.4（2023-07-13）
1.  修复value/v-model更改不生效的BUG
## 1.0.3（2023-07-03）
去除插槽判断，避免某些平台不显示的BUG
## 1.0.2（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.1（2023-05-12）
1. 修复vue3双向绑定的BUG
## 1.0.0（2023-05-10）
uv-input 输入框
