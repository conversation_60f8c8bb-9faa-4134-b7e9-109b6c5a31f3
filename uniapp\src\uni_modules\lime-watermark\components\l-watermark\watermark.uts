import { WatermarkProps, WatermarkFont, WatermarkGAP } from './type'
import { ComponentPublicInstance } from 'vue'
// import { defaultFont } from './commom'
const defaultFont : WatermarkFont = {
	color: 'rgba(0,0,0,.15)',
	fontSize: 16,
	fontWeight: 'normal',
	fontStyle: 'normal',
	fontFamily: 'sans-serif',
}


// #ifdef APP-ANDROID
function shallowMerge(obj1 : WatermarkFont | null, obj2 : WatermarkFont = defaultFont) : WatermarkFont {
	if (obj1 != null) {
		const result = obj1;
		for (const key in obj2) {
			if (obj1[key] == null) {
				result[key] = obj2[key];
			}
		}
		return result
	} else {
		return obj2
	}
}

function shallowMerge(obj1 : WatermarkProps | null, obj2 : WatermarkProps) : WatermarkProps {
	if (obj1 != null) {
		const result = obj1;
		for (const key in obj2) {
			if (obj1[key] == null) {
				result[key] = obj2[key];
			}
		}
		return result
	} else {
		return obj2
	}
}
// #endif

// #ifndef APP-ANDROID
function shallowMerge(obj1 : WatermarkFont | null, obj2 : WatermarkFont = defaultFont) : WatermarkFont 
function shallowMerge(obj1 : WatermarkProps | null, obj2 : WatermarkProps) : WatermarkProps {
	if (obj1 != null) {
		const result = obj1;
		for (const key in obj2) {
			if (obj1[key] == null) {
				result[key] = obj2[key];
			}
		}
		return result
	} else {
		return obj2
	}
}
// #endif
export class Watermark {
	el : UniElement
	props : WatermarkProps = {
		rotate: -22,
		baseSize: 2,
		fontGap: 3,
		gap: [30, 30]
	} as WatermarkProps
	gap : WatermarkGAP = {
		gapX: 0,
		gapY: 0,
		gapXCenter: 0,
		gapYCenter: 0,
		offsetLeft: 0,
		offsetTop: 0,
	} as WatermarkGAP
	font : WatermarkFont = defaultFont
	instance: ComponentPublicInstance|null = null
	ctx: DrawableContext|null=null
	constructor(el : UniElement, instance: ComponentPublicInstance|null = null) {
		this.el = el
		this.instance = instance
	}
	getMarkSize():Promise<number[]> {
		const firstChild = this.el.parentElement!.firstChild!
		const children = firstChild.children
		const length = children.size
		const {font} = this
		let _childSize = 0
		for (var i = 0; i < length; i++) {
			const child = children[i.toInt()];
			if (child.tagName == 'TEXT') {
				_childSize++
				for (let key in font) {
					child.style.setProperty(key, font[key])
				}
			}
		}
		return new Promise<number[]>((resolve)=>{
			setTimeout(()=>{
				uni.createSelectorQuery().in(this.instance!).select(`.${firstChild.classList[0]}`).boundingClientRect(res=>{
					res as NodeInfo
					console.log(res.width)
					// ios createSelectorQuery in 无效 故使用offset
					const width = firstChild.offsetWidth // res.width as number
					const height = firstChild.offsetHeight //res.height as number
					resolve([width, height + this.props.fontGap + _childSize])
				}).exec()
			},0)
		})
	}
	show(ctx: DrawableContext){
		this.ctx = ctx
	}
	calcGap() {
		const { gap = [20, 20], offset } = this.props
		const [gapX, gapY] = gap;
		const gapXCenter = gapX / 2;
		const gapYCenter = gapY / 2;
		const offsetLeft = offset != null ? offset[0] : gapXCenter;
		const offsetTop = offset != null && offset.length > 1? offset[1]: gapYCenter;
		this.gap = {
			gapX,
			gapY,
			gapXCenter,
			gapYCenter,
			offsetLeft,
			offsetTop
		} as WatermarkGAP
	}
	rotateRectangle(width: number, height: number):number[]{
		const rotate = this.props.rotate as number
		const angleRadians = rotate * Math.PI / 180;
		const sin = Math.sin(angleRadians);
		const cos = Math.cos(angleRadians);
		const rotatedWidth = Math.abs(width * cos) + Math.abs(height * sin);
		const rotatedHeight = Math.abs(width * sin) + Math.abs(height * cos);
  
		this.el.style.setProperty('width', rotatedWidth)
		this.el.style.setProperty('height', rotatedHeight)
		this.el.style.setProperty('transform', `translate(-50%,-50%) rotate(${rotate}deg)`)
		return [rotatedWidth, rotatedHeight];
	}
	render(options : WatermarkProps) {
		this.props = shallowMerge(options, this.props)
		this.font = shallowMerge(options.font, this.font)
		this.calcGap()
		const contents:string[] = this.props.content as string[] 
		this.getMarkSize().then(res=>{
			const [markWidth, markHeight] = res
			const { gapX, gapY } = this.gap
			
			const ctx = this.ctx ?? this.el.getDrawableContext() as DrawableContext
			
			const [canvasWidth, canvasHeight] = this.rotateRectangle(this.el.offsetWidth, this.el.offsetHeight)
			
			const cols = Math.ceil(canvasWidth / (markWidth + gapX))
			const rows = Math.ceil(canvasHeight / (markHeight + gapY))
			
			ctx.font = `${this.font.fontSize}`
			ctx.textAlign = 'center'
			ctx.fillStyle = this.font.color!
			for (let i = 0; i < rows; i++) {
				for (let j = 0; j < cols; j++) {
					const x = j * (markWidth + gapX)  + (markWidth + gapX) / 2
					const y = i * (markHeight + gapY) + (markHeight + gapY) / 2
					contents.forEach((text, index) =>{
						ctx.fillText(text, x, y + index * (this.font.fontSize! + this.props.fontGap))
					})
					ctx.update()
				}
			}
		})
	}
}