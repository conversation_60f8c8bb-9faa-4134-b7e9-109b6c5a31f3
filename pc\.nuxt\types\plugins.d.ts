// Generated by Nuxt'
import type { Plugin } from '#app'

type Decorate<T extends Record<string, any>> = { [K in keyof T as K extends string ? `$${K}` : never]: T[K] }

type IsAny<T> = 0 extends 1 & T ? true : false
type InjectionType<A extends Plugin> = IsAny<A> extends true ? unknown : A extends Plugin<infer T> ? Decorate<T> : unknown

type NuxtAppInjections = 
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/app/plugins/payload.client").default> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/app/plugins/navigation-repaint.client").default> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/app/plugins/revive-payload.server").default> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/app/plugins/revive-payload.client").default> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/app/plugins/chunk-reload.client").default> &
  InjectionType<typeof import("../../node_modules/.pnpm/@pinia+nuxt@0.4.11_magicast@0.3.5_rollup@4.29.1_typescript@4.9.3_vue@3.5.13_typescript@4.9.3_/node_modules/@pinia/nuxt/dist/runtime/plugin.vue3").default> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/head/runtime/plugins/unhead").default> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/pages/runtime/plugins/router").default> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.12.4_@parcel+watcher@2.5.0_@types+node@18.19.68_db0@0.2.1_eslint@8.41.0_ioredis@5.4.2__w3zwnsg5icbmgrcqcmuo7352zq/node_modules/nuxt/dist/pages/runtime/plugins/prefetch.client").default> &
  InjectionType<typeof import("../../src/plugins/detectMobile.client").default> &
  InjectionType<typeof import("../../src/plugins/fetch").default> &
  InjectionType<typeof import("../../src/plugins/icons").default> &
  InjectionType<typeof import("../../src/plugins/tinymce.client").default> &
  InjectionType<typeof import("../../src/plugins/vconsole").default> &
  InjectionType<typeof import("../../src/plugins/vue-query").default>

declare module '#app' {
  interface NuxtApp extends NuxtAppInjections { }

  interface NuxtAppLiterals {
    pluginName: 'nuxt:revive-payload:client' | 'nuxt:head' | 'nuxt:router' | 'nuxt:payload' | 'nuxt:revive-payload:server' | 'nuxt:chunk-reload' | 'nuxt:global-components' | 'nuxt:prefetch'
  }
}

declare module 'vue' {
  interface ComponentCustomProperties extends NuxtAppInjections { }
}

export { }
