<template>
    <view class="p-[20rpx]">
        <view class="bg-white rounded-lg p-[30rpx]">
            <view class="flex items-center">
                <view class="text-[#555555] text-base w-[190rpx]"
                    >订单编号</view
                >
                <view class="text-[#333333] text-base flex-1">{{
                    detailData?.sn
                }}</view>
            </view>
            <view class="flex items-center mt-[40rpx]">
                <view class="text-[#555555] text-base w-[190rpx]"
                    >用户信息</view
                >
                <view class="text-[#333333] text-base flex-1">{{
                    detailData?.user.nickname
                }}</view>
            </view>
            <view class="flex items-center mt-[40rpx]">
                <view class="text-[#555555] text-base w-[190rpx]"
                    >操作时间</view
                >
                <view class="text-[#333333] text-base flex-1">{{
                    detailData?.create_time
                }}</view>
            </view>
            <view class="flex items-center mt-[40rpx]">
                <view class="text-[#555555] text-base w-[190rpx]"
                    >智能体/应用名</view
                >
                <view class="text-[#333333] text-base flex-1">{{
                    detailData?.robot_name
                }}</view>
            </view>
            <view class="flex items-center mt-[40rpx]">
                <view class="text-[#555555] text-base w-[190rpx]"
                    >变动类型</view
                >
                <view class="text-[#333333] text-base flex-1 flex items-center">
                    <text class="mr-[10rpx]">{{ detailData?.change_type }}</text>
                    <!-- 智能体分成收益标识 -->
                    <view 
                        v-if="isRobotRevenue(detailData?.change_type)"
                        class="px-[12rpx] py-[4rpx] text-[20rpx] bg-green-100 text-green-800 rounded-full"
                    >
                        分成收益
                    </view>
                </view>
            </view>
            <view class="flex items-center mt-[40rpx]">
                <view class="text-[#555555] text-base w-[190rpx]"
                    >变动数量</view
                >
                <view class="text-[#333333] text-base flex-1" :class="{ 'text-green-600': isRobotRevenue(detailData?.change_type) && detailData?.action == 1 }">
                    {{ detailData?.action == 1 ? '+' : '-' }}
                    {{ detailData?.change_amount }}
                </view>
            </view>

            <!-- 智能体分成收益详细信息 -->
            <view v-if="detailData?.revenue_info" class="mt-[40rpx]">
                <view class="text-[#555555] text-base mb-[20rpx] border-b border-gray-200 pb-[10rpx]">
                    分成收益详情
                </view>
                
                <!-- 收益说明 -->
                <view class="mb-[30rpx]">
                    <view class="text-[#555555] text-sm mb-[10rpx]">收益说明</view>
                    <view class="text-green-600 font-medium text-sm">
                        {{ detailData.revenue_info.revenue_desc }}
                    </view>
                </view>
                
                <!-- 智能体信息 -->
                <view class="mb-[30rpx]">
                    <view class="text-[#555555] text-sm mb-[10rpx]">智能体信息</view>
                    <view class="flex items-center">
                        <image 
                            :src="detailData.revenue_info.robot_image" 
                            class="w-[60rpx] h-[60rpx] rounded-full mr-[20rpx]"
                            mode="aspectFill"
                        />
                        <view>
                            <view class="font-medium text-sm">{{ detailData.revenue_info.robot_name }}</view>
                            <view class="text-xs text-gray-500 mt-[4rpx]">来自智能体广场</view>
                        </view>
                    </view>
                </view>
                
                <!-- 分享者信息 -->
                <view class="mb-[30rpx]">
                    <view class="text-[#555555] text-sm mb-[10rpx]">分享者</view>
                    <view class="flex items-center">
                        <image 
                            :src="detailData.revenue_info.sharer_avatar" 
                            class="w-[48rpx] h-[48rpx] rounded-full mr-[20rpx]"
                            mode="aspectFill"
                        />
                        <text class="text-sm">{{ detailData.revenue_info.sharer_nickname }}</text>
                    </view>
                </view>
                
                <!-- 分成详情 -->
                <view class="mb-[30rpx]">
                    <view class="text-[#555555] text-sm mb-[10rpx]">分成详情</view>
                    <view class="bg-gray-50 p-[20rpx] rounded-lg">
                        <view class="flex justify-between mb-[10rpx]">
                            <text class="text-sm">总消耗电力值：</text>
                            <text class="text-sm font-medium">{{ detailData.revenue_info.total_cost }}</text>
                        </view>
                        <view class="flex justify-between mb-[10rpx]">
                            <text class="text-sm">分成比例：</text>
                            <text class="text-sm font-medium text-blue-600">{{ detailData.revenue_info.share_ratio }}%</text>
                        </view>
                        <view class="flex justify-between mb-[10rpx]">
                            <text class="text-sm">您的收益：</text>
                            <text class="text-sm font-medium text-green-600">+{{ detailData.revenue_info.share_amount }}</text>
                        </view>
                        <view class="flex justify-between">
                            <text class="text-xs text-gray-500">平台保留：</text>
                            <text class="text-xs text-gray-500">{{ detailData.revenue_info.platform_amount }}</text>
                        </view>
                    </view>
                </view>
                
                <!-- 结算信息 -->
                <view class="mb-[30rpx]">
                    <view class="text-[#555555] text-sm mb-[10rpx]">结算信息</view>
                    <view class="text-sm">
                        <view class="mb-[8rpx]">
                            <text class="text-gray-500">结算方式：</text>
                            <text>{{ detailData.revenue_info.settle_type_desc }}</text>
                        </view>
                        <view v-if="detailData.revenue_info.settle_time">
                            <text class="text-gray-500">结算时间：</text>
                            <text>{{ detailData.revenue_info.settle_time }}</text>
                        </view>
                    </view>
                </view>
                
                <!-- 收益说明 -->
                <view class="bg-blue-50 p-[20rpx] rounded-lg border border-blue-200">
                    <view class="flex items-start">
                        <text class="text-blue-500 mr-[10rpx] text-[24rpx]">ℹ️</text>
                        <view class="flex-1">
                            <view class="text-blue-700 font-medium text-sm mb-[8rpx]">收益说明</view>
                            <view class="text-blue-700 text-xs leading-[1.4]">
                                • 通过使用智能体广场的智能体可获得分成收益<br/>
                                • 分成收益将在每天凌晨自动结算并发放<br/>
                                • 分成比例由智能体分享者设置决定
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <view class="mt-[40rpx]" v-if="Number(type) === 1">
                <view class="text-[#555555] text-base w-[190rpx]"
                    >扣费明细</view
                >
                <view class="mt-2">
                    <uni-table border stripe emptyText="暂无更多数据">
                        <!-- 表头行 -->
                        <uni-tr>
                            <uni-th align="center">模块名称</uni-th>
                            <uni-th align="center">AI模型</uni-th>
                            <uni-th align="center"
                                >消耗{{ appStore.getTokenUnit }}</uni-th
                            >
                        </uni-tr>
                        <!-- 表格数据行 -->
                        <uni-tr
                            v-for="(item, index) in detailData?.flows"
                            :key="index"
                        >
                            <uni-td>{{ item.name }}</uni-td>
                            <uni-td>{{ item.model }}</uni-td>
                            <uni-td>{{ item.total_price }}</uni-td>
                        </uni-tr>
                    </uni-table>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { accountDetail } from '@/api/user'
import { useAppStore } from '@/stores/app'
import { useRoute } from 'uniapp-router-next'

const appStore = useAppStore()
const route = useRoute()
const id = ref('')

const type = route.query.type
const detailData = ref()

const tableData = ref([])

// 判断是否是智能体分成收益
const isRobotRevenue = (changeType: string) => {
    return changeType && changeType.includes('智能体分成收益')
}

const getData = async () => {
    detailData.value = await accountDetail({ id: id.value })
}

const getTable = async () => {}

onLoad((option: any) => {
    id.value = option.id
    getData()
})
</script>

<style lang="scss" scoped></style>
