<template>
	<view class="demo-block">
		<text class="demo-block__title-text ultra">水印</text>
		<text class="demo-block__desc-text">给页面或某个区域加上水印</text>	
		<view class="demo-block__body">
			<view class="demo-block card" v-if="!hasFullScreenl">
				<!-- <text class="demo-block__title-text large">基础</text> -->
				<view class="demo-block__body">
					<l-watermark content="LimeUi">
						<view class="content">
							<text style="padding: 20rpx;">这是很重要的内容</text>
							<button @click="onClick">基础</button>
						</view>
					</l-watermark>
					
				</view>	
			</view>	
			
			<view class="demo-block card" v-if="!hasFullScreenl">
				<!-- <text class="demo-block__title-text large">多行</text> -->
				<view class="demo-block__body">
					<l-watermark :content="content" :baseSize="1">
						<view class="content">
							<text style="padding: 20rpx;">这是很重要的内容</text>
							<button @click="onClick">多行</button>
						</view>
					</l-watermark>
					
				</view>	
			</view>	
			<!-- #ifdef APP-ANDROID -->
			<view class="demo-block card">
				<text class="demo-block__title-text large">全屏水印</text>
				<text class="demo-block__desc-text" style="padding-left: 20rpx;">仅支持安卓</text>	
				<view class="demo-block__body" style="padding:20rpx">
					<button @click="onAdd">增加全屏水印</button>
					<button style="margin-top: 20rpx;" @click="onRemove">删除全屏水印</button>
				</view>	
			</view>	
			<!-- #endif -->
			<!-- 暂不支持图片 -->
			<!-- <view class="demo-block card">
				<text class="demo-block__title-text large">图片</text>
				<view class="demo-block__body">
					
					<l-watermark image="https://img10.360buyimg.com/img/jfs/t1/182127/16/37474/11761/64659c31F0cd84976/21f25b952f03a49a.jpg" :width="60" :height="60">
						<view class="content">
							<text style="padding: 20rpx;">这是很重要的内容</text>
							<button @click="onClick">点击</button>
						</view>
					</l-watermark>
					
				</view>	
			</view>	 -->
		</view>	
	</view>
</template>

<script>
	// #ifdef APP-ANDROID
	import {createWatermark, removeWatermark} from '../l-watermark/full.uts'
	// #endif
	
	export default {
		data() {
			return {
				hasFullScreenl: false,
				content: ['LimeUi', '人生得意须尽欢']
			}
		},
		mounted() {
			setTimeout(()=>{
				this.content = ['LimeUiX', '人生得意须尽欢']
			},1000)
		},
		methods: {
			onRemove(){
				// #ifdef APP-ANDROID
				removeWatermark()
				this.hasFullScreenl = false
				// #endif
			},
			onAdd(){
				// #ifdef APP-ANDROID
				const watermark = createWatermark()
				this.hasFullScreenl = true
				// watermark.color = 'red'
				watermark.gap = [50, 50]
				watermark.setContent(['lime Ui','这是很重要的内容'])
				// #endif
			
			},
			onClick(){
				console.log('水印')
			}
		}
	}
</script>

<style lang="scss">
	.content {
		box-sizing: border-box;
		height: 400rpx; 
		// width: 100%; 
		// background-color: red;
		// background-color: rgba(0, 0, 0, 0.1);
		padding: 30rpx;
	}
	
	
	.demo-block {
		margin: 32px 20px 0;
		// overflow: visible;
		&.card{
			// padding: 20rpx;
			background-color: white;
			margin-bottom: 20rpx !important;
			.demo-block__title-text{
				 padding: 20rpx 20rpx 0;
			}
		}
		&__title {
			margin: 0;
			margin-top: 8px;
			&-text {
				color: rgba(0, 0, 0, 0.6);
				font-weight: 400;
				font-size: 14px;
				line-height: 16px;
				
				&.large {
					color: rgba(0, 0, 0, 0.9);
					font-size: 18px;
					font-weight: 700;
					line-height: 26px;
				}
				&.ultra {
					color: rgba(0, 0, 0, 0.9);
					font-size: 24px;
					font-weight: 700;
					line-height: 32px;
				}
			}
		}
		&__desc-text {
			color: rgba(0, 0, 0, 0.6);
			margin: 8px 16px 0 0;
			font-size: 14px;
			line-height: 22px;
		}
		&__body {
			margin: 16px 0;
			// overflow: visible;
			.demo-block {
				// margin-top: 0px;
				margin: 0;
			}
		}
	}
</style>