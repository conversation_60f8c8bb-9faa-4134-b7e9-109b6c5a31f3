function t(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
t=function(){return r};var r={},e=Object.prototype,n=e.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{u({},"")}catch(t){u=function(t,r,e){return t[r]=e}}function s(t,r,e,n){var o=Object.create((r&&r.prototype instanceof f?r:f).prototype),a=new M(n||[]);return o._invoke=function(t,r,e){var n="suspendedStart";return function(o,a){if("executing"===n)throw Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return S()}for(e.method=o,e.arg=a;;){var i=e.delegate;if(i){var c=b(i,e);if(c){if(c===h)continue;return c}}if("next"===e.method)e.sent=e._sent=e.arg;else if("throw"===e.method){if("suspendedStart"===n)throw n="completed",e.arg;e.dispatchException(e.arg)}else"return"===e.method&&e.abrupt("return",e.arg);n="executing";var u=l(t,r,e);if("normal"===u.type){if(n=e.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:e.done}}"throw"===u.type&&(n="completed",e.method="throw",e.arg=u.arg)}}}(t,e,a),o}function l(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=s;var h={};function f(){}function p(){}function v(){}var y={};u(y,a,(function(){return this}));var d=Object.getPrototypeOf,m=d&&d(d(E([])));m&&m!==e&&n.call(m,a)&&(y=m);var g=v.prototype=f.prototype=Object.create(y);function w(t){["next","throw","return"].forEach((function(r){u(t,r,(function(t){return this._invoke(r,t)}))}))}function x(t,r){function e(o,a,i,c){var u=l(t[o],t,a);if("throw"!==u.type){var s=u.arg,h=s.value;return h&&"object"==typeof h&&n.call(h,"__await")?r.resolve(h.__await).then((function(t){e("next",t,i,c)}),(function(t){e("throw",t,i,c)})):r.resolve(h).then((function(t){s.value=t,i(s)}),(function(t){return e("throw",t,i,c)}))}c(u.arg)}var o;this._invoke=function(t,n){function a(){return new r((function(r,o){e(t,n,r,o)}))}return o=o?o.then(a,a):a()}}function b(t,r){var e=t.iterator[r.method];if(void 0===e){if(r.delegate=null,"throw"===r.method){if(t.iterator.return&&(r.method="return",r.arg=void 0,b(t,r),"throw"===r.method))return h;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var n=l(e,t.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,h;var o=n.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,h):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}function k(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function L(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function E(t){if(t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var e=-1,o=function r(){for(;++e<t.length;)if(n.call(t,e))return r.value=t[e],r.done=!1,r;return r.value=void 0,r.done=!0,r};return o.next=o}}return{next:S}}function S(){return{value:void 0,done:!0}}return p.prototype=v,u(g,"constructor",v),u(v,"constructor",p),p.displayName=u(v,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===p||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,c,"GeneratorFunction")),t.prototype=Object.create(g),t},r.awrap=function(t){return{__await:t}},w(x.prototype),u(x.prototype,i,(function(){return this})),r.AsyncIterator=x,r.async=function(t,e,n,o,a){void 0===a&&(a=Promise);var i=new x(s(t,e,n,o),a);return r.isGeneratorFunction(e)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},w(g),u(g,c,"Generator"),u(g,a,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=[];for(var e in t)r.push(e);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},r.values=E,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function e(e,n){return i.type="throw",i.arg=t,r.next=e,n&&(r.method="next",r.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return e("end");if(this.prev>=a.tryLoc){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(a.catchLoc>this.prev)return e(a.catchLoc,!0);if(a.finallyLoc>this.prev)return e(a.finallyLoc)}else if(c){if(a.catchLoc>this.prev)return e(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(a.finallyLoc>this.prev)return e(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(this.prev>=o.tryLoc&&n.call(o,"finallyLoc")&&o.finallyLoc>this.prev){var a=o;break}}a&&("break"===t||"continue"===t)&&r>=a.tryLoc&&a.finallyLoc>=r&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=r,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(i)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),h},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),L(e),h}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;L(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,e){return this.delegate={iterator:E(t),resultName:r,nextLoc:e},"next"===this.method&&(this.arg=void 0),h}},r}function r(t,r,e,n,o,a,i){try{var c=t[a](i),u=c.value}catch(t){return void e(t)}c.done?r(u):Promise.resolve(u).then(n,o)}function e(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}function n(t,r){for(var e=0;r.length>e;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function o(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==e)return;var n,o,a=[],i=!0,c=!1;try{for(e=e.call(t);!(i=(n=e.next()).done)&&(a.push(n.value),!r||a.length!==r);i=!0);}catch(t){c=!0,o=t}finally{try{i||null==e.return||e.return()}finally{if(c)throw o}}return a}(t,r)||i(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t){return function(t){if(Array.isArray(t))return c(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||i(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t,r){if(t){if("string"==typeof t)return c(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?c(t,r):void 0}}function c(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);r>e;e++)n[e]=t[e];return n}var u={color:"rgba(0,0,0,.15)",fontSize:16,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"},s=function(){function i(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e(this,i),this.canvas=null,this.ctx=null,this.options={pixelRatio:1},this.props={rotate:-22,baseSize:2,fontGap:3,gap:[30,30]},this.gap={gapX:0,gapY:0,gapXCenter:0,gapYCenter:0,offsetLeft:0,offsetTop:0},this.font=u,this.appendWatermark=function(){},this.createImage=function(){return new Image},t?this.canvas=t:"undefined"!=typeof window&&(this.canvas=document.createElement("canvas"));var n=r.appendWatermark,o=r.createImage;n&&(this.appendWatermark=n),o&&(this.createImage=o),Object.assign(this.options,r)}var c,s,l,h,f;return c=i,s=[{key:"rotateWatermark",value:function(t,r,e,n){t.translate(r,e),t.rotate(Math.PI/180*Number(n)),t.translate(-r,-e)}},{key:"calcFont",value:function(){Object.assign(this.font,u,this.props.font||{})}},{key:"calcGap",value:function(){var t,r,e=this.props,n=e.gap,a=e.offset,i=o(void 0===n?[20,20]:n,2),c=i[0],u=i[1],s=c/2,l=u/2,h=null!==(t=null==a?void 0:a[0])&&void 0!==t?t:s,f=null!==(r=null==a?void 0:a[1])&&void 0!==r?r:l;this.gap={gapX:c,gapY:u,gapXCenter:s,gapYCenter:l,offsetLeft:h,offsetTop:f}}},{key:"getPixelRatio",value:function(){var t=this.options.pixelRatio;return t||("undefined"!=typeof window?window.devicePixelRatio:1)}},{key:"calcRotateMarkSize",value:function(t,r){var e=this.props.rotate;if(!e)return[t,r];var n=e*Math.PI/180,o=-t/2*Math.cos(n)+r/2*Math.sin(n),a=-t/2*Math.sin(n)-r/2*Math.cos(n),i=t/2*Math.cos(n)+r/2*Math.sin(n),c=t/2*Math.sin(n)-r/2*Math.cos(n),u=t/2*Math.cos(n)-r/2*Math.sin(n),s=t/2*Math.sin(n)+r/2*Math.cos(n),l=-t/2*Math.cos(n)-r/2*Math.sin(n),h=-t/2*Math.sin(n)+r/2*Math.cos(n);return[Math.round(Math.max(o,i,u,l)-Math.min(o,i,u,l)),Math.round(Math.max(a,c,s,h)-Math.min(a,c,s,h))]}},{key:"getMarkSize",value:function(t){var r=this.props,e=r.width,n=r.height,i=r.content,c=r.fontGap,u=this.font,s=u.fontSize,l=u.fontFamily,h=120,f=64;if(!r.image&&t.measureText){t.font="".concat(Number(s),"px ").concat(l);var p=Array.isArray(i)?i:[i],v=p.map((function(r){return t.measureText(r).width}));h=Math.ceil(Math.max.apply(Math,a(v)));var y=o(this.calcRotateMarkSize(h,f=Number(s)*p.length+(p.length-1)*c),2);h=y[0],f=y[1]}return[e||h,n||f]}},{key:"fillTexts",value:function(t,r,e,n,o){var a=this.getPixelRatio(),i=this.font,c=i.fontFamily,u=i.fontWeight,s=i.fontStyle,l=i.color,h=this.props,f=h.content,p=h.fontGap,v=Number(i.fontSize)*a;t.font="".concat(s," normal ").concat(u," ").concat(v,"px/").concat(o,"px ").concat(c),t.fillStyle=l,t.textAlign="center",t.textBaseline="top",t.translate(n/2,0);var y=Array.isArray(f)?f:[f];null==y||y.forEach((function(n,o){t.fillText(null!=n?n:"",r,e+o*(v+p*a))}))}},{key:"toDataURL",value:function(t){try{var r=t.toDataURL();return"string"==typeof r?Promise.resolve(r):r}catch(t){return console.warn(t),Promise.reject(t)}}},{key:"drawText",value:function(t,r,e,n,o,a,i,c,u,s,l){var h=this,f=this.props.rotate;this.fillTexts(r,e,n,o,a),r.restore(),this.rotateWatermark(r,i,c,f),this.fillTexts(r,u,s,o,a),this.draw(r),this.toDataURL(t).then((function(t){return h.appendWatermark(t,l,h.gap)}))}},{key:"draw",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};t.draw?t.draw(!1,r):r()}},{key:"render",value:(h=t().mark((function r(e){var n,a,i,c,u,s,l,h,f,p,v,y,d,m,g,w,x,b,k,L,M,E,S,j,O,P,T,I=this;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(Object.assign(this.props,e||{}),this.calcFont(),this.calcGap(),n=this.canvas){t.next=6;break}return t.abrupt("return");case 6:if(a=n.getContext("2d"),this.ctx=a,!a){t.next=33;break}if(i=this.getPixelRatio(),c=this.getMarkSize(a),u=o(c,2),y=(v=this.props).rotate,d=v.image,w=((p=(h=this.gap).gapY)+(l=u[1]))*i,n.width=(g=((f=h.gapX)+(s=u[0]))*i)*(m=v.baseSize),n.height=w*m,!("draw"in a)){t.next=20;break}return t.next=20,new Promise((function(t){return setTimeout(t,100)}));case 20:S=(x=f*i/2)+g,j=(b=p*i/2)+w,O=(M=((k=s*i)+f*i)/2)+g,P=(E=((L=l*i)+p*i)/2)+w,a.save(),this.rotateWatermark(a,M,E,y),d?((T=this.createImage(this.canvas)).onload=function(){var t="path"in T?T.path:T;a.drawImage(t,x,b,k,L),a.restore(),I.rotateWatermark(a,O,P,y),a.drawImage(t,S,j,k,L),I.draw(a,(function(){return I.toDataURL(n).then((function(t){return I.appendWatermark(t,s,I.gap)}))}))},T.onerror=function(){return I.drawText(n,a,x,b,k,L,O,P,S,j,s)},T.crossOrigin="anonymous",T.referrerPolicy="no-referrer",T.src=d):this.drawText(n,a,x,b,k,L,O,P,S,j,s);case 33:case"end":return t.stop()}}),r,this)})),f=function(){var t=this,e=arguments;return new Promise((function(n,o){var a=h.apply(t,e);function i(t){r(a,n,o,i,c,"next",t)}function c(t){r(a,n,o,i,c,"throw",t)}i(void 0)}))},function(t){return f.apply(this,arguments)})}],s&&n(c.prototype,s),l&&n(c,l),Object.defineProperty(c,"prototype",{writable:!1}),i}();export{s as Watermark};
