{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"svgicon": "node ./src/uni_modules/zui-svg-icon/tools/generate-svg-icon.js", "init": "node initialize.js", "dev": "node scripts/develop.js", "dev:app": "uni -p app", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baid2u", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build": "node scripts/publish.js", "build:app": "uni build -p app", "build:custom": "uni build -p", "build:h5": "uni build && node scripts/release.mjs -t h5 -o mobile", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin && node scripts/release.mjs -t mp-weixin -o weapp", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-3070920230324001", "@dcloudio/uni-app-plus": "3.0.0-3070920230324001", "@dcloudio/uni-components": "3.0.0-3070920230324001", "@dcloudio/uni-h5": "3.0.0-3070920230324001", "@dcloudio/uni-mp-alipay": "3.0.0-3070920230324001", "@dcloudio/uni-mp-baidu": "3.0.0-3070920230324001", "@dcloudio/uni-mp-jd": "3.0.0-3070920230324001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3070920230324001", "@dcloudio/uni-mp-lark": "3.0.0-3070920230324001", "@dcloudio/uni-mp-qq": "3.0.0-3070920230324001", "@dcloudio/uni-mp-toutiao": "3.0.0-3070920230324001", "@dcloudio/uni-mp-weixin": "3.0.0-3070920230324001", "@dcloudio/uni-quickapp-webview": "3.0.0-3070920230324001", "@dcloudio/uni-webview-js": "^0.0.3", "@fingerprintjs/fingerprintjs": "^4.3.0", "@vscode/markdown-it-katex": "^1.1.0", "@vueuse/core": "9.3.0", "css-color-function": "^1.3.3", "fontfaceobserver": "^2.3.0", "github-markdown-css": "^5.2.0", "highlight.js": "11.0.0", "howler": "^2.2.4", "joplin-turndown-plugin-gfm": "^1.0.12", "js-mp3": "^0.1.0", "jsonc-parser": "^3.2.1", "lodash-es": "^4.17.21", "mammoth": "^1.6.0", "markdown-it": "^13.0.1", "markdown-it-math": "^4.1.1", "markmap-common": "^0.15.3", "markmap-lib": "^0.15.4", "markmap-view": "^0.15.4", "mathjs": "^11.8.0", "npm": "^8.19.4", "papaparse": "^5.4.1", "pdfjs-dist": "^2.10.377", "pinia": "2.0.20", "recorder-core": "^1.3.23122400", "svgo": "^3.3.2", "turndown": "^7.2.0", "uniapp-router-next": "^1.2.7", "uniapp-router-next-zm": "^1.0.1", "vconsole": "^3.14.6", "video.js": "^8.12.0", "videojs-mobile-ui": "^0.7.0", "vue": "^3.2.45", "vue-i18n": "^9.1.9", "weixin-js-sdk": "^1.6.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.0/xlsx-0.20.0.tgz", "z-paging": "^2.7.11"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-3070920230324001", "@dcloudio/uni-cli-shared": "3.0.0-3070920230324001", "@dcloudio/uni-stacktracey": "3.0.0-3070920230324001", "@dcloudio/vite-plugin-uni": "3.0.0-3070920230324001", "@rushstack/eslint-patch": "^1.1.4", "@types/howler": "^2.2.11", "@types/lodash-es": "^4.17.6", "@types/markdown-it": "^12.2.3", "@types/node": "^18.7.16", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "autoprefixer": "^10.4.8", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.4.0", "execa": "^6.1.0", "fs-extra": "^10.1.0", "minimist": "^1.2.8", "postcss": "^8.4.16", "postcss-rem-to-responsive-pixel": "^5.1.3", "prettier": "^2.7.1", "sass": "1.54.5", "tailwindcss": "^3.3.2", "typescript": "^4.7.4", "unplugin-uni-router": "^1.2.7", "vite": "4.1.4", "weapp-tailwindcss-webpack-plugin": "1.12.8"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}