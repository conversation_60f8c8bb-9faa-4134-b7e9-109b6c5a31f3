import request from '@/utils/request'

// 获取示例库类别列表
export function getCategoryList(params: any) {
    return request.get({ url: '/kb.example_category/lists', params })
}

// 获取示例库类别详情
export function getCategoryDetail(params: any) {
    return request.get({ url: '/kb.example_category/detail', params })
}

// 添加示例库类别
export function addCategory(params: any) {
    return request.post({ url: '/kb.example_category/add', params })
}

// 编辑示例库类别
export function editCategory(params: any) {
    return request.post({ url: '/kb.example_category/edit', params })
}

// 删除示例库类别
export function deleteCategory(params: any) {
    return request.post({ url: '/kb.example_category/del', params })
}

// 修改示例库类别状态
export function updateCategoryStatus(params: any) {
    return request.post({ url: '/kb.example_category/status', params })
}

// 获取类别下拉选择列表
export function getCategorySelectList() {
    return request.get({ url: '/kb.example_category/getSelectList' })
} 