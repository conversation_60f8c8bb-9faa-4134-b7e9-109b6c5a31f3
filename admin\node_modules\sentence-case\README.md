# Sentence Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Transform into a lower case with spaces between words, then capitalize the string.

## Installation

```
npm install sentence-case --save
```

## Usage

```js
import { sentenceCase } from "sentence-case";

sentenceCase("string"); //=> "String"
sentenceCase("dot.case"); //=> "Dot case"
sentenceCase("PascalCase"); //=> "Pascal case"
sentenceCase("version 1.2.10"); //=> "Version 1 2 10"
```

The function also accepts [`options`](https://github.com/blakeembrey/change-case#options).

## License

MIT

[npm-image]: https://img.shields.io/npm/v/sentence-case.svg?style=flat
[npm-url]: https://npmjs.org/package/sentence-case
[downloads-image]: https://img.shields.io/npm/dm/sentence-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/sentence-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/sentence-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=sentence-case
