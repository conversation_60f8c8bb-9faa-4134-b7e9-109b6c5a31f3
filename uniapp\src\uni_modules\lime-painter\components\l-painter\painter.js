var t=function(){return t=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},t.apply(this,arguments)};function e(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{h(n.next(t))}catch(t){o(t)}}function a(t){try{h(n.throw(t))}catch(t){o(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))}function i(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}var n={MP_WEIXIN:"mp-weixin",MP_QQ:"mp-qq",MP_ALIPAY:"mp-alipay",MP_BAIDU:"mp-baidu",MP_TOUTIAO:"mp-toutiao",MP_DINGDING:"mp-dingding",H5:"h5",WEB:"web",PLUS:"plus"},r=["contentSize","clientSize","borderSize","offsetSize"],o="row",s="column",a="top",h="middle",c="bottom",d="left",l="center",f="right",u="view",p="text",g="image",v="qrcode",y="block",b="inline-block",x="none",m="flex",w="absolute",S="fixed",z={display:y,color:"#000000",lineHeight:"1.4em",fontSize:14,fontWeight:400,fontFamily:"sans-serif",lineCap:"butt",flexDirection:o,flexWrap:"nowrap",textAlign:"left",alignItems:"flex-start",justifyContent:"flex-start",position:"static",transformOrigin:"center center"},k={upx2px:function(t){return window.innerWidth/750*t},getSystemInfoSync:function(){return{screenWidth:window.innerWidth,screenHeight:window.innerHeight}},getImageInfo:function(t){var e=t.src,i=t.success,n=t.fail,r=new Image;r.onload=function(){i({width:r.naturalWidth,height:r.naturalHeight,path:r.src,src:e})},r.onerror=n,r.src=e}},M="object"==typeof window?"undefined"==typeof uni||"undefined"!=typeof uni&&!uni.addInterceptor?n.WEB:n.H5:"object"==typeof swan?n.MP_BAIDU:"object"==typeof tt?n.MP_TOUTIAO:"object"==typeof plus?n.PLUS:"object"==typeof wx?n.MP_WEIXIN:void 0,I=M==n.MP_WEIXIN?wx:"undefined"!=typeof uni?uni.getImageInfo?{upx2px:function(t){return uni.upx2px(t)},getSystemInfoSync:function(){return uni.getSystemInfoSync()},getImageInfo:function(t){return uni.getImageInfo(t)},downloadFile:function(t){return uni.downloadFile(t)}}:Object.assign(uni,k):"undefined"!=typeof window?k:uni;if(!I.upx2px){var B=((I.getSystemInfoSync&&uni.getSystemInfoSync()).screenWidth||375)/750;I.upx2px=function(t){return B*t}}function W(t){return/^-?\d+(\.\d+)?$/.test(t)}function P(t,e,i){if("number"==typeof t)return t;if(W(t))return 1*t;if("string"==typeof t){var n=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|vw|vh|px|%)$/g.exec(t);if(!t||!n)return 0;var r=n[3];t=parseFloat(t);var o=0;if("rpx"===r)o=I.upx2px(t);else if("px"===r)o=1*t;else if("%"===r&&e)o=t*P(e)/100;else if("em"===r&&e)o=t*P(e||14);else if(["vw","vh"].includes(r)){var s=I.getSystemInfoSync(),a=s.screenWidth,h=s.screenHeight;o=t*("vw"==r?a:h)/100}return 1*o.toFixed(2)}return 0}function R(t){return/%$/.test(t)}var L=function(t){return!(!t||!t.startsWith("linear")&&!t.startsWith("radial"))},O=function(t,e,i,n,r,o){t.startsWith("linear")?function(t,e,i,n,r,o){for(var s=function(t,e,i,n,r){void 0===n&&(n=0);void 0===r&&(r=0);var o=t.match(/([-]?\d{1,3})deg/),s=o&&o[1]?parseFloat(o[1]):0;s>=360&&(s-=360);s<0&&(s+=360);if(0===(s=Math.round(s)))return{x0:Math.round(e/2)+n,y0:i+r,x1:Math.round(e/2)+n,y1:r};if(180===s)return{x0:Math.round(e/2)+n,y0:r,x1:Math.round(e/2)+n,y1:i+r};if(90===s)return{x0:n,y0:Math.round(i/2)+r,x1:e+n,y1:Math.round(i/2)+r};if(270===s)return{x0:e+n,y0:Math.round(i/2)+r,x1:n,y1:Math.round(i/2)+r};var a=Math.round(180*Math.asin(e/Math.sqrt(Math.pow(e,2)+Math.pow(i,2)))/Math.PI);if(s===a)return{x0:n,y0:i+r,x1:e+n,y1:r};if(s===180-a)return{x0:n,y0:r,x1:e+n,y1:i+r};if(s===180+a)return{x0:e+n,y0:r,x1:n,y1:i+r};if(s===360-a)return{x0:e+n,y0:i+r,x1:n,y1:r};var h=0,c=0,d=0,l=0;if(s<a||s>180-a&&s<180||s>180&&s<180+a||s>360-a){var f=s*Math.PI/180,u=s<a||s>360-a?i/2:-i/2,p=Math.tan(f)*u,g=s<a||s>180-a&&s<180?e/2-p:-e/2-p;h=-(d=p+(v=Math.pow(Math.sin(f),2)*g)),c=-(l=u+v/Math.tan(f))}if(s>a&&s<90||s>90&&s<90+a||s>180+a&&s<270||s>270&&s<360-a){var v;f=(90-s)*Math.PI/180,p=s>a&&s<90||s>90&&s<90+a?e/2:-e/2,u=Math.tan(f)*p,g=s>a&&s<90||s>270&&s<360-a?i/2-u:-i/2-u;h=-(d=p+(v=Math.pow(Math.sin(f),2)*g)/Math.tan(f)),c=-(l=u+v)}return h=Math.round(h+e/2)+n,c=Math.round(i/2-c)+r,d=Math.round(d+e/2)+n,l=Math.round(i/2-l)+r,{x0:h,y0:c,x1:d,y1:l}}(r,t,e,i,n),a=s.x0,h=s.y0,c=s.x1,d=s.y1,l=o.createLinearGradient(a,h,c,d),f=r.match(/linear-gradient\((.+)\)/)[1],u=T(f.substring(f.indexOf(",")+1)),p=0;p<u.colors.length;p++)l.addColorStop(u.percents[p],u.colors[p]);o.setFillStyle(l)}(e,i,n,r,t,o):t.startsWith("radial")&&function(t,e,i,n,r,o){for(var s=T(r.match(/radial-gradient\((.+)\)/)[1]),a=Math.round(t/2)+i,h=Math.round(e/2)+n,c=o.createRadialGradient(a,h,0,a,h,Math.max(t,e)/2),d=0;d<s.colors.length;d++)c.addColorStop(s.percents[d],s.colors[d]);o.setFillStyle(c)}(e,i,n,r,t,o)};function T(t){for(var e=[],i=[],n=0,r=t.substring(0,t.length-1).split("%,");n<r.length;n++){var o=r[n];e.push(o.substring(0,o.lastIndexOf(" ")).trim()),i.push(o.substring(o.lastIndexOf(" "),o.length)/100)}return{colors:e,percents:i}}function F(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function A(){return A=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},A.apply(this,arguments)}function j(t,e){return j=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},j(t,e)}function C(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function E(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return C(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?C(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function H(t){return"number"==typeof t}function Y(t){return"auto"===t||null===t}function D(t){return/%$/.test(t)}var $=g,U=p,X=u,_=v,N=b,V=w,G=S;var q,J=0,Q=function(){function t(){F(this,"elements",[]),F(this,"afterElements",[]),F(this,"beforeElements",[]),F(this,"ids",[]),F(this,"width",0),F(this,"height",0),F(this,"top",0),F(this,"left",0),F(this,"pre",null),F(this,"offsetX",0),F(this,"offsetY",0),J++,this.id=J}var e=t.prototype;return e.fixedBind=function(t,e){void 0===e&&(e=0),this.container=e?t.parent:t.root,this.container.fixedLine=this,this.fixedAdd(t)},e.fixedAdd=function(t){this.elements.push(t);var e=t.computedStyle.zIndex;(void 0===e?0:e)>=0?this.afterElements.push(t):this.beforeElements.push(t),this.refreshLayout()},e.bind=function(t){this.container=t.parent,this.container.line=null,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),this.isInline=t.isInline(),this.container.line=this,this.outerWidth=t.parent&&t.parent.contentSize.width?t.parent.contentSize.width:1/0,this.add(t)},e.getPreLine=function(){return this.container.lines[this.container.lines.length-2]},e.canIEnter=function(t){return this.outerWidth||t.parent&&t.parent.contentSize.width,!((100*t.offsetSize.width+100*this.width)/100>this.outerWidth)||(this.closeLine(),!1)},e.closeLine=function(){delete this.container.line},e.add=function(t){this.ids.includes(t.id)||(this.ids.push(t.id),this.elements.push(t),this.refreshWidthHeight(t))},e.refreshWidthHeight=function(t){t.offsetSize.height>this.height&&(this.height=t.offsetSize.height),this.width+=t.offsetSize.width||0,(this.container.lineMaxWidth||0)<this.width&&(this.container.lineMaxWidth=this.width)},e.refreshXAlign=function(){if(this.isInline){var t=this.container.contentSize.width-this.width,e=this.container.style.textAlign;"center"===e?t/=2:"left"===e&&(t=0),this.offsetX=t}},e.getOffsetY=function(t){if(!t||!t.style)return 0;var e=(t.style||{}).verticalAlign;return"bottom"===e?this.height-t.contentSize.height:"middle"===e?(this.height-t.contentSize.height)/2:0},e.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){var n=i.elements[e-1],r=i.getOffsetY(t);t.style.top=i.top+r,t.style.left=n?n.offsetSize.left+n.offsetSize.width:i.left,t.getBoxPosition()}))},e.refreshLayout=function(){this.afterElements=this.afterElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex})),this.beforeElements=this.beforeElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex}))},t}(),Z=((q={})[o]={width:"width",contentWidth:"width",lineMaxWidth:"lineMaxWidth",left:"left",top:"top",height:"height",lineMaxHeight:"lineMaxHeight",marginLeft:"marginLeft"},q[s]={width:"height",contentWidth:"height",lineMaxWidth:"lineMaxHeight",left:"top",top:"left",height:"width",lineMaxHeight:"lineMaxWidth",marginLeft:"marginTop"},q),K=function(t){var e,i;function n(){var e;return F(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e=t.call(this)||this),"outerWidth",0),e.exactValue=0,e.flexTotal=0,e.width=0,e.key=null,e.flexDirection="row",e}i=t,(e=n).prototype=Object.create(i.prototype),e.prototype.constructor=e,j(e,i);var r=n.prototype;return r.bind=function(t){console.warn("[painter] flex-box 功能未完善谨慎使用"),this.container=t.parent,this.container.line=this,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),t.parent&&(this.flexDirection=t.parent.style.flexDirection,this.key=Z[this.flexDirection]),this.initHeight(t),this.outerWidth=t.parent&&t.parent.contentSize[this.key.contentWidth]?t.parent.contentSize[this.key.contentWidth]:1/0,this.add(t)},r.add=function(t){this.ids.push(t.id),H(t.style.flex)?this.flexTotal+=t.style.flex:H(t.style[this.key.width])&&(this.exactValue+=t.contentSize[this.key.width]||0),this.elements.push(t),this.refreshWidthHeight(t),t.next||this.closeLine()},r.closeLine=function(){this.calcFlex()},r.initHeight=function(t){this[this.key.height]=0},r.calcFlex=function(){var t=this,e=this.container.contentSize[this.key.contentWidth],i=0;this.elements.forEach((function(n){var r=n.style[t.key.width]||n.contentSize[t.key.width]||0;H(n.style.flex)&&(r=n.style.flex/t.flexTotal*(e-t.exactValue)),n.computedStyle[t.key.width]=r,n.isFlexCalc=!0,delete n.line,delete n.lines,delete n.lineMaxWidth,n.getBoxWidthHeight(),i=Math.max(i,n.offsetSize[t.key.height])})),this[this.key.height]=i},r.refreshWidthHeight=function(t){this.container.style.alignItems&&!t.style.alignSelf&&(t.style.alignSelf=this.container.style.alignItems),t.offsetSize[this.key.height]>this[this.key.height]&&(this.container[this.key.lineMaxHeight]=this[this.key.height]=t.offsetSize[this.key.height]),this[this.key.width]+=t.offsetSize[this.key.width]||0;var e=Math.min(this[this.key.width],!this.container.contentSize[this.key.width]&&1/0);(this.container[this.key.lineMaxWidth]||0)<e&&(this.container[this.key.lineMaxWidth]=e)},r.refreshXAlign=function(){var t=this,e=this.elements.reduce((function(e,i){return e+i.offsetSize[t.key.width]}),0),i=(this.outerWidth==1/0?0:this.outerWidth-e)||0,n=this.container.style.justifyContent;"center"===n?i/=2:"flex-start"===n?i=0:["space-between","space-around"].includes(n)&&(!function(e,i){void 0===i&&(i=0),i/=t.elements.length+(e?-1:1),t.elements.forEach((function(n,r){var o;e&&!r||(n.style.margin?n.style.margin[t.key.marginLeft]+=i:n.style.margin=((o={})[t.key.marginLeft]=i,o),n.getBoxPosition())})),i=0}("space-between"==n,i),i=0),this.offsetX=i||0,this.refreshYAlign()},r.refreshYAlign=function(){var t=this;if(1==this.container.lines.length)return 0;var e=this.container.lines.reduce((function(e,i){return e+i[t.key.height]}),0);if("center"===this.container.style.alignItems){var i=(this.container.contentSize[this.key.height]-e)/(this.container.lines.length+1);this.container.lines.forEach((function(t){t.offsetY=i}))}if("flex-end"===this.container.style.alignItems){var n=this.container.contentSize[this.key.height]-e;this.container.lines[0].offsetY=n}},r.getOffsetY=function(t){return this.container.lines.length>1?0:"flex-end"===t.style.alignSelf?this.container.contentSize[this.key.height]-t.offsetSize[this.key.height]:"center"===t.style.alignSelf?(this.container.contentSize[this.key.height]-t.offsetSize[this.key.height])/2:0},r.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){var n=i.elements[e-1],r=i.getOffsetY(t);t.style[i.key.top]=i[i.key.top]+r,t.style[i.key.left]=n?n.offsetSize[i.key.left]+n.offsetSize[i.key.width]:i[i.key.left],t.getBoxPosition()}))},n}(Q),et=g,it=p,nt=u,rt=y,ot=b,st=m,at=w,ht=S,ct=0,dt={left:null,top:null,width:null,height:null},lt=function(){function e(e,i,n,r){var o=this;F(this,"id",ct++),F(this,"style",{left:null,top:null,width:null,height:null}),F(this,"computedStyle",{}),F(this,"originStyle",{}),F(this,"children",{}),F(this,"layoutBox",A({},dt)),F(this,"contentSize",A({},dt)),F(this,"clientSize",A({},dt)),F(this,"borderSize",A({},dt)),F(this,"offsetSize",A({},dt)),this.ctx=r,this.root=n,i&&(this.parent=i),this.name=e.name||e.type,this.attributes=this.getAttributes(e);var s=function(e,i){var n=["color","fontSize","lineHeight","verticalAlign","fontWeight","textAlign"],r=e.css,o=void 0===r?{}:r,s=e.type,a=void 0===s?X:s,h=t({},z);if([U,$,_].includes(a)&&!o.display&&(h.display=N),i)for(var c=0;c<n.length;c++){var d=n[c];(o[d]||i[d])&&(o[d]=o[d]||i[d])}for(var l=function(t){var e,i,n,r,s=o[t];if(/-/.test(t)&&(t=t.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()})),h.key=s),/^(box|text)?shadow$/i.test(t)){var c=[];return s.replace(/((-?\d+(rpx|px|vw|vh)?\s+?){3})(.+)/,(function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];c=t[1].match(/-?\d+(rpx|px|vh|vw)?/g).map((function(t){return P(t)})).concat(t[4])})),/^text/.test(t)?h.textShadow=c:h.boxShadow=c,"continue"}if(/^border/i.test(t)&&!/radius$/i.test(t)){var d=t.match(/^border([BTRLa-z]+)?/)[0],l=t.match(/[W|S|C][a-z]+/),f=s.replace(/([\(,])\s+|\s+([\),])/g,"$1$2").split(" ").map((function(t){return/^\d/.test(t)?P(t,""):t}));return h[d]||(h[d]={}),1==f.length&&l?h[d][d+l[0]]=f[0]:h[d]=((e={})[d+"Width"]=W(f[0])?f[0]:0,e[d+"Style"]=f[1]||"solid",e[d+"Color"]=f[2]||"black",e),"continue"}if(/^background(color)?$/i.test(t))return h.backgroundColor=s,"continue";if(/^objectPosition$/i.test(t))return h[t]=s.split(" "),"continue";if(/padding|margin|radius/i.test(t)){var u=/radius$/i.test(t),p=u?"borderRadius":t.match(/[a-z]+/)[0],g=[0,0,0,0].map((function(t,e){return u?["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"][e]:[p+"Top",p+"Right",p+"Bottom",p+"Left"][e]})),v="margin";if("padding"===t||t===v||/^(border)?radius$/i.test(t)){f="".concat(s).split(" ").map((function(e){return/^-?\d+(rpx|px|vh|vw)?$/.test(e)?P(e):t!=v&&/auto/.test(e)?0:e}),[])||[0];var y=u?"borderRadius":t,b=f[0],x=f[1],m=f[2],w=f[3];h[y]=((i={})[g[0]]=Y(b)?0:b,i[g[1]]=W(x)||Y(x)?x:b,i[g[2]]=Y(W(m)?m:b)?0:W(m)?m:b,i[g[3]]=W(w)?w:x||b,i)}else"object"==typeof h[p]||(h[p]=((n={})[g[0]]=h[p]||0,n[g[1]]=h[p]||0,n[g[2]]=h[p]||0,n[g[3]]=h[p]||0,n)),h[p][t]=p==v&&Y(s)||D(s)?s:P(s);return"continue"}if(/^transform$/i.test(t))return h[t]={},s.replace(/([a-zA-Z]+)\(([0-9,-\.%rpxdeg\s]+)\)/g,(function(e,i,n){var r=n.split(",").map((function(t){return t.replace(/(^\s*)|(\s*$)/g,"")})),s=function(t,e){return t.includes("deg")?1*t:e&&!D(e)?P(t,e):t};i.includes("matrix")?h[t][i]=r.map((function(t){return 1*t})):i.includes("rotate")?h[t][i]=1*n.match(/^-?\d+(\.\d+)?/)[0]:/[X, Y]/.test(i)?h[t][i]=/[X]/.test(i)?s(r[0],o.width):s(r[0],o.height):(h[t][i+"X"]=s(r[0],o.width),h[t][i+"Y"]=s(r[1]||r[0],o.height))})),"continue";if(/^font$/i.test(t)&&console.warn("font 不支持简写"),/^textstroke/i.test(t)){var S=t.match(/color|width|type$/i),z=(d="textStroke",s.split(" ").map((function(t){return/^\d+(rpx|px|vh|vw)?$/.test(t)?P(t):t})));return S?h[d]?h[d][S[0]]=z[0]:h[d]=((r={})[S[0]]=z[0],r):h[d]={width:z[0],color:z[1],type:z[2]},"continue"}/^left|top$/i.test(t)&&![V,G].includes(o.position)?h[t]=0:h[t]=/^-?[\d\.]+(px|rpx|vw|vh)?$/.test(s)?P(s):/em$/.test(s)&&a==U?P(s,o.fontSize):s},f=0,u=Object.keys(o);f<u.length;f++)l(u[f]);return h}(e,i&&i.computedStyle);this.isAbsolute=s.position==at,this.isFixed=s.position==ht,this.originStyle=s,Object.keys(s).forEach((function(t){Object.defineProperty(o.style,t,{configurable:!0,enumerable:!0,get:function(){return s[t]},set:function(e){s[t]=e}})}));var a={contentSize:A({},this.contentSize),clientSize:A({},this.clientSize),borderSize:A({},this.borderSize),offsetSize:A({},this.offsetSize)};Object.keys(a).forEach((function(t){Object.keys(o[t]).forEach((function(e){Object.defineProperty(o[t],e,{configurable:!0,enumerable:!0,get:function(){return a[t][e]},set:function(i){a[t][e]=i}})}))})),this.computedStyle=this.style}var i=e.prototype;return i.add=function(t){t.parent=this,this.children[t.id]=t},i.getChildren=function(){var t=this;return Object.keys(this.children).map((function(e){return t.children[e]}))},i.prev=function(t){void 0===t&&(t=this);var e=t.parent.getChildren();return e[e.findIndex((function(e){return e.id==t.id}))-1]},i.getLineRect=function(t,e){var i={width:0,height:0},n=e?e.lines:this.parent&&this.parent.lines;return n&&n.find((function(e){return e.ids.includes(t)}))||i},i.setPosition=function(t,e){var i={left:"width",top:"height",right:"width",bottom:"height"};Object.keys(i).forEach((function(n){var r="right"==n?"left":"top";["right","bottom"].includes(n)&&void 0!==t.style[n]&&"number"!=typeof t.originStyle[r]?t.style[r]=e[i[n]]-t.offsetSize[i[n]]-P(t.style[n],e[i[n]]):t.style[n]=P(t.style[n],e[i[n]])}))},i.getAttributes=function(t){var e=t.attributes||{};return(t.url||t.src)&&(e.src=e.src||t.url||t.src),t.replace&&(e.replace=t.replace),t.text&&(e.text=t.text),e},i.getOffsetSize=function(t,e,i){void 0===i&&(i=r[3]);var n=e||{},o=n.margin,s=(o=void 0===o?{}:o).marginLeft,a=void 0===s?0:s,h=o.marginTop,c=void 0===h?0:h,d=o.marginRight,l=void 0===d?0:d,f=o.marginBottom,u=void 0===f?0:f,p=n.padding,g=(p=void 0===p?{}:p).paddingLeft,v=void 0===g?0:g,y=p.paddingTop,b=void 0===y?0:y,x=p.paddingRight,m=void 0===x?0:x,w=p.paddingBottom,S=void 0===w?0:w,z=n.border,k=(z=void 0===z?{}:z).borderWidth,M=void 0===k?0:k,I=n.borderTop,B=(I=void 0===I?{}:I).borderTopWidth,W=void 0===B?M:B,P=n.borderBottom,R=(P=void 0===P?{}:P).borderBottomWidth,L=void 0===R?M:R,O=n.borderRight,T=(O=void 0===O?{}:O).borderRightWidth,F=void 0===T?M:T,A=n.borderLeft,j=(A=void 0===A?{}:A).borderLeftWidth,C=void 0===j?M:j,E=a<0&&l<0?Math.abs(a+l):0,H=c<0&&u<0?Math.abs(c+u):0,Y=a>=0&&l<0,D=c>=0&&u<0;return i==r[0]&&(this[i].left=t.left+a+v+C+(Y?2*-l:0),this[i].top=t.top+c+b+W+(D?2*-u:0),this[i].width=t.width+(this[i].widthAdd?0:E),this[i].height=t.height+(this[i].heightAdd?0:H),this[i].widthAdd=E,this[i].heightAdd=H),i==r[1]&&(this[i].left=t.left+a+C+(Y<0?-l:0),this[i].top=t.top+c+W+(D?-u:0),this[i].width=t.width+v+m,this[i].height=t.height+b+S),i==r[2]&&(this[i].left=t.left+a+C/2+(Y<0?-l:0),this[i].top=t.top+c+W/2+(D?-u:0),this[i].width=t.width+v+m+C/2+F/2,this[i].height=t.height+b+S+L/2+W/2),i==r[3]&&(this[i].left=t.left+(Y<0?-l:0),this[i].top=t.top+(D?-u:0),this[i].width=t.width+v+m+C+F+a+l,this[i].height=t.height+b+S+L+W+u+c),this[i]},i.layoutBoxUpdate=function(t,e,i,n){var o=this;if(void 0===i&&(i=-1),"border-box"==e.boxSizing){var s=e||{},a=s.border,h=(a=void 0===a?{}:a).borderWidth,c=void 0===h?0:h,d=s.borderTop,l=(d=void 0===d?{}:d).borderTopWidth,f=void 0===l?c:l,u=s.borderBottom,p=(u=void 0===u?{}:u).borderBottomWidth,g=void 0===p?c:p,v=s.borderRight,y=(v=void 0===v?{}:v).borderRightWidth,b=void 0===y?c:y,x=s.borderLeft,m=(x=void 0===x?{}:x).borderLeftWidth,w=void 0===m?c:m,S=s.padding,z=(S=void 0===S?{}:S).paddingTop,k=void 0===z?0:z,M=S.paddingRight,I=void 0===M?0:M,B=S.paddingBottom,W=void 0===B?0:B,P=S.paddingLeft,R=void 0===P?0:P;i||(t.width-=R+I+b+w),1!==i||n||(t.height-=k+W+f+g)}this.layoutBox&&(r.forEach((function(i){return o.layoutBox[i]=o.getOffsetSize(t,e,i)})),this.layoutBox=Object.assign({},this.layoutBox,this.layoutBox.borderSize))},i.getBoxPosition=function(){var t=this.computedStyle,e=this.fixedLine,i=this.lines,n=t.left,r=void 0===n?0:n,o=t.top,s=void 0===o?0:o,a=t.padding||{},h=a.paddingBottom,c=void 0===h?0:h,d=a.paddingRight,l=void 0===d?0:d,f=A({},this.contentSize,{left:r,top:s}),u=this.contentSize.top-this.offsetSize.top,p=this.contentSize.left-this.offsetSize.left;if(this.root.fixedLine&&!this.root.isDone){this.root.isDone=!0;for(var g,v=E(this.root.fixedLine.elements);!(g=v()).done;){var y=g.value;y.setPosition(y,this.root.offsetSize),y.getBoxPosition()}}if(e)for(var b,x=E(e.elements);!(b=x()).done;){var m=b.value;m.setPosition(m,f),m.style.left+=r+p+l,m.style.top+=s+u+c,m.getBoxPosition()}if(i)for(var w,S=E(i);!(w=S()).done;){w.value.layout(f.top+u,f.left+p)}return this.layoutBoxUpdate(f,t),this.layoutBox},i.getBoxState=function(t,e){return this.isBlock(t)||this.isBlock(e)},i.isBlock=function(t){return void 0===t&&(t=this),t&&t.style.display==rt},i.isFlex=function(t){return void 0===t&&(t=this),t&&t.style.display==st},i.isInFlow=function(){return!(this.isAbsolute||this.isFixed)},i.inFlexBox=function(t){return void 0===t&&(t=this),!!t.isInFlow()&&(!!t.parent&&(!(!t.parent||t.parent.style.display!==st)||void 0))},i.isInline=function(t){return void 0===t&&(t=this),t&&t.style.display==ot},i.contrastSize=function(t,e,i){var n=t;return i&&(n=Math.min(n,i)),e&&(n=Math.max(n,e)),n},i.measureText=function(t,e){var i=this.ctx.measureText(t),n=i.width,r=i.actualBoundingBoxAscent,o=i.actualBoundingBoxDescent;return{ascent:r,descent:o,width:n,fontHeight:r+o||.7*e+1}},i.getBoxWidthHeight=function(){var t=this,e=this.name,i=this.computedStyle,n=this.attributes,r=this.parent,o=void 0===r?{}:r,s=this.ctx,a=this.getChildren(),h=i.left,c=void 0===h?0:h,d=i.top,l=void 0===d?0:d,f=i.bottom,u=i.right,p=i.width,g=void 0===p?0:p,v=i.minWidth,y=i.maxWidth,b=i.minHeight,x=i.maxHeight,m=i.height,w=void 0===m?0:m,S=i.fontSize,z=void 0===S?14:S,k=i.fontWeight,M=i.fontFamily,I=i.fontStyle,B=i.position,W=i.lineClamp,R=i.lineHeight,L=i.padding,O=void 0===L?{}:L,T=i.margin,F=void 0===T?{}:T,A=i.border,j=(A=void 0===A?{}:A).borderWidth,C=void 0===j?0:j,E=i.borderRight,H=(E=void 0===E?{}:E).borderRightWidth,Y=void 0===H?C:H,$=i.borderLeft,U=($=void 0===$?{}:$).borderLeftWidth,X=void 0===U?C:U,_=o.contentSize&&o.contentSize.width,N=o.contentSize&&o.contentSize.height;if(D(g)&&_&&(g=P(g,_)),D(g)&&!_&&(g=null),D(w)&&N&&(w=P(w,N)),D(w)&&!N&&(w=null),D(v)&&_&&(v=P(v,_)),D(y)&&_&&(y=P(y,_)),D(b)&&N&&(b=P(b,N)),D(x)&&N&&(x=P(x,N)),i.padding&&_)for(var V in i.padding)Object.hasOwnProperty.call(i.padding,V)&&(i.padding[V]=P(i.padding[V],_));var G=O.paddingRight,q=void 0===G?0:G,J=O.paddingLeft,Z=void 0===J?0:J;if(i.margin&&[i.margin.marginLeft,i.margin.marginRight].includes("auto"))if(g){var tt=_&&_-g-q-Z-X-Y||0;i.margin.marginLeft==i.margin.marginRight?i.margin.marginLeft=i.margin.marginRight=tt/2:"auto"==i.margin.marginLeft?i.margin.marginLeft=tt:i.margin.marginRight=tt}else i.margin.marginLeft=i.margin.marginRight=0;var rt=F.marginRight,ot=void 0===rt?0:rt,st=F.marginLeft,ht={width:g,height:w,left:0,top:0},ct=Z+q+X+Y+(void 0===st?0:st)+ot;if(this.offsetWidth=ct,e==it&&!this.attributes.widths){var dt=n.text||"";s.save(),s.setFonts({fontFamily:M,fontSize:z,fontWeight:k,fontStyle:I});var lt=new Map;dt.split("\n").map((function(e){var i=e.split("").map((function(e){var i=lt.get(e);if(i)return i;var n=t.measureText(e,z).width;return lt.set(e,n),n})),n=t.measureText(e,z),r=n.fontHeight,o=n.ascent,s=n.descent;t.attributes.fontHeight=r,t.attributes.ascent=o,t.attributes.descent=s,t.attributes.widths||(t.attributes.widths=[]),t.attributes.widths.push({widths:i,total:i.reduce((function(t,e){return t+e}),0)})})),s.restore()}if(e==et&&null==g){var ft=n.width,ut=n.height;ht.width=this.contrastSize(Math.round(ft*w/ut)||0,v,y),this.layoutBoxUpdate(ht,i,0)}if(e==it&&null==g){var pt=this.attributes.widths,gt=Math.max.apply(Math,pt.map((function(t){return t.total})));if(o&&_>0&&(gt>_||this.isBlock(this))&&!this.isAbsolute&&!this.isFixed)gt=_-ct;ht.width=this.contrastSize(gt,v,y),this.layoutBoxUpdate(ht,i,0)}if(e==it&&(o.style.flex||!this.attributes.lines)){var vt=this.attributes.widths.length;this.attributes.widths.forEach((function(t){return t.widths.reduce((function(t,e,i){return t+e>ht.width?(vt++,e):t+e}),0)})),vt=W&&vt>W?W:vt,this.attributes.lines=vt}if(e==et&&null==w){var yt=n.width,bt=n.height;ht.height=this.contrastSize(P(ht.width*bt/yt)||0,b,x),this.layoutBoxUpdate(ht,i,1)}e==it&&null==w&&(R=P(R,z),ht.height=this.contrastSize(P(this.attributes.lines*R),b,x),this.layoutBoxUpdate(ht,i,1,!0)),o&&o.children&&_&&(!this.isFlex(o)||o.isFlexCalc)&&([nt,it].includes(e)&&this.isFlex()||e==nt&&this.isBlock(this)&&this.isInFlow())&&(ht.width=this.contrastSize(_-(o.isFlexCalc?0:ct),v,y),this.layoutBoxUpdate(ht,i)),g&&!D(g)&&(ht.width=this.contrastSize(g,v,y),this.layoutBoxUpdate(ht,i,0)),w&&!D(w)&&(ht.height=this.contrastSize(ht.height,b,x),this.layoutBoxUpdate(ht,i,1));var xt=0;if(a.length){var mt=null;a.forEach((function(e,n){e.getBoxWidthHeight();var r=a[n+1];if(r&&r.isInFlow()&&(e.next=r),e.isInFlow()&&!e.inFlexBox()){var o=t.getBoxState(mt,e);t.line&&t.line.canIEnter(e)&&!o?t.line.add(e):(new Q).bind(e),mt=e}else e.inFlexBox()?t.line&&(t.line.canIEnter(e)||"nowrap"==i.flexWrap)?t.line.add(e):(new K).bind(e):e.isFixed?t.root.fixedLine?t.root.fixedLine.fixedAdd(e):(new Q).fixedBind(e):t.fixedLine?t.fixedLine.fixedAdd(e):(new Q).fixedBind(e,1)})),this.lines&&(xt=this.lines.reduce((function(t,e){return t+e.height}),0))}var wt=0,St=0;if(!g&&(this.isAbsolute||this.isFixed)&&_){var zt=B==at?_:this.root.width,kt=zt-(D(c)?P(c,zt):c)-(D(u)?P(u,zt):u);wt=i.left?kt:this.lineMaxWidth}if(!w&&(null!=l?l:this.isAbsolute||this.isFixed&&N)){var Mt=B==at?N:this.root.height,It=Mt-(D(l)?P(l,Mt):l)-(D(f)?P(f,Mt):f);St=i.top?It:0}if(g&&!D(g)||ht.width||(ht.width=wt||this.contrastSize((this.isBlock(this)&&!this.isInFlow()?_||o.lineMaxWidth:this.lineMaxWidth)||this.lineMaxWidth,v,y),this.layoutBoxUpdate(ht,i,0)),w||!xt&&!St||(ht.height=St||this.contrastSize(xt,b,x),this.layoutBoxUpdate(ht,i)),i.borderRadius&&this.borderSize&&this.borderSize.width)for(var V in i.borderRadius)Object.hasOwnProperty.call(i.borderRadius,V)&&(i.borderRadius[V]=P(i.borderRadius[V],this.borderSize.width));return this.layoutBox},i.layout=function(){return this.getBoxWidthHeight(),this.root.offsetSize=this.offsetSize,this.getBoxPosition(),this.offsetSize},e}(),ft=function(){var t,e,i,n,r,o,s=[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28],a=[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177],h=[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],c=[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],d=[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175],l=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],f=[],u=[],p=[],g=[],v=[],y=2;function b(t,e){var i;t>e&&(i=t,t=e,e=i),i=e,i*=e,i+=e,i>>=1,g[i+=t]=1}function x(t,i){var n;for(p[t+e*i]=1,n=-2;n<2;n++)p[t+n+e*(i-2)]=1,p[t-2+e*(i+n+1)]=1,p[t+2+e*(i+n)]=1,p[t+n+1+e*(i+2)]=1;for(n=0;n<2;n++)b(t-1,i+n),b(t+1,i-n),b(t-n,i-1),b(t+n,i+1)}function m(t){for(;t>=255;)t=((t-=255)>>8)+(255&t);return t}var w=[];function S(t,e,i,n){var r,o,s;for(r=0;r<n;r++)f[i+r]=0;for(r=0;r<e;r++){if(255!=(s=d[f[t+r]^f[i]]))for(o=1;o<n;o++)f[i+o-1]=f[i+o]^l[m(s+w[n-o])];else for(o=i;o<i+n;o++)f[o]=f[o+1];f[i+n-1]=255==s?0:l[m(s+w[0])]}}function z(t,e){var i;return t>e&&(i=t,t=e,e=i),i=e,i+=e*e,i>>=1,g[i+=t]}function k(t){var i,n,r,o;switch(t){case 0:for(n=0;n<e;n++)for(i=0;i<e;i++)i+n&1||z(i,n)||(p[i+n*e]^=1);break;case 1:for(n=0;n<e;n++)for(i=0;i<e;i++)1&n||z(i,n)||(p[i+n*e]^=1);break;case 2:for(n=0;n<e;n++)for(r=0,i=0;i<e;i++,r++)3==r&&(r=0),r||z(i,n)||(p[i+n*e]^=1);break;case 3:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=o,i=0;i<e;i++,r++)3==r&&(r=0),r||z(i,n)||(p[i+n*e]^=1);break;case 4:for(n=0;n<e;n++)for(r=0,o=n>>1&1,i=0;i<e;i++,r++)3==r&&(r=0,o=!o),o||z(i,n)||(p[i+n*e]^=1);break;case 5:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+!(!r|!o)||z(i,n)||(p[i+n*e]^=1);break;case 6:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+(r&&r==o)&1||z(i,n)||(p[i+n*e]^=1);break;case 7:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(r&&r==o)+(i+n&1)&1||z(i,n)||(p[i+n*e]^=1)}}function M(t){var e,i=0;for(e=0;e<=t;e++)v[e]>=5&&(i+=3+v[e]-5);for(e=3;e<t-1;e+=2)v[e-2]==v[e+2]&&v[e+2]==v[e-1]&&v[e-1]==v[e+1]&&3*v[e-1]==v[e]&&(0==v[e-3]||e+3>t||3*v[e-3]>=4*v[e]||3*v[e+3]>=4*v[e])&&(i+=40);return i}function I(){var t,i,n,r,o,s=0,a=0;for(i=0;i<e-1;i++)for(t=0;t<e-1;t++)(p[t+e*i]&&p[t+1+e*i]&&p[t+e*(i+1)]&&p[t+1+e*(i+1)]||!(p[t+e*i]||p[t+1+e*i]||p[t+e*(i+1)]||p[t+1+e*(i+1)]))&&(s+=3);for(i=0;i<e;i++){for(v[0]=0,n=r=t=0;t<e;t++)(o=p[t+e*i])==r?v[n]++:v[++n]=1,a+=(r=o)?1:-1;s+=M(n)}a<0&&(a=-a);var h=a,c=0;for(h+=h<<2,h<<=1;h>e*e;)h-=e*e,c++;for(s+=10*c,t=0;t<e;t++){for(v[0]=0,n=r=i=0;i<e;i++)(o=p[t+e*i])==r?v[n]++:v[++n]=1,r=o;s+=M(n)}return s}var B=null;return{api:{get ecclevel(){return y},set ecclevel(t){y=t},get size(){return _size},set size(t){_size=t},get canvas(){return B},set canvas(t){B=t},getFrame:function(v){return function(v){var M,B,W,P,R,L,O,T;P=v.length,t=0;do{if(t++,W=4*(y-1)+16*(t-1),i=c[W++],n=c[W++],r=c[W++],o=c[W],P<=(W=r*(i+n)+n-3+(t<=9)))break}while(t<40);for(e=17+4*t,R=r+(r+o)*(i+n)+n,P=0;P<R;P++)u[P]=0;for(f=v.slice(0),P=0;P<e*e;P++)p[P]=0;for(P=0;P<(e*(e+1)+1)/2;P++)g[P]=0;for(P=0;P<3;P++){for(W=0,B=0,1==P&&(W=e-7),2==P&&(B=e-7),p[B+3+e*(W+3)]=1,M=0;M<6;M++)p[B+M+e*W]=1,p[B+e*(W+M+1)]=1,p[B+6+e*(W+M)]=1,p[B+M+1+e*(W+6)]=1;for(M=1;M<5;M++)b(B+M,W+1),b(B+1,W+M+1),b(B+5,W+M),b(B+M+1,W+5);for(M=2;M<4;M++)p[B+M+e*(W+2)]=1,p[B+2+e*(W+M+1)]=1,p[B+4+e*(W+M)]=1,p[B+M+1+e*(W+4)]=1}if(t>1)for(P=s[t],B=e-7;;){for(M=e-7;M>P-3&&(x(M,B),!(M<P));)M-=P;if(B<=P+9)break;x(6,B-=P),x(B,6)}for(p[8+e*(e-8)]=1,B=0;B<7;B++)b(7,B),b(e-8,B),b(7,B+e-7);for(M=0;M<8;M++)b(M,7),b(M+e-8,7),b(M,e-8);for(M=0;M<9;M++)b(M,8);for(M=0;M<8;M++)b(M+e-8,8),b(8,M);for(B=0;B<7;B++)b(8,B+e-7);for(M=0;M<e-14;M++)1&M?(b(8+M,6),b(6,8+M)):(p[8+M+6*e]=1,p[6+e*(8+M)]=1);if(t>6)for(P=a[t-7],W=17,M=0;M<6;M++)for(B=0;B<3;B++,W--)1&(W>11?t>>W-12:P>>W)?(p[5-M+e*(2-B+e-11)]=1,p[2-B+e-11+e*(5-M)]=1):(b(5-M,2-B+e-11),b(2-B+e-11,5-M));for(B=0;B<e;B++)for(M=0;M<=B;M++)p[M+e*B]&&b(M,B);for(R=f.length,L=0;L<R;L++)u[L]=f.charCodeAt(L);if(f=u.slice(0),R>=(M=r*(i+n)+n)-2&&(R=M-2,t>9&&R--),L=R,t>9){for(f[L+2]=0,f[L+3]=0;L--;)P=f[L],f[L+3]|=255&P<<4,f[L+2]=P>>4;f[2]|=255&R<<4,f[1]=R>>4,f[0]=64|R>>12}else{for(f[L+1]=0,f[L+2]=0;L--;)P=f[L],f[L+2]|=255&P<<4,f[L+1]=P>>4;f[1]|=255&R<<4,f[0]=64|R>>4}for(L=R+3-(t<10);L<M;)f[L++]=236,f[L++]=17;for(w[0]=1,L=0;L<o;L++){for(w[L+1]=1,O=L;O>0;O--)w[O]=w[O]?w[O-1]^l[m(d[w[O]]+L)]:w[O-1];w[0]=l[m(d[w[0]]+L)]}for(L=0;L<=o;L++)w[L]=d[w[L]];for(W=M,B=0,L=0;L<i;L++)S(B,r,W,o),B+=r,W+=o;for(L=0;L<n;L++)S(B,r+1,W,o),B+=r+1,W+=o;for(B=0,L=0;L<r;L++){for(O=0;O<i;O++)u[B++]=f[L+O*r];for(O=0;O<n;O++)u[B++]=f[i*r+L+O*(r+1)]}for(O=0;O<n;O++)u[B++]=f[i*r+L+O*(r+1)];for(L=0;L<o;L++)for(O=0;O<i+n;O++)u[B++]=f[M+L+O*o];for(f=u,M=B=e-1,W=R=1,T=(r+o)*(i+n)+n,L=0;L<T;L++)for(P=f[L],O=0;O<8;O++,P<<=1){128&P&&(p[M+e*B]=1);do{R?M--:(M++,W?0!=B?B--:(W=!W,6==(M-=2)&&(M--,B=9)):B!=e-1?B++:(W=!W,6==(M-=2)&&(M--,B-=8))),R=!R}while(z(M,B))}for(f=p.slice(0),P=0,B=3e4,W=0;W<8&&(k(W),(M=I())<B&&(B=M,P=W),7!=P);W++)p=f.slice(0);for(P!=W&&k(P),B=h[P+(y-1<<3)],W=0;W<8;W++,B>>=1)1&B&&(p[e-1-W+8*e]=1,W<6?p[8+e*W]=1:p[8+e*(W+1)]=1);for(W=0;W<7;W++,B>>=1)1&B&&(p[8+e*(e-7+W)]=1,W?p[6-W+8*e]=1:p[7+8*e]=1);return p}(v)},utf16to8:function(t){var e,i,n,r;for(e="",n=t.length,i=0;i<n;i++)(r=t.charCodeAt(i))>=1&&r<=127?e+=t.charAt(i):r>2047?(e+=String.fromCharCode(224|r>>12&15),e+=String.fromCharCode(128|r>>6&63),e+=String.fromCharCode(128|r>>0&63)):(e+=String.fromCharCode(192|r>>6&31),e+=String.fromCharCode(128|r>>0&63));return e},draw:function(t,i,n,r,o){i.drawView(n,r);var s=i.ctx,a=n.contentSize,h=a.width,c=a.height,d=a.left,l=a.top;r.borderRadius,r.backgroundColor;var f=r.color,u=void 0===f?"#000000":f;r.border,n.contentSize.left,n.borderSize.left,n.contentSize.top,n.borderSize.top;if(y=o||y,s){s.save(),i.setOpacity(r),i.setTransform(n,r);var p=Math.min(h,c);t=this.utf16to8(t);var g=this.getFrame(t),v=p/e;s.setFillStyle(u);for(var b=0;b<e;b++)for(var x=0;x<e;x++)g[x*e+b]&&s.fillRect(d+v*b,l+v*x,v,v);s.restore(),i.setBorder(n,r)}else console.warn("No canvas provided to draw QR code in!")}}}}(),ut=g,pt=p,gt=v,vt=u,yt=a,bt=h,xt=c,mt=d,wt=l,St=f,zt=function(){function r(t){var e,i=this;this.v="1.9.3.5",this.id=null,this.pixelRatio=1,this.width=0,this.height=0,this.sleep=1e3/30,this.count=0,this.isRate=!1,this.isDraw=!0,this.isCache=!0,this.fixed="",this.useCORS=!1,this.imageBus=[],this.createImage=function(t,e){return new Promise((function(n,r){var o=null;window||i.canvas.createImage?(o=i.canvas&&i.canvas.createImage?i.canvas.createImage():new Image,e&&o.setAttribute("crossOrigin","Anonymous"),o.src=t,o.onload=function(){n({width:o.naturalWidth||o.width,height:o.naturalHeight||o.height,path:o,src:this.src})},o.onerror=function(t){r(t)}):r({fail:"getImageInfo fail",src:t})}))},this.options=t,Object.assign(this,t),this.ctx=((e=t.context).setFonts=function(t){var i=t.fontFamily,r=void 0===i?"sans-serif":i,o=t.fontSize,s=void 0===o?14:o,a=t.fontWeight,h=void 0===a?"normal":a,c=t.fontStyle,d=void 0===c?"normal":c;M==n.MP_TOUTIAO&&(h="bold"==h?"bold":"",d="italic"==d?"italic":""),e.font="".concat(d," ").concat(h," ").concat(Math.round(s),"px ").concat(r)},e.draw&&e.setFillStyle?e:Object.assign(e,{setStrokeStyle:function(t){e.strokeStyle=t},setLineWidth:function(t){e.lineWidth=t},setLineCap:function(t){e.lineCap=t},setFillStyle:function(t){e.fillStyle=t},setFontSize:function(t){e.font="".concat(String(t),"px sans-serif")},setGlobalAlpha:function(t){e.globalAlpha=t},setLineJoin:function(t){e.lineJoin=t},setTextAlign:function(t){e.textAlign=t},setMiterLimit:function(t){e.miterLimit=t},setShadow:function(t,i,n,r){e.shadowOffsetX=t,e.shadowOffsetY=i,e.shadowBlur=n,e.shadowColor=r},setTextBaseline:function(t){e.textBaseline=t},createCircularGradient:function(){},draw:function(){}})),this.progress=0,this.root={width:t.width,height:t.height,fontSizeRate:1,fixedLine:null},this.size=this.root;var r=0;Object.defineProperty(this,"progress",{configurable:!0,set:function(t){r=t,i.lifecycle("onProgress",t/i.count)},get:function(){return r||0}})}return r.prototype.lifecycle=function(t,e){this.options.listen&&this.options.listen[t]&&this.options.listen[t](e)},r.prototype.setContext=function(t){t&&(this.ctx=t)},r.prototype.init=function(){if(this.canvas.height||n.WEB==M){this.ctx.setTransform(1,0,0,1,0,0);var t=this.size.height*this.pixelRatio,e=this.size.width*this.pixelRatio;this.canvas.height=t,this.canvas.width=e,this.ctx.scale(this.pixelRatio,this.pixelRatio)}},r.prototype.clear=function(){this.ctx.clearRect(0,0,this.size.width,this.size.height)},r.prototype.clipPath=function(t,e,i,n,r,o,s){void 0===o&&(o=!1),void 0===s&&(s=!1);var a=this.ctx;if(/polygon/.test(r)){var h=r.match(/-?\d+(rpx|px|%)?\s+-?\d+(rpx|px|%)?/g)||[];a.beginPath(),h.map((function(r){var o=r.split(" "),s=o[0],a=o[1];return[P(s,i)+t,P(a,n)+e]})).forEach((function(t,e){0==e?a.moveTo(t[0],t[1]):a.lineTo(t[0],t[1])})),a.closePath(),s&&a.stroke(),o&&a.fill()}},r.prototype.roundRect=function(t,e,i,n,r,o,s){if(void 0===o&&(o=!1),void 0===s&&(s=!1),!(r<0)){var a=this.ctx;if(a.beginPath(),r){var h=r||{},c=h.borderTopLeftRadius,d=void 0===c?r||0:c,l=h.borderTopRightRadius,f=void 0===l?r||0:l,u=h.borderBottomRightRadius,p=void 0===u?r||0:u,g=h.borderBottomLeftRadius,v=void 0===g?r||0:g;a.arc(t+i-p,e+n-p,p,0,.5*Math.PI),a.lineTo(t+v,e+n),a.arc(t+v,e+n-v,v,.5*Math.PI,Math.PI),a.lineTo(t,e+d),a.arc(t+d,e+d,d,Math.PI,1.5*Math.PI),a.lineTo(t+i-f,e),a.arc(t+i-f,e+f,f,1.5*Math.PI,2*Math.PI),a.lineTo(t+i,e+n-p)}else a.rect(t,e,i,n);a.closePath(),s&&a.stroke(),o&&a.fill()}},r.prototype.setTransform=function(t,e){var i=e.transform,n=e.transformOrigin,r=this.ctx,o=i||{},s=o.scaleX,a=void 0===s?1:s,h=o.scaleY,c=void 0===h?1:h,d=o.translateX,l=void 0===d?0:d,f=o.translateY,u=void 0===f?0:f,p=o.rotate,g=void 0===p?0:p,v=o.skewX,y=void 0===v?0:v,b=o.skewY,x=void 0===b?0:b,m=t.left,w=t.top,S=t.width,z=t.height;l=P(l,S)||0,u=P(u,z)||0;var k={top:P("0%",1),center:P("50%",1),bottom:P("100%",1)},M={left:P("0%",1),center:P("50%",1),right:P("100%",1)};if(n=n.split(" ").filter((function(t,e){return e<2})).reduce((function(t,e){if(/\d+/.test(e)){var i=P(e,1)/(/px|rpx$/.test(e)?W(t.x)?z:S:1);return W(t.x)?Object.assign(t,{y:i}):Object.assign(t,{x:i})}return W(M[e])&&!W(t.x)?Object.assign(t,{x:M[e]}):Object.assign(t,{y:k[e]||.5})}),{}),(l||u)&&r.translate(l,u),(a||c)&&r.scale(a,c),g){var I=m+S*n.x,B=w+z*n.y;r.translate(I,B),r.rotate(g*Math.PI/180),r.translate(-I,-B)}(y||x)&&r.transform(1,Math.tan(x*Math.PI/180),Math.tan(y*Math.PI/180),1,0,0)},r.prototype.setBackground=function(t,e,i,r,o){var s=this.ctx;t&&"transparent"!=t?L(t)?O(t,e,i,r,o,s):s.setFillStyle(t):[n.MP_TOUTIAO,n.MP_BAIDU].includes(M)?s.setFillStyle("rgba(0,0,0,0)"):s.setFillStyle("transparent")},r.prototype.setShadow=function(t){var e=t.boxShadow,i=void 0===e?[]:e,n=this.ctx;if(i.length){var r=i[0],o=i[1],s=i[2],a=i[3];n.setShadow(r,o,s,a)}},r.prototype.setBorder=function(t,e){var i=this.ctx,n=t.width,r=t.height,o=t.left,s=t.top,a=e.border,h=e.borderBottom,c=e.borderTop,d=e.borderRight,l=e.borderLeft,f=e.borderRadius,u=e.lineCap,p=a||{},g=p.borderWidth,v=void 0===g?0:g,y=p.borderStyle,b=p.borderColor,x=h||{},m=x.borderBottomWidth,w=void 0===m?v:m,S=x.borderBottomStyle,z=void 0===S?y:S,k=x.borderBottomColor,I=void 0===k?b:k,B=c||{},W=B.borderTopWidth,P=void 0===W?v:W,R=B.borderTopStyle,L=void 0===R?y:R,O=B.borderTopColor,T=void 0===O?b:O,F=d||{},A=F.borderRightWidth,j=void 0===A?v:A,C=F.borderRightStyle,E=void 0===C?y:C,H=F.borderRightColor,Y=void 0===H?b:H,D=l||{},$=D.borderLeftWidth,U=void 0===$?v:$,X=D.borderLeftStyle,_=void 0===X?y:X,N=D.borderLeftColor,V=void 0===N?b:N,G=f||{},q=G.borderTopLeftRadius,J=void 0===q?f||0:q,Q=G.borderTopRightRadius,Z=void 0===Q?f||0:Q,K=G.borderBottomRightRadius,tt=void 0===K?f||0:K,et=G.borderBottomLeftRadius,it=void 0===et?f||0:et;if(h||l||c||d||a){var nt=function(t,e,n){"dashed"==e?/mp/.test(M)?i.setLineDash([Math.ceil(4*t/3),Math.ceil(4*t/3)]):i.setLineDash([Math.ceil(6*t),Math.ceil(6*t)]):"dotted"==e&&i.setLineDash([t,t]),i.setStrokeStyle(n)},rt=function(t,e,n,r,o,s,a,h,c,d,l,f,p,g,v){i.save(),i.setLineCap(v?"square":u),i.setLineWidth(f),nt(f,p,g),i.beginPath(),i.arc(t,e,a,Math.PI*c,Math.PI*d),i.lineTo(n,r),i.arc(o,s,h,Math.PI*d,Math.PI*l),i.stroke(),i.restore()};if(i.save(),a&&!h&&!l&&!c&&!d)return i.setLineWidth(v),nt(v,y,b),this.roundRect(o,s,n,r,f,!1,!!b),void i.restore();w&&rt(o+n-tt,s+r-tt,o+it,s+r,o+it,s+r-it,tt,it,.25,.5,.75,w,z,I,U&&j),U&&rt(o+it,s+r-it,o,s+J,o+J,s+J,it,J,.75,1,1.25,U,_,V,P&&w),P&&rt(o+J,s+J,o+n-Z,s,o+n-Z,s+Z,J,Z,1.25,1.5,1.75,P,L,T,U&&j),j&&rt(o+n-Z,s+Z,o+n,s+r-tt,o+n-tt,s+r-tt,Z,tt,1.75,2,.25,j,E,Y,P&&w)}},r.prototype.setOpacity=function(t){var e=t.opacity,i=void 0===e?1:e;this.ctx.setGlobalAlpha(i)},r.prototype.drawPattern=function(t,n,r){return e(this,void 0,void 0,(function(){var e=this;return i(this,(function(i){return[2,new Promise((function(i,o){e.drawView(n,r,!0,!1,!0);var s=e,a=s.ctx;s.canvas;var h=n.width,c=n.height,d=n.left,l=n.top,f=r||{},u=f.borderRadius,p=void 0===u?0:u,g=f.backgroundImage,v=f.backgroundRepeat,y=void 0===v?"repeat":v;g&&function(t){var o=a.createPattern(t.src,y);a.setFillStyle(o),e.roundRect(d,l,h,c,p,!0,!1),e.setBorder(n,r),i()}(t)}))]}))}))},r.prototype.drawView=function(t,e,i,n,r){void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===r&&(r=!0);var o=this.ctx,s=t.width,a=t.height,h=t.left,c=t.top,d=e||{},l=d.borderRadius,f=void 0===l?0:l,u=d.backgroundColor,p=void 0===u?"transparent":u,g=d.overflow;e.opacity&&this.setOpacity(e),this.setTransform(t,e),r&&(o.save(),this.setShadow(e)),i&&this.setBackground(p,s,a,h,c),e.clipPath?this.clipPath(h,c,s,a,e.clipPath,i,!1):this.roundRect(h,c,s,a,f,i,!1),r&&o.restore(),n&&this.setBorder(t,e),"hidden"==g&&o.clip()},r.prototype.drawImage=function(t,r,o,s){return void 0===r&&(r={}),void 0===o&&(o={}),void 0===s&&(s=!0),e(this,void 0,void 0,(function(){var a=this;return i(this,(function(h){switch(h.label){case 0:return[4,new Promise((function(h,c){return e(a,void 0,void 0,(function(){var e,a,c,d,l,f,u,p,g,v,y,b,x,m,w,S,z,k,I,B,W,L=this;return i(this,(function(i){return e=this.ctx,a=o.borderRadius,c=void 0===a?0:a,d=o.backgroundColor,l=void 0===d?"transparent":d,f=o.objectFit,u=void 0===f?"fill":f,p=o.backgroundSize,g=void 0===p?"fill":p,v=o.objectPosition,y=o.backgroundPosition,b=o.boxShadow,o.backgroundImage&&(u=g,v=y),b&&this.drawView(r,Object.assign(o,{backgroundColor:l||b&&(l||"#ffffff")}),!0,!1,!0),x=r.width,m=r.height,w=r.left,S=r.top,e.save(),z=r.contentSize.left-r.borderSize.left,k=r.contentSize.top-r.borderSize.top,s||(this.setOpacity(o),this.setTransform(r,o),this.setBackground(l,x,m,w,S),this.roundRect(w,S,x,m,c,!!(c||!b&&l),!1)),w+=z,S+=k,e.clip(),I=function(t){if("fill"!==u){var i=function(t,e,i){var n=t.objectFit,r=t.objectPosition,o=e.width/e.height,s=i.width/i.height,a=1;"contain"==n&&o>=s||"cover"==n&&o<s?a=e.height/i.height:("contain"==n&&o<s||"cover"==n&&o>=s)&&(a=e.width/i.width);var h=i.width*a,c=i.height*a,d=r||[],l=d[0],f=d[1],u=/^\d+px|rpx$/.test(l)?P(l,e.width):(e.width-h)*(R(l)?P(l,1):{left:0,center:.5,right:1}[l||"center"]),p=/^\d+px|rpx$/.test(f)?P(f,e.height):(e.height-c)*(R(f)?P(f,1):{top:0,center:.5,bottom:1}[f||"center"]),g=function(t,e){return[(t-u)/a,(e-p)/a]},v=g(0,0),y=v[0],b=v[1],x=g(e.width,e.height),m=x[0],w=x[1];return{sx:Math.max(y,0),sy:Math.max(b,0),sw:Math.min(m-y,i.width),sh:Math.min(w-b,i.height),dx:Math.max(u,0),dy:Math.max(p,0),dw:Math.min(h,e.width),dh:Math.min(c,e.height)}}({objectFit:u,objectPosition:v},r.contentSize,t),o=i.sx,s=i.sy,a=i.sh,h=i.sw,c=i.dx,d=i.dy,l=i.dh,f=i.dw;M==n.MP_BAIDU?e.drawImage(t.src,c+w,d+S,f,l,o,s,h,a):e.drawImage(t.src,o,s,h,a,c+w,d+S,f,l)}else e.drawImage(t.src,w,S,x,m)},B=function(){e.restore(),L.drawView(r,o,!1,!0,!1),h(1)},W=function(t){I(t),B()},W(t),[2]}))}))}))];case 1:return h.sent(),[2]}}))}))},r.prototype.drawText=function(t,e,i,n){var r=this,o=this.ctx,s=e.borderSize,a=e.contentSize,h=e.left,c=e.top,d=a.width,l=a.height,f=a.left-s.left,u=a.top-s.top,p=i.color,g=void 0===p?"#000000":p,v=i.lineHeight,y=void 0===v?"1.4em":v,b=i.fontSize,x=void 0===b?14:b,m=i.fontWeight,w=i.fontFamily,S=i.fontStyle,z=i.textAlign,k=void 0===z?"left":z,M=i.textStroke,I=i.verticalAlign,B=void 0===I?bt:I,W=i.backgroundColor,R=i.lineClamp,L=i.backgroundClip,O=i.textShadow,T=i.textDecoration;if(this.drawView(e,i,L!=pt),y=P(y,x),t){o.save(),h+=f,c+=u;var F=n.fontHeight,A=n.descent+n.ascent;switch(o.setFonts({fontFamily:w,fontSize:x,fontWeight:m,fontStyle:S}),o.setTextBaseline(bt),o.setTextAlign(k),L?this.setBackground(W,d,l,h,c):o.setFillStyle(g),k){case mt:break;case wt:h+=.5*d;break;case St:h+=d}var j=n.lines*y,C=Math.ceil((l-j)/2);switch(C<0&&(C=0),B){case yt:break;case bt:c+=C;break;case xt:c+=2*C}var E=(y-F)/2,H=y/2,Y=function(t){var e=o.measureText(t),i=e.actualBoundingBoxDescent,n=void 0===i?0:i,r=e.actualBoundingBoxAscent;return B==yt?{fix:A?void 0===r?0:r:H-E/2,lineY:A?0:E-E/2}:B==bt?{fix:A?H+n/4:H,lineY:A?0:E}:B==xt?{fix:A?y-n:H+E/2,lineY:A?2*E:E+E/2}:{fix:0,height:0,lineY:0}},D=function(t,e,i){var r=t;switch(k){case mt:r+=i;break;case wt:r=(t-=i/2)+i;break;case St:r=t,t-=i}if(T){o.setLineWidth(x/13),o.beginPath();var s=.1*n.fontHeight;/\bunderline\b/.test(T)&&(o.moveTo(t,e+n.fontHeight+s),o.lineTo(r,e+n.fontHeight+s)),/\boverline\b/.test(T)&&(o.moveTo(t,e-s),o.lineTo(r,e-s)),/\bline-through\b/.test(T)&&(o.moveTo(t,e+.5*n.fontHeight),o.lineTo(r,e+.5*n.fontHeight)),o.closePath(),o.setStrokeStyle(g),o.stroke()}},$=function(t,e,i){var n=function(){o.setLineWidth(M.width),o.setStrokeStyle(M.color),o.strokeText(t,e,i)},s="outset";M&&M.type!==s?(o.save(),r.setShadow({boxShadow:O}),o.fillText(t,e,i),o.restore(),n()):M&&M.type==s?(o.save(),r.setShadow({boxShadow:O}),n(),o.restore(),o.save(),o.fillText(t,e,i),o.restore()):(r.setShadow({boxShadow:O}),o.fillText(t,e,i))};if(!n.widths||1==n.widths.length&&n.widths[0].total<=a.width){var U=Y(t),X=U.fix,_=U.lineY;return $(t,h,c+X),D(h,c+_,n&&n.widths&&n.widths[0].total||n.text),c+=y,o.restore(),void this.setBorder(e,i)}for(var N=t.split(""),V=c,G=h,q="",J=0,Q=0;Q<=N.length;Q++){var Z=N[Q]||"",K="\n"===Z,tt=""==Z,et=q+(Z=K?"":Z),it=o.measureText(et).width;if(J>=R)break;if(G=h,it>a.width||K||tt){if(J++,q=tt&&it<=a.width?et:q,J===R&&it>d){for(;o.measureText("".concat(q,"...")).width>a.width&&!(q.length<=1);)q=q.substring(0,q.length-1);q+="..."}var nt=Y(q);X=nt.fix,_=nt.lineY;if($(q,G,c+X),D(G,c+_,it),q=Z,(c+=y)>V+l)break}else q=et}o.restore()}},r.prototype.source=function(t){return e(this,void 0,void 0,(function(){var e,n,r,o,s=this;return i(this,(function(i){switch(i.label){case 0:if(this.node=null,e=+new Date,"{}"==JSON.stringify(t))return[2];if(!t.type)for(n in t.type=vt,t.css=t.css||{},t)["views","children","type","css"].includes(n)||(t.css[n]=t[n],delete t[n]);return t.css.boxSizing||(t.css.boxSizing="border-box"),[4,this.create(t)];case 1:return(r=i.sent())?(o=r.layout()||{},this.size=o,this.node=r,this.onEffectFinished().then((function(t){return s.lifecycle("onEffectSuccess",t)})).catch((function(t){return s.lifecycle("onEffectFail",t)})),console.log("布局用时："+(+new Date-e)+"ms"),[2,this.size]):[2,console.warn("no node")]}}))}))},r.prototype.getImageInfo=function(t){return this.imageBus[t]||(this.imageBus[t]=this.createImage(t,this.useCORS)),this.imageBus[t]},r.prototype.create=function(n,r){return e(this,void 0,void 0,(function(){var e,o,s,a,h,c,d,l,f,u,p,g,v,y,b,m,w;return i(this,(function(i){switch(i.label){case 0:if(e=n.type==ut,o=[pt,gt].includes(n.type),s=n.css||{},a=s.backgroundImage,h=s.display,e&&!n.src&&!n.url||o&&!n.text)return[2];if(h==x)return[2];if(o&&(n.text=String(n.text)),!(e||n.type==vt&&a))return[3,4];c=e?n.src:"",d=/url\((.+)\)/.exec(a),a&&d&&d[1]&&(c=d[1]||""),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.getImageInfo(c)];case 2:return l=i.sent(),f=l.width,u=l.height,!(p=l.path)&&e?[2]:(p&&(n.attributes=Object.assign(n.attributes||{},{width:f,height:u,path:p,src:p,naturalSrc:c})),[3,4]);case 3:return g=i.sent(),n.type!=vt?[2]:(this.lifecycle("onEffectFail",t(t({},g),{src:c})),[3,4]);case 4:if(this.count+=1,v=new lt(n,r,this.root,this.ctx),!(y=n.views||n.children))return[3,8];b=0,i.label=5;case 5:return b<y.length?(m=y[b],[4,this.create(m,v)]):[3,8];case 6:(w=i.sent())&&v.add(w),i.label=7;case 7:return b++,[3,5];case 8:return[2,v]}}))}))},r.prototype.drawNode=function(t,n){return void 0===n&&(n=!1),e(this,void 0,void 0,(function(){var e,r,o,s,a,h,c,d,l,f,u,p,g,v,y,b,x,m,w,S,z,k,M;return i(this,(function(i){switch(i.label){case 0:return e=t.layoutBox,r=t.computedStyle,o=t.attributes,s=t.name,a=t.children,h=t.fixedLine,c=t.attributes,d=c.src,l=c.text,f=r.position,u=r.backgroundImage,p=r.backgroundRepeat,["fixed"].includes(f)&&!n?[2]:(this.ctx.save(),s!==vt?[3,7]:d&&u?p?[4,this.drawPattern(o,e,r)]:[3,2]:[3,5]);case 1:return i.sent(),[3,4];case 2:return[4,this.drawImage(o,e,r,!1)];case 3:i.sent(),i.label=4;case 4:return[3,6];case 5:this.drawView(e,r),i.label=6;case 6:return[3,10];case 7:return s===ut&&d?[4,this.drawImage(o,e,r,!1)]:[3,9];case 8:return i.sent(),[3,10];case 9:s===pt?this.drawText(l,e,r,o):s===gt&&ft.api&&ft.api.draw(l,this,e,r),i.label=10;case 10:if(this.progress+=1,v=(g=h||{}).beforeElements,y=g.afterElements,!v)return[3,14];b=0,x=v,i.label=11;case 11:return b<x.length?(M=x[b],[4,this.drawNode(M)]):[3,14];case 12:i.sent(),i.label=13;case 13:return b++,[3,11];case 14:if(!a)return[3,18];m=Object.values?Object.values(a):Object.keys(a).map((function(t){return a[t]})),w=0,S=m,i.label=15;case 15:return w<S.length?"absolute"===(M=S[w]).computedStyle.position?[3,17]:[4,this.drawNode(M)]:[3,18];case 16:i.sent(),i.label=17;case 17:return w++,[3,15];case 18:if(!y)return[3,22];z=0,k=y,i.label=19;case 19:return z<k.length?(M=k[z],[4,this.drawNode(M)]):[3,22];case 20:i.sent(),i.label=21;case 21:return z++,[3,19];case 22:return this.ctx.restore(),[2]}}))}))},r.prototype.render=function(t){var n=this;return void 0===t&&(t=30),new Promise((function(r,o){return e(n,void 0,void 0,(function(){var e,n,s,a,h,c,d,l,f,u;return i(this,(function(i){switch(i.label){case 0:return e=+new Date,this.init(),[4,(p=t,void 0===p&&(p=0),new Promise((function(t){return setTimeout(t,p)})))];case 1:i.sent(),i.label=2;case 2:if(i.trys.push([2,14,,15]),!this.node)return[3,12];if(n=this.root.fixedLine||{},s=n.beforeElements,a=n.afterElements,!s)return[3,6];h=0,c=s,i.label=3;case 3:return h<c.length?(f=c[h],[4,this.drawNode(f,!0)]):[3,6];case 4:i.sent(),i.label=5;case 5:return h++,[3,3];case 6:return[4,this.drawNode(this.node)];case 7:if(i.sent(),!a)return[3,11];d=0,l=a,i.label=8;case 8:return d<l.length?(f=l[d],[4,this.drawNode(f,!0)]):[3,11];case 9:i.sent(),i.label=10;case 10:return d++,[3,8];case 11:return r(this.node),[3,13];case 12:this.lifecycle("onEffectFail","node is empty"),i.label=13;case 13:return[3,15];case 14:return u=i.sent(),this.lifecycle("onEffectFail",u),o(u),[3,15];case 15:return console.log("渲染用时："+(+new Date-e-30)+"ms"),[2]}var p}))}))}))},r.prototype.onEffectFinished=function(){var t=this,e=Object.keys(this.imageBus).map((function(e){return t.imageBus[e]}));return Promise.all(e)},r.prototype.destroy=function(){this.node=[]},r.prototype.save=function(t){try{var e=t||{},i=e.fileType,n=void 0===i?"png":i,r=e.quality,o=void 0===r?1:r;return this.canvas.toDataURL("image/".concat(n),o)}catch(t){return this.lifecycle("onEffectFail","image cross domain"),t}},r}();n.WEB==M&&(window.Painter=zt);export{zt as Painter,zt as default};
