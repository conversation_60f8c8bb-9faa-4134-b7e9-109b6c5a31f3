@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=F:\erkai999\pc\node_modules\.pnpm\replace-in-file@6.3.5\node_modules\replace-in-file\bin\node_modules;F:\erkai999\pc\node_modules\.pnpm\replace-in-file@6.3.5\node_modules\replace-in-file\node_modules;F:\erkai999\pc\node_modules\.pnpm\replace-in-file@6.3.5\node_modules;F:\erkai999\pc\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=F:\erkai999\pc\node_modules\.pnpm\replace-in-file@6.3.5\node_modules\replace-in-file\bin\node_modules;F:\erkai999\pc\node_modules\.pnpm\replace-in-file@6.3.5\node_modules\replace-in-file\node_modules;F:\erkai999\pc\node_modules\.pnpm\replace-in-file@6.3.5\node_modules;F:\erkai999\pc\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\replace-in-file\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\replace-in-file\bin\cli.js" %*
)
