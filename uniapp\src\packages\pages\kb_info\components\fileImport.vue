<template>
    <u-popup
        v-model="show"
        safe-area-inset-bottom
        closeable
        border-radius="16"
        mode="bottom"
    >
        <view class="h-[80vh] flex flex-col">
            <view
                class="text-xl py-[28rpx] font-bold border-b border-solid border-light border-0 mx-[20rpx]"
            >
                文件导入
            </view>
            
            <!-- 重要提示 -->
            <view class="important-notice mx-[20rpx] mt-[20rpx]">
                <view class="notice-header">
                    <text class="notice-icon">⚠️</text>
                    <text class="notice-title">重要提示</text>
                </view>
                <view class="notice-content">
                    为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。
                </view>
            </view>
            
            <view class="flex-1 min-h-0">
                <scroll-view scroll-y class="h-full">
                    <view class="mx-[20rpx]">
                        <view>
                            <view
                                v-for="(item, index) in tabList"
                                :key="index"
                                class="rounded-lg border border-solid border-[#E2E6EA] p-[30rpx] mt-2"
                                :class="{ isSelect: item.type == isSelectType }"
                                @click="isSelectType = item.type"
                            >
                                <view>{{ item.name }}</view>
                                <view class="text-info text-xs mt-[10rpx]">{{
                                    item.describe
                                }}</view>
                            </view>
                        </view>
                        <view class="flex-1">
                            <Doc
                                v-if="ImportTypeEnum.DOC == isSelectType"
                                v-model="currentDate"
                            />
                            <Cvs
                                v-if="ImportTypeEnum.CVS == isSelectType"
                                v-model="currentDate"
                            />
                            <QASplit
                                v-if="ImportTypeEnum.QASplit == isSelectType"
                                v-model="currentDate"
                            />
                            <WebPage
                                v-if="ImportTypeEnum.WEB_PAGE == isSelectType"
                                v-model="currentDate"
                            />
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view class="p-[20rpx]">
                <u-button @click="handleSubmit" type="primary">
                    确认导入
                </u-button>
            </view>
        </view>
    </u-popup>
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'
import { ImportTypeEnum, IDataItem } from './import/hook'
import Doc from './import/doc.vue'
import Cvs from './import/csv.vue'
import QASplit from './import/QASplit.vue'
import WebPage from './import/web-page.vue'
import { useKB } from '../useKb'

const { importData, KBId } = useKB()

const props = withDefaults(
    defineProps<{
        modelValue: boolean
    }>(),
    {}
)

const emit = defineEmits<{
    (event: 'update:modelValue', value: boolean): void
    (event: 'close'): void
}>()

const show = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        seletFirst()
        emit('update:modelValue', value)
    }
})

//选中导入方式
const isSelectType = ref<number>(0)

const docData = ref<IDataItem[]>([])
const cvsData = ref<IDataItem[]>([])
const QASplitData = ref<IDataItem[]>([])
const webData = ref<IDataItem[]>([])
const tabList = computed(() => {
    const list = reactive([
        {
            name: '文档导入',
            type: ImportTypeEnum.DOC,
            describe: '选择文本文件，直接将其按分段进行处理',

            show: true,
            data: docData
        },
        {
            name: '问答对导入',
            type: ImportTypeEnum.CVS,
            describe: '批量导入问答对，效果最佳',

            show: true,
            data: cvsData
        },
        {
            name: '自动拆分问答对',
            type: ImportTypeEnum.QASplit,
            describe: '选择文本文件，让大模型自动生成问答对',

            show: true,
            data: QASplitData
        },

        {
            name: '网页解析',
            type: ImportTypeEnum.WEB_PAGE,
            describe: '输入网页链接，快速导入内容',
            //   show: Number(props.type) === 1,
            show: true,
            data: webData
        }
    ])
    return list.filter(({ show }) => show)
})

//选择导入方式
const selectImportType = (type: number) => {
    isSelectType.value = type
}

//加载第一个导入方式
const seletFirst = () => {
    selectImportType(tabList.value[0].type)
}

const currentDate = computed(
    () => tabList.value.find(({ type }) => type === isSelectType.value)?.data!
)

const handleSubmit = async () => {
    const selectTab = tabList.value.find(
        ({ type }) => type === isSelectType.value
    )
    const { data, type } = selectTab
    console.log(data, type)

    const params = {
        kb_id: KBId.value,
        method: type,
        documents: data
    }
    await importData({ ...params })
    emit('close')
}
</script>

<style lang="scss" scoped>
.important-notice {
    background: linear-gradient(135deg, #fff8e6 0%, #fff3d6 100%);
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
    
    .notice-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .notice-icon {
            font-size: 18px;
            margin-right: 8px;
        }
        
        .notice-title {
            font-weight: 600;
            color: #e6a23c;
            font-size: 16px;
        }
    }
    
    .notice-content {
        color: #856404;
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
        padding-left: 26px;
    }
}

.isSelect {
    border: 1px solid var(--color-primary);
    color: var(--color-primary);
    background-color: var(--color-primary-light-9);
}
</style>
