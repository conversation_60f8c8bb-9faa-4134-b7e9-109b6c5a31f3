#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/untyped@1.5.2/node_modules/untyped/dist/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/untyped@1.5.2/node_modules/untyped/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/untyped@1.5.2/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/erkai999/pc/node_modules/.pnpm/untyped@1.5.2/node_modules/untyped/dist/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/untyped@1.5.2/node_modules/untyped/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/untyped@1.5.2/node_modules:/mnt/f/erkai999/pc/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../untyped/dist/cli.mjs" "$@"
else
  exec node  "$basedir/../untyped/dist/cli.mjs" "$@"
fi
